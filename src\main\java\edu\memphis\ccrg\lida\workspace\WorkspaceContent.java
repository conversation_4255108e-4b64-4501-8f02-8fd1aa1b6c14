/*******************************************************************************
 * Copyright (c) 2009, 2011 The University of Memphis.  All rights reserved. 
 * This program and the accompanying materials are made available 
 * under the terms of the LIDA Software Framework Non-Commercial License v1.0 
 * which accompanies this distribution, and is available at
 * http://ccrg.cs.memphis.edu/assets/papers/2010/LIDA-framework-non-commercial-v1.0.pdf
 *******************************************************************************/
package edu.memphis.ccrg.lida.workspace;

import edu.memphis.ccrg.lida.framework.shared.Link;
import edu.memphis.ccrg.lida.framework.shared.NodeStructure;

import java.util.List;
import java.util.Map;

/**
 * WorkspaceContent is a general name for the content of the workspace.  
 * Currently it is a NodeStructure only.  
 * In the future it may include Sensory Scene layers
 * 
 * <AUTHOR> J. McCall
 */
public interface WorkspaceContent extends NodeStructure {

}
