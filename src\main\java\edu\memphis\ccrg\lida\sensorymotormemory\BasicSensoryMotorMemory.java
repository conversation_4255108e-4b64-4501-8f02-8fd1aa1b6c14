/*******************************************************************************
 * Copyright (c) 2009, 2011 The University of Memphis.  All rights reserved. 
 * This program and the accompanying materials are made available 
 * under the terms of the LIDA Software Framework Non-Commercial License v1.0 
 * which accompanies this distribution, and is available at
 * http://ccrg.cs.memphis.edu/assets/papers/2010/LIDA-framework-non-commercial-v1.0.pdf
 *******************************************************************************/
package edu.memphis.ccrg.lida.sensorymotormemory;

import edu.memphis.ccrg.lida.actionselection.Action;
import edu.memphis.ccrg.lida.actionselection.ActionSelectionListener;
import edu.memphis.ccrg.lida.alifeagent.environment.operators.*;
import edu.memphis.ccrg.lida.environment.Environment;
import edu.memphis.ccrg.lida.framework.FrameworkModule;
import edu.memphis.ccrg.lida.framework.FrameworkModuleImpl;
import edu.memphis.ccrg.lida.framework.ModuleListener;
import edu.memphis.ccrg.lida.framework.initialization.AgentStarter;
import edu.memphis.ccrg.lida.framework.initialization.Initializable;
import edu.memphis.ccrg.lida.framework.tasks.TaskManager;
import edu.memphis.ccrg.lida.sensorymemory.SensoryMemoryListener;
import org.opennars.operator.misc.*;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.logging.Level;
import java.util.logging.Logger;

/**
 * Default implementation of a Map-based SensoryMotorMemory
 * 
 * <AUTHOR> J. McCall
 * 
 */
public class BasicSensoryMotorMemory extends FrameworkModuleImpl implements
		SensoryMotorMemory, SensoryMemoryListener, ActionSelectionListener {

	private static final Logger logger = Logger
			.getLogger(BasicSensoryMotorMemory.class.getCanonicalName());

	private static final int DEFAULT_BACKGROUND_TASK_TICKS = 1;
	protected int processActionTaskTicks;
	private List<SensoryMotorMemoryListener> listeners = new ArrayList<SensoryMotorMemoryListener>();
	protected Map<Number, Object> actionAlgorithmMap = new HashMap<Number, Object>();
	private Environment environment;

	/**
	 * Default constructor
	 */
	public BasicSensoryMotorMemory() {
	}

	/**
     * Will set parameters with the following names:<br/><br/>
     * 
     * <b>smm.processActionTaskTicks</b> delay (in ticks) on the processing of an Action receive from ActionSelection<br/>
     * 
     * @see Initializable
     */
	@Override
	public void init() {
		processActionTaskTicks = (Integer)getParam("smm.processActionTaskTicks", DEFAULT_BACKGROUND_TASK_TICKS);
	}
	
	@Override
	public void addListener(ModuleListener listener) {
		if (listener instanceof SensoryMotorMemoryListener) {
			addSensoryMotorMemoryListener((SensoryMotorMemoryListener) listener);
		} else {
			logger.log(Level.WARNING, "Cannot add listener {1}",
					new Object[]{TaskManager.getCurrentTick(),listener});
		}
	}

	@Override
	public void addSensoryMotorMemoryListener(SensoryMotorMemoryListener l) {
		listeners.add(l);
	}

	@Override
	public void setAssociatedModule(FrameworkModule module, String moduleUsage) {
		if (module instanceof Environment) {
			environment = (Environment) module;

			AgentStarter.nar.memory.addOperator(new Right(this,"^turnRight"));
			AgentStarter.nar.memory.addOperator(new Left(this,"^turnLeft"));
			AgentStarter.nar.memory.addOperator(new Around(this,"^turnAround"));
			AgentStarter.nar.memory.addOperator(new Eat(this,"^eat"));
			AgentStarter.nar.memory.addOperator(new Move(this,"^moveAgent"));
			AgentStarter.nar.memory.addOperator(new Flee(this,"^fleeAgent"));

			AgentStarter.nar.memory.addOperator(new Say("^say"));
			AgentStarter.nar.memory.addOperator(new CutStr("^strCut"));
			AgentStarter.nar.memory.addOperator(new Add0("^addd"));

			AgentStarter.nar.memory.addOperator(new SearchSB("^SearchSB"));
			AgentStarter.nar.memory.addOperator(new SearchSOS("^SearchSOS"));
			AgentStarter.nar.memory.addOperator(new SearchSOM("^search"));
			AgentStarter.nar.memory.addOperator(new SearchMOM("^SearchMOM"));
			AgentStarter.nar.memory.addOperator(new SearchMOS("^SearchMOS"));

			AgentStarter.nar.memory.addOperator(new Learn("^learn"));

		} else {
			logger.log(Level.WARNING, "Cannot add module {1}",
					new Object[]{TaskManager.getCurrentTick(),module});
		}
	}

	/**
	 * Adds an Algorithm to this {@link SensoryMotorMemory}
	 * @param actionId Id of {@link Action} which is implemented by the algorithm
	 * @param action an algorithm
	 */
	public void addActionAlgorithm(Number actionId, Object action) {
		actionAlgorithmMap.put(actionId, action);
	}

	@Override
	public synchronized void receiveAction(Action action) {
		if(action != null){
//			ProcessActionTask t = new ProcessActionTask(this, action);
//			taskSpawner.addTask(t);

//			WorkspaceBuffer buffer = (WorkspaceBuffer) ((WorkspaceImpl) AgentStarter.pam.getListener())
//					.getSubmodule(ModuleName.SeqGraph);
//			NodeStructure nsmem = buffer.getBufferContent(null);

			String nn = action.getName();
			if (nn.equals("get") || nn.equals("eat")){
				nn = "eat";
			}
			AgentStarter.nar.addInput("(^" + nn + ",{SELF})! :|:");
			AgentStarter.nar.cycles(10);

////			AgentStarter.actionId = 0;
		}else{
			logger.log(Level.WARNING, "Received null action", TaskManager.getCurrentTick());
		}
	}

	@Override
	public void sendActuatorCommand(Object command) {
		environment.processAction(command);
		for (SensoryMotorMemoryListener l : listeners) {
			l.receiveActuatorCommand(command);
		}
	}

	@Override
	public void decayModule(long ticks) {
		// module-specific decay code
	}

	@Override
	public Object getModuleContent(Object... params) {
		return null;
	}

	@Override
	public void receiveSensoryMemoryContent(Object content) {
		// Research problem
	}

}
