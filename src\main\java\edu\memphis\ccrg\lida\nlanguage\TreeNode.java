package edu.memphis.ccrg.lida.nlanguage;

import java.util.ArrayList;
import java.util.List;
import java.util.Optional;

public class TreeNode {
    Object value;
    List<TreeNode> children;
    // 别称、可替代、同义词、命名等。类似相似或对等词项。可多个，父子关系共享
    List<Object> alias;
    // 主题信息池，包括相关的概念、属性、时序等，供动机执行时检索，区分可能混杂的数据，参考程序执行，可能要垃圾回收
    // 人类只有一个共用的工作记忆，可能信息混淆，且容量有限，还要衰减，但计算机可有多个不同的工作记忆，属于优化处理
    List<Object> topicDataPool;
    private TreeNode parent;

    public TreeNode(Object value) {
        this.value = value;
        this.children = new ArrayList<>();
        this.alias = new ArrayList<>();
        this.topicDataPool = new ArrayList<>();
    }

    public boolean addChild(TreeNode child) {
        // 不能重复添加
        for (TreeNode node : children)
            if (node.equals(child)) return false;
        // 不能添加自己为子节点
        if (this == child) return false;
        // 不能添加父节点为子节点
        for (TreeNode node : children)
            if (node == child.parent) return false;
        // 不能添加循环引用
        for (TreeNode node : children)
            if (node.children.contains(node)) return false;

        children.add(child);
        child.parent = this;
        return true;
    }

    public Optional<TreeNode> findChildByValue(Object value) {
        return children.stream()
                .filter(node -> node.value.equals(value))
                .findFirst();
    }

    public void removeChild(TreeNode child) {
        children.remove(child);
    }

    // 添加别称
    public void addAlias(Object alias) {
        this.alias.add(alias);
    }

    // 移除别称
    public void removeAlias(Object alias) {
        this.alias.remove(alias);
    }

    // 检查是否包含特定的别称
    public boolean hasAlias(Object alias) {
        return this.alias.contains(alias);
    }

    // 获取别称列表
    public List<Object> getAliases() {
        return this.alias;
    }

    // 添加信息池数据
    public void addTopicData(Object data) {
        this.topicDataPool.add(data);
    }

    // 移除信息池数据
    public void removeTopicData(Object data) {
        this.topicDataPool.remove(data);
    }

    // 检查信息池是否包含特定数据
    public boolean hasTopicData(Object data) {
        return this.topicDataPool.contains(data);
    }

    // 获取信息池数据列表
    public List<Object> getTopicDataPool() {
        return this.topicDataPool;
    }

    // 修改节点值
    public void setValue(Object newValue) {
        this.value = newValue;
    }

    // 获取节点值
    public Object getValue() {
        return this.value;
    }

    public List<TreeNode> getChildren() {
        return this.children;
    }
}
