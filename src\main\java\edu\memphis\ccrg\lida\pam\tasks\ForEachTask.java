/*******************************************************************************
 * Copyright (c) 2009, 2011 The University of Memphis.  All rights reserved. 
 * This program and the accompanying materials are made available 
 * under the terms of the LIDA Software Framework Non-Commercial License v1.0 
 * which accompanies this distribution, and is available at
 * http://ccrg.cs.memphis.edu/assets/papers/2010/LIDA-framework-non-commercial-v1.0.pdf
 *******************************************************************************/
package edu.memphis.ccrg.lida.pam.tasks;

import edu.memphis.ccrg.lida.framework.shared.Link;
import edu.memphis.ccrg.lida.framework.shared.Node;
import edu.memphis.ccrg.lida.framework.shared.NodeStructure;
import edu.memphis.ccrg.lida.framework.tasks.FrameworkTaskImpl;
import edu.memphis.ccrg.lida.pam.PAMemory;
import edu.memphis.ccrg.lida.pam.PamLink;

import java.util.List;
import java.util.Map;

/**
 * A task to add a {@link PamLink} and its sink to the percept.
 * 
 * <AUTHOR> J. McCall
 * @see ExcitationTask creates this task
 * @see PropagationTask creates this task
 */
public class ForEachTask extends FrameworkTaskImpl {
	private String sinks;
	private PAMemory pam;
	private Link link;
	private Node sink;
	private Node source;
	private NodeStructure sceneNs;
	private NodeStructure goalNs;
	private NodeStructure seqNs;
	private NodeStructure yufaNs;
	private int elsesize = 0;
	private List<Link> elseLinks;
	private Map<Long, List<Link>> elsemap;
	private List<Link> cosclist;
	private String actStamp;
	/**
	 * Default constructor
	 * @param pam {@link PAMemory}
	 */
	public ForEachTask(Link link, PAMemory pam, NodeStructure seqNs, NodeStructure sceneNs, String actStamp) {
		super(1, "tact");
		this.link = link;
		this.pam = pam;
		this.sceneNs = sceneNs;
		this.seqNs = seqNs;
		this.actStamp = actStamp;
	}

	/**
	 * Default constructor
	 * @param link {@link PamLink}
	 * @param pam {@link PAMemory}
	 */
	public ForEachTask(Link link, PAMemory pam) {
		super(1, "tact");
		this.pam = pam;
		this.link = link;
	}

	/**
	 * Adds link's sink to the percept and tries to add the link as well then finishes.
	 */
	@Override
	protected void runThisFrameworkTask() {
		seqNs = pam.getWorkspaceBuffer("seq").getBufferContent(null);
		yufaNs = pam.getWorkspaceBuffer("yufa").getBufferContent(null);

		// 进入循环结构体内，需要存入上位时序，以便回溯，遍历也只需一条上位边？
		seqNs.getDoMainPath_map().get(actStamp).add(link);

		// todo 判断哪种循环，这里先实现do while。另外维护循环变量和层级，以便回溯

		// 循环体执行跟普通时序一致。do的部分
		pam.getActRoot(link, false, true, actStamp);

		// 循环体执行完，判断是否继续循环，while的部分
		// todo 不能直接在这里执行最后步骤，因为都是子线程，很难判断他们是否都执行完毕，
		//  所以需要在外面再加一个任务，来判断是否执行完毕？


//		String query = "match (m)-[r:循环条件]->(i) where id(m) = " + sink.getNodeId() + " return r";

		cancel();
	}
	
}

