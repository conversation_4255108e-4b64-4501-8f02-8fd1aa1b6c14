package edu.memphis.ccrg.lida.nlanguage;

import edu.memphis.ccrg.lida.framework.shared.Link;
import edu.memphis.ccrg.lida.framework.shared.NodeStructureImpl;
import edu.memphis.ccrg.linars.Term;

import java.util.ArrayList;
import java.util.Collection;

public class ChartTreeSet extends NodeStructureImpl{
    public TreeBag chartSet;

    public ChartTreeSet() {
        chartSet = new TreeBag(100,100,100);
    }

    public TreeBag getTreeBag() {
        return chartSet;
    }

    public Collection<Term> getLinksOfSinkT(String nodeName){
        Collection<Link> lls = getLinksOfSink(nodeName);
        Collection<Term> result = new ArrayList<Term>();
        for(Link l:lls){
            result.add((Term) l);
        }
        return result;
    }

    // 已激活完整但未匹配完整，都放linkableMap。匹配一半以上则完整激活
    // 可能会有多种完整匹配，全是单词项，或含嵌套词项，嵌套位置等也不一样，嵌套里还有嵌套

    // 按关系类型分类列表，快速找到，判断有无激活，能衰减。总子图和各子图都分别分类，按需增删改查衰减
    // 每个线程每个子图，每种关系分别对应不同处理
    // 每个线程先自由扩散，后各自处理，按需再扩散？
}
