/*******************************************************************************
 * Copyright (c) 2009, 2011 The University of Memphis.  All rights reserved. 
 * This program and the accompanying materials are made available 
 * under the terms of the LIDA Software Framework Non-Commercial License v1.0 
 * which accompanies this distribution, and is available at
 * http://ccrg.cs.memphis.edu/assets/papers/2010/LIDA-framework-non-commercial-v1.0.pdf
 *******************************************************************************/
package edu.memphis.ccrg.lida.pam.tasks;

import edu.memphis.ccrg.lida.data.NeoUtil;
import edu.memphis.ccrg.lida.framework.ModuleName;
import edu.memphis.ccrg.lida.framework.initialization.AgentStarter;
import edu.memphis.ccrg.lida.framework.shared.Link;
import edu.memphis.ccrg.lida.framework.shared.Node;
import edu.memphis.ccrg.lida.framework.shared.NodeStructure;
import edu.memphis.ccrg.lida.framework.tasks.FrameworkTaskImpl;
import edu.memphis.ccrg.lida.pam.*;

import java.util.HashMap;
import java.util.HashSet;
import java.util.Map;
import java.util.Set;

/**
 * A task to add a {@link PamLink} and its sink to the percept.
 * 
 * <AUTHOR> J. McCall
 * @see ExcitationTask creates this task
 * @see PropagationTask creates this task
 */
public class IsaPamTask extends FrameworkTaskImpl {

	private PAMemory pam;
	private Node sink;
	private Node pn;
	private static NodeStructure seqNs;
	private static PamImpl0.PamNodeStructure pamNodeStructure;

	/**
	 * Default constructor
	 * @param sink {@link PamLink}
	 * @param pam {@link PAMemory}
	 */
	public IsaPamTask(Node pn, Node sink, PAMemory pam, PamImpl0.PamNodeStructure pamNodeStructure,
					  NodeStructure seqNs, String type) {
		super(1, type);
		this.pam = pam;
		this.sink = sink;
		this.pn = pn;
		this.pamNodeStructure = pamNodeStructure;
		this.seqNs = seqNs;
	}

	/**
	 * Adds link's sink to the percept and tries to add the link as well then finishes.
	 */
	@Override
	protected void runThisFrameworkTask() {
		Set<Link> candidateLinks = new HashSet<Link>();

		candidateLinks = NeoUtil.getSomeLinks(sink, null, null, null, "变量");
		int index0 = 1;
		String type = "";
		Map<Integer, Node> varmap = new HashMap<>();
		// 复杂表达式，如8+8*8，同一个对象多次运用，自指+回指+环指，
		// 目前先按最简单的情况，变量边类型唯一，非同对象自指，可有typemap等map
		for (Link link : candidateLinks) {
			type = link.getCategory().getName();
			varmap.put(index0, link.getSource());
			pamNodeStructure.addNode(link.getSource(), "PamNodeImpl");
			index0++;
		}
		if (type.equals("")) return;

		index0 = 1;
		Node node;
		Set<Link> linkSet = NeoUtil.getSomeLinks(pn, type, null, null, null);
		for (Link link0 : linkSet) {
			node = varmap.get(index0);
			makeNowisa(link0.getSource(), node);
			index0++;
		}

		// 某些框架可能没有动作，如纯表达式
		Set<Link> linkSet1 = NeoUtil.getSomeLinks(sink, "动作", null, null, null);
		for (Link link1 : linkSet1) {

			pam.putMap((Node)link1.getSink(), link1.getSink().getTNname());

			// 就只加入无意识？
			pam.getListener().receivePercept((Node)link1.getSink(), ModuleName.NonGraph);
			pam.getListener().receivePercept(link1.getSource(), ModuleName.NonGraph);
			pam.getListener().receivePercept(link1, ModuleName.NonGraph);

			// todo 不再pam激活，直接加激活度即可，需要激活新链接，则直接查到激活
			// 实例化变量后，需要再激活一下原动作，原动作与当前实例可能有共同场景
//			pam.propagateActivationToParents(link1.getSource(), 1, "varscene");

			link1.getSource().setActivation(link1.getSource().getActivation() + 0.3);

			// 实例化对象本身也再激活一次，避免传递链过长衰减完
			// 如果没有动作，对象也不激活，多个动作=多次激活？多个对象，也多次激活
			for (Link link0 : linkSet) {
				// 就只加入无意识？
				pam.getListener().receivePercept((Node)link0.getSink(), ModuleName.NonGraph);
				pam.getListener().receivePercept(link0.getSource(), ModuleName.NonGraph);
				pam.getListener().receivePercept(link0, ModuleName.NonGraph);

//				pam.propagateActivationToParents(link0.getSource(), 1, "varscene");
				System.out.println(link0.toString() + "-- varscene_var---" + "--deep " + 1);

				link0.getSource().setActivation(link0.getSource().getActivation() + 0.3);
			}
			break;    // 默认只一个动作
		}

		cancel();
	}

	public static void makeNowisa(Node vartype, Node varnow) {
		pamNodeStructure = AgentStarter.pam.getPamNodeStructure();
		seqNs = AgentStarter.pam.getWorkspaceBuffer("seq").getBufferContent(null);
		synchronized (seqNs) {
			// pn -- isa --sink, vartype -- nowisa --> varnow, 变量类型指向具体值
			pamNodeStructure.addNode(varnow, "PamNodeImpl");
			pamNodeStructure.addNode(vartype, "PamNodeImpl");
			PamNode category = new PamNodeImpl();
			category.setNodeName("nowisa");

			if (vartype != null) {
				// 两个都是source，在前，以实际方向为准，而不是查询语句先后
				Link nowlink = pamNodeStructure.addDefaultLink(vartype, varnow,
						category, 1.0, 0.0);
				// todo 加入无意识buffer？
				for (Link ll : seqNs.getLinksOfSource(vartype.getTNname())) {//vartype.getNodeId()
					// 如果已有变量值，全部覆盖，以后面的为准
					seqNs.removeLink(ll);
				}
				AgentStarter.pam.getListener().receivePercept(nowlink, ModuleName.SeqGraph);
				AgentStarter.pam.getListener().receivePercept(nowlink.getSource(), ModuleName.SeqGraph);
				AgentStarter.pam.getListener().receivePercept((Node) nowlink.getSink(), ModuleName.SeqGraph);
			}
		}
	}
}

