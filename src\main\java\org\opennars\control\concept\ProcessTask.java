/* 
 * The MIT License
 *
 * Copyright 2018 The OpenNARS authors.
 *
 * Permission is hereby granted, free of charge, to any person obtaining a copy
 * of this software and associated documentation files (the "Software"), to deal
 * in the Software without restriction, including without limitation the rights
 * to use, copy, modify, merge, publish, distribute, sublicense, and/or sell
 * copies of the Software, and to permit persons to whom the Software is
 * furnished to do so, subject to the following conditions:
 *
 * The above copyright notice and this permission notice shall be included in
 * all copies or substantial portions of the Software.
 *
 * THE SOFTWARE IS PROVIDED "AS IS", WITHOUT WARRANTY OF ANY KIND, EXPRESS OR
 * IMPLIED, INCLUDING BUT NOT LIMITED TO THE WARRANTIES OF MERCHANTABILITY,
 * FITNESS FOR A PARTICULAR PURPOSE AND NONINFRINGEMENT. IN NO EVENT SHALL THE
 * AUTHORS OR COPYRIGHT HOLDERS BE LIABLE FOR ANY CLAIM, DAMAGES OR OTHER
 * LIABILITY, WHETHER IN AN ACTION OF CONTRACT, TORT OR OTHERWISE, ARISING FROM,
 * OUT OF OR IN CONNECTION WITH THE SOFTWARE OR THE USE OR OTHER DEALINGS IN
 * THE SOFTWARE.
 */
package org.opennars.control.concept;


import edu.memphis.ccrg.lida.nlanguage.SubGraphSet;
import edu.memphis.ccrg.lida.nlanguage.TreeNode;
import edu.memphis.ccrg.lida.nlanguage.Tree_nest;
import edu.memphis.ccrg.linars.*;
import org.opennars.control.DerivationContext;
import org.opennars.entity.*;
import org.opennars.interfaces.Timable;
import org.opennars.io.Symbols;
import org.opennars.language.*;

import java.util.Optional;

/**
 * Encapsulates the dispatching task processing
 *
 * <AUTHOR> Hammer
 *
 */
public class ProcessTask {
    /**
     * Directly process a new task within a concept.Here task can either be a judgement, goal, question or quest.The function is called exactly once on each task.Using
 local information and finishing in a constant time.
     * Also providing feedback 
 in the budget value of the task:
 de-priorize already fullfilled questions and goals
 increase quality of beliefs if they turned out to be useful.
 After the re-priorization is done, a tasklink is finally constructed.
 For input events the concept is set observable too. 
     *
     * @param concept The concept of the task
     * @param nal The derivation context
     * @param task The task to be processed
     * @param time The time
     * @return whether it was processed
     */
    // called in Memory.localInference only, for both derived and input tasks
    public static boolean processTask(final Concept concept, final DerivationContext nal, final Task task, Timable time, Memory mem) {
        synchronized(concept) { // 去掉会好多报错，它作用是：同一时间只能有一个线程访问concept
            concept.observable |= task.isInput();
            final char type = task.sentence.punctuation;
            switch (type) {
                case Symbols.JUDGMENT_MARK:
                    ProcessJudgment.processJudgment(concept, nal, task);
                    break;
                case Symbols.GOAL_MARK:
                    ProcessGoal.processGoal(concept, nal, task);
                    break;
                case Symbols.QUESTION_MARK:
                case Symbols.QUEST_MARK:
                    ProcessQuestion.processQuestion(concept, nal, task);
                    break;
                default:
                    return false;
            }

//            if(task.toString().contains("<ft_不执行 <->")){
//                System.out.println("addToTargetConceptsPreconditions ------ " + task.toString());
//            }

            //reduce priority by achievement:
            task.setPriority((float)(task.getPriority() * task.getAchievement()));
            //now process
            if (task.aboveThreshold()) {
                // still need to be processed
                TaskLink taskl = concept.linkToTask(task, nal);
                Term term = task.getTerm();
                // 顺承、相似、等价，多类潜在动机竞争。两种途径提升为动机，一是普通推理，二是这里直接拉来竞争
                if(task.sentence.isJudgment() && term instanceof Statement){
//                    Term subject = ((Statement)term).getSubject();
//                    Term predicate = ((Statement)term).getPredicate();
//                    if(predicate instanceof CompoundTerm && subject instanceof CompoundTerm && predicate.hasVar() && subject.hasVar()){
//                        System.out.println("addToTargetConceptsPreconditions CompoundTerm----" + task.toString());
//                    }
                    if(ProcessJudgment.isBeliefsHypothesis(task, nal)) { //isExecutableHypothesis
                        //after linkToTask。because now the components are there
                        // 在linkToTask之后，因为现在组件都在那里
                        ProcessJudgment.addToTargetConceptsPreconditions(task, mem);
//                    }else if (ProcessJudgment.isBeliefsHypothesis(task,nal)){
                        // 不是可执行，但可以是状态
//                        ProcessJudgment.addToTargetConceptsBeliefs(task, nal);
                    }

                    // 如果不是继承或操作，也就是相似、对等、顺承等，则直接拉到潜在动机
//                    if(!(term instanceof Inheritance)){
//                        addTaskToConceptMotivation(task, concept, nal);
//                    }
                }
                // 基于已知于概念邻居的信念，触发预测性推理
                ProcessAnticipation.firePredictions(task, concept, nal, time, taskl);
            }
        }
        return true;
    }

    private static void addTaskToConceptMotivation(Task task, Concept concept, DerivationContext nal) {
        Memory memory = nal.memory;
        if (memory instanceof SubGraphSet){
            SubGraphSet subgraphset = (SubGraphSet) memory;
            Tree_nest goalTree = subgraphset.goalTree;
            if(goalTree != null){
                // 找到目标树中包含的节点，有则加入，先取出task三元组的头尾节点
                Term pred = ((Statement)task.getTerm()).getPredicate();
                Term subj = ((Statement)task.getTerm()).getSubject();
                Optional<TreeNode> node = goalTree.findNode(pred);
                // todo 有问题，目前是顺推，需从下往上构建，从本能开始则是从上往下，可能需要两颗树同时构建？
                // 双向同时构建，像最短路算法，且保留中间结果，避免重复计算，只是有些累赘，中间路径数据结构完整
                // 直接用最短路算法，自定义遍历逻辑，只取到中间路径的id，而非完整数据结构，快捷而且节省空间

                // nars的表已经构建了顺推树结构，可考虑构建一个相对稳定的从本能出发的逆推树结构，两者交互加速构建
                // todo 更重要的是目标树=已确定要执行的动机树，可包含短中长目标。备选表只是目前激活=与目前任务相关
                // 动机树和备选树分开，更清晰轻便，精简动机不用被大量备选干扰，要搜索备选，就去备选树搜即可，也即概念和时序表
                if(node.isPresent()){
                    TreeNode node1 = node.get();
                    Optional<TreeNode> node2 = goalTree.findNode(subj);
                    if(!node2.isPresent()){
                        Term term = task.getTerm();
                        // 不存在则加入节点，并添加到目标树中
                        // 假如task是相似、对等，则加入节点的alias属性，否则加入子节点
                        if(term instanceof Similarity || term instanceof Equivalence){
                            node1.addAlias(new TreeNode(subj));
                        }else{
                            node1.addChild(new TreeNode(subj));
                        }
                    }
                }
            }
        }
    }
}
