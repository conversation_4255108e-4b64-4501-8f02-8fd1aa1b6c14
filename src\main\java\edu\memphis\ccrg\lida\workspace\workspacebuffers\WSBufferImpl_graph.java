/*******************************************************************************
 * Copyright (c) 2009, 2011 The University of Memphis.  All rights reserved. 
 * This program and the accompanying materials are made available 
 * under the terms of the LIDA Software Framework Non-Commercial License v1.0 
 * which accompanies this distribution, and is available at
 * http://ccrg.cs.memphis.edu/assets/papers/2010/LIDA-framework-non-commercial-v1.0.pdf
 *******************************************************************************/
package edu.memphis.ccrg.lida.workspace.workspacebuffers;

import java.util.Map;

import edu.memphis.ccrg.lida.framework.FrameworkModuleImpl;
import edu.memphis.ccrg.lida.framework.shared.ExtendedId;
import edu.memphis.ccrg.lida.framework.shared.Linkable;
import edu.memphis.ccrg.lida.framework.shared.NodeStructure;
import edu.memphis.ccrg.lida.framework.shared.UnmodifiableNodeStructureImpl;
import edu.memphis.ccrg.lida.nlanguage.SubGraphSet;
import edu.memphis.ccrg.lida.workspace.WorkspaceContent;

/**
 * This class implements module of WorkspaceBuffer. WorkspaceBuffer is a submodule of workspace and 
 * it contains nodeStructures. Also this class maintains activation lower bound of its nodeStructures.
 * {@link WorkspaceBuffer} implementation. Uses a single NodeStructure for the content.
 * WorkspaceBuffer是工作区的子模块，它包含nodeStructures。 另外这个类维护激活下界，nodeStructures的
 * <AUTHOR> J. McCall
 */
public class WSBufferImpl_graph extends FrameworkModuleImpl implements	WorkspaceBuffer {

//	private NodeStructure buffer = new NodeStructureImpl();
	private NodeStructure buffer = new SubGraphSet();// 一步到位，向下兼容树和原ns
	
	/**
	 * Note that this method <i>merges</i> the specified content into the
	 * buffer. Since {@link NodeStructure} copies all added {@link Linkable} objects, the
	 * resultant content inside the buffer consists of different Java objects
	 * than those supplied in the argument. The {@link ExtendedId} of the new
	 * {@link Linkable} are still the same as the originals.
	 */
	@Override
	public void addBufferContent(WorkspaceContent content) {
		buffer.mergeWith(content);
	}

	@Override
	public WorkspaceContent getBufferContent(Map<String, Object> params) {
		return (WorkspaceContent) buffer;
	}
	
	@Override
	public void decayModule(long t) {
//		if(this.getModuleName().name.equals("SceneGraph") && buffer.getLinkables().size() > 0){
//			System.out.println("SceneGraph 衰减--目前数量" + buffer.getLinkables().size());
//		}
		buffer.decayNodeStructure(t);
	}

	@Override
	public Object getModuleContent(Object... params) {
		return new UnmodifiableNodeStructureImpl(buffer);
	}
	
}
