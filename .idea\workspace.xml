<?xml version="1.0" encoding="UTF-8"?>
<project version="4">
  <component name="AutoImportSettings">
    <option name="autoReloadType" value="SELECTIVE" />
  </component>
  <component name="ChangeListManager">
    <list default="true" id="a1ec1d63-8b2a-4e80-bad8-90ba8f8f21ec" name="变更" comment="加法图式结构-导入图谱形式-细化">
      <change afterPath="$PROJECT_DIR$/.cursor/rules/project-overview.mdc" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/.cursor/rules/term-links.mdc" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/.cursor/rules/terms-processing.mdc" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/.cursor/rules/项目理论架构概述与总优化方案.mdc" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/src/main/java/edu/memphis/ccrg/lida/pam/tasks/LearnTask.java" beforeDir="false" afterPath="$PROJECT_DIR$/src/main/java/edu/memphis/ccrg/lida/pam/tasks/LearnTask.java" afterDir="false" />
    </list>
    <list id="ef9d0f92-054e-4d42-8946-81a2e19df511" name="log" comment="">
      <change beforePath="$PROJECT_DIR$/hs_err_pid11324.log" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/hs_err_pid5712.log" beforeDir="false" />
    </list>
    <option name="SHOW_DIALOG" value="false" />
    <option name="HIGHLIGHT_CONFLICTS" value="true" />
    <option name="HIGHLIGHT_NON_ACTIVE_CHANGELIST" value="false" />
    <option name="LAST_RESOLUTION" value="IGNORE" />
  </component>
  <component name="FileTemplateManagerImpl">
    <option name="RECENT_TEMPLATES">
      <list>
        <option value="Class" />
      </list>
    </option>
  </component>
  <component name="Git.Rebase.Settings">
    <option name="NEW_BASE" value="nars312" />
  </component>
  <component name="Git.Settings">
    <option name="RECENT_BRANCH_BY_REPOSITORY">
      <map>
        <entry key="$PROJECT_DIR$" value="master" />
      </map>
    </option>
    <option name="RECENT_GIT_ROOT_PATH" value="$PROJECT_DIR$" />
  </component>
  <component name="HighlightingSettingsPerFile">
    <setting file="file://$PROJECT_DIR$/src/main/java/edu/memphis/ccrg/lida/alifeagent/environment/ALifeEnvironment.java" root0="FORCE_HIGHLIGHTING" />
    <setting file="file://$PROJECT_DIR$/src/main/java/edu/memphis/ccrg/lida/proceduralmemory/ProceduralMemoryImpl.java" root0="FORCE_HIGHLIGHTING" />
    <setting file="file://$PROJECT_DIR$/pom.xml" root0="FORCE_HIGHLIGHTING" />
  </component>
  <component name="InvalidFacetManager">
    <ignored-facets>
      <facet id="neo-lida/invalid/Spring" />
    </ignored-facets>
  </component>
  <component name="MarkdownSettingsMigration">
    <option name="stateVersion" value="1" />
  </component>
  <component name="MavenImportPreferences">
    <option name="generalSettings">
      <MavenGeneralSettings>
        <option name="localRepository" value="D:\.m2\repository" />
        <option name="useMavenConfig" value="false" />
        <option name="userSettingsFile" value="C:\Users\<USER>\.m2\settings.xml" />
      </MavenGeneralSettings>
    </option>
    <option name="importingSettings">
      <MavenImportingSettings>
        <option name="jdkForImporter" value="#JAVA_HOME" />
      </MavenImportingSettings>
    </option>
  </component>
  <component name="ProblemsViewState">
    <option name="selectedTabId" value="CurrentFile" />
  </component>
  <component name="ProjectCodeStyleSettingsMigration">
    <option name="version" value="2" />
  </component>
  <component name="ProjectColorInfo">{
  &quot;associatedIndex&quot;: 5
}</component>
  <component name="ProjectId" id="2MWy7r12u2q9n0U4DM3PuyUpkM3" />
  <component name="ProjectLevelVcsManager">
    <ConfirmationsSetting value="2" id="Add" />
  </component>
  <component name="ProjectViewState">
    <option name="autoscrollFromSource" value="true" />
    <option name="autoscrollToSource" value="true" />
    <option name="hideEmptyMiddlePackages" value="true" />
    <option name="openDirectoriesWithSingleClick" value="true" />
    <option name="showLibraryContents" value="true" />
    <option name="showVisibilityIcons" value="true" />
  </component>
  <component name="PropertiesComponent"><![CDATA[{
  "keyToString": {
    "ASKED_ADD_EXTERNAL_FILES": "true",
    "Notification.DisplayName-DoNotAsk-DatabaseConfigFileWatcher.found": "找到数据库连接形参",
    "Notification.DoNotAsk-DatabaseConfigFileWatcher.found": "true",
    "RequestMappingsPanelOrder0": "0",
    "RequestMappingsPanelOrder1": "1",
    "RequestMappingsPanelWidth0": "75",
    "RequestMappingsPanelWidth1": "75",
    "RunOnceActivity.OpenProjectViewOnStart": "true",
    "RunOnceActivity.ShowReadmeOnStart": "true",
    "RunOnceActivity.git.unshallow": "true",
    "SHARE_PROJECT_CONFIGURATION_FILES": "true",
    "Spring Boot.linars.executor": "Debug",
    "WebServerToolWindowFactoryState": "false",
    "codeReviewSummary": "[]",
    "git-widget-placeholder": "master",
    "ignore.virus.scanning.warn.message": "true",
    "last_directory_selection": "D:/lida/neo-lida-nars310-xr/src/test/java",
    "last_opened_file_path": "D:/lida/neo-lida-nars310-xr",
    "node.js.detected.package.eslint": "true",
    "node.js.detected.package.tslint": "true",
    "node.js.selected.package.eslint": "(autodetect)",
    "node.js.selected.package.tslint": "(autodetect)",
    "nodejs_package_manager_path": "npm",
    "project.structure.last.edited": "全局库",
    "project.structure.proportion": "0.15",
    "project.structure.side.proportion": "0.4213564",
    "run.code.analysis.last.selected.profile": "pProject Default",
    "settings.editor.selected.configurable": "preferences.pluginManager",
    "spring.configuration.checksum": "10ba6e9d12eb13e9b56a5e538c103803",
    "two.files.diff.last.used.file": "D:/downcode/opennars-3.1.0/src/main/java/org/opennars/language/CompoundTerm.java",
    "vue.rearranger.settings.migration": "true"
  },
  "keyToStringList": {
    "ChangesTree.GroupingKeys": [
      "directory"
    ]
  }
}]]></component>
  <component name="ReactorSettings">
    <option name="notificationShown" value="true" />
  </component>
  <component name="RecentsManager">
    <key name="CreateClassDialog.RecentsKey">
      <recent name="edu.memphis.ccrg.lida.framework.shared.scene" />
    </key>
    <key name="CopyFile.RECENT_KEYS">
      <recent name="D:\lida\neo-lida-nars310-xr" />
      <recent name="D:\lida\neo-lida-nars310-xr\src\test\resources" />
      <recent name="D:\lida\neo-lida-nars310-xr\src\main\resources\static\static" />
      <recent name="D:\lida\neo-lida-nars310-xr\src\main\java" />
      <recent name="D:\lida\neo-lida-nars310-xr\src\main\java\edu\memphis\ccrg\alife" />
    </key>
    <key name="MoveFile.RECENT_KEYS">
      <recent name="D:\lida\neo-lida-nars310-xr\src\test\java" />
      <recent name="D:\lida\neo-lida-nars310-xr\一些杂项" />
      <recent name="D:\lida\neo-lida-nars310-xr\理论架构" />
      <recent name="D:\lida\neo-lida-nars310-xr" />
      <recent name="D:\lida\neo-lida-nars310-xr\src\main\java\logging" />
    </key>
    <key name="MoveClassesOrPackagesDialog.RECENTS_KEY">
      <recent name="" />
      <recent name="edu.memphis.ccrg.lida" />
    </key>
    <key name="MoveInnerDialog.RECENTS_KEY">
      <recent name="edu.memphis.ccrg.alife.gui" />
    </key>
    <key name="CopyClassDialog.RECENTS_KEY">
      <recent name="edu.memphis.ccrg.lida.pam" />
      <recent name="org.opennars.operator.misc" />
      <recent name="" />
      <recent name="edu.memphis.ccrg.lida.pam.tasks" />
      <recent name="edu.memphis.ccrg.lida.language" />
    </key>
  </component>
  <component name="RunManager" selected="Spring Boot.linars">
    <configuration name="TreeConverter" type="Application" factoryName="Application" temporary="true" nameIsGenerated="true">
      <option name="MAIN_CLASS_NAME" value="some.TreeConverter" />
      <module name="neo-lida" />
      <method v="2">
        <option name="Make" enabled="true" />
      </method>
    </configuration>
    <configuration name="csv2neo" type="Application" factoryName="Application" temporary="true" nameIsGenerated="true">
      <option name="MAIN_CLASS_NAME" value="some.csv2neo" />
      <module name="neo-lida" />
      <method v="2">
        <option name="Make" enabled="true" />
      </method>
    </configuration>
    <configuration default="true" type="Babashka" factoryName="BabashkaLocalRepl" activateToolWindowBeforeRun="false">
      <setting name="displayName" value="" />
      <setting name="bbPath" value="" />
      <setting name="parameters" value="" />
      <option name="PARENT_ENVS" value="true" />
      <setting name="workingDir" value="" />
      <setting name="focusEditor" value="false" />
      <method v="2">
        <option name="Make" enabled="true" />
      </method>
    </configuration>
    <configuration default="true" type="ClojureREPL" factoryName="Local" activateToolWindowBeforeRun="false">
      <option name="configVersion" value="1" />
      <option name="options" />
      <option name="profiles" />
      <method v="2" />
    </configuration>
    <configuration default="true" type="ClojureREPL" factoryName="Remote" activateToolWindowBeforeRun="false">
      <setting name="displayName" value="" />
      <setting name="host" value="" />
      <setting name="port" value="0" />
      <setting name="replType" value="SOCKET" />
      <setting name="configType" value="SPECIFY" />
      <setting name="replPortFileType" value="STANDARD" />
      <setting name="customPortFile" value="" />
      <setting name="fixLineNumbers" value="false" />
      <setting name="focusEditor" value="false" />
      <setting name="urlFile" value="" />
      <method v="2" />
    </configuration>
    <configuration name="Lida2narseseTest.transTotal0" type="JUnit" factoryName="JUnit" temporary="true" nameIsGenerated="true">
      <module name="neo-lida" />
      <extension name="coverage">
        <pattern>
          <option name="PATTERN" value="edu.memphis.ccrg.lida.episodicmemory.neo.*" />
          <option name="ENABLED" value="true" />
        </pattern>
      </extension>
      <option name="PACKAGE_NAME" value="edu.memphis.ccrg.lida.episodicmemory.neo" />
      <option name="MAIN_CLASS_NAME" value="edu.memphis.ccrg.lida.episodicmemory.neo.Lida2narseseTest" />
      <option name="METHOD_NAME" value="transTotal0" />
      <option name="TEST_OBJECT" value="method" />
      <envs>
        <env name="--add-opens java.base/java.lang" value="ALL-UNNAMED " />
      </envs>
      <method v="2">
        <option name="Make" enabled="true" />
      </method>
    </configuration>
    <configuration default="true" type="SpringBootApplicationConfigurationType" factoryName="Spring Boot">
      <option name="SHORTEN_COMMAND_LINE" value="NONE" />
      <method v="2">
        <option name="Make" enabled="true" />
      </method>
    </configuration>
    <configuration name="linars" type="SpringBootApplicationConfigurationType" factoryName="Spring Boot">
      <option name="ALTERNATIVE_JRE_PATH" value="corretto-11" />
      <option name="FRAME_DEACTIVATION_UPDATE_POLICY" value="UpdateClassesAndResources" />
      <module name="neo-lida" />
      <option name="SHORTEN_COMMAND_LINE" value="MANIFEST" />
      <option name="SPRING_BOOT_MAIN_CLASS" value="edu.memphis.ccrg.lida.alifeagent.Run0" />
      <option name="UPDATE_ACTION_UPDATE_POLICY" value="UpdateClassesAndResources" />
      <method v="2">
        <option name="Make" enabled="true" />
      </method>
    </configuration>
    <list>
      <item itemvalue="JUnit.Lida2narseseTest.transTotal0" />
      <item itemvalue="Spring Boot.linars" />
      <item itemvalue="应用程序.TreeConverter" />
      <item itemvalue="应用程序.csv2neo" />
    </list>
    <recent_temporary>
      <list>
        <item itemvalue="JUnit.Lida2narseseTest.transTotal0" />
        <item itemvalue="应用程序.csv2neo" />
        <item itemvalue="应用程序.TreeConverter" />
      </list>
    </recent_temporary>
  </component>
  <component name="SharedIndexes">
    <attachedChunks>
      <set>
        <option value="bundled-jdk-9823dce3aa75-fbdcb00ec9e3-intellij.indexing.shared.core-IU-251.23774.435" />
        <option value="bundled-js-predefined-d6986cc7102b-f27c65a3e318-JavaScript-IU-251.23774.435" />
      </set>
    </attachedChunks>
  </component>
  <component name="SpellCheckerSettings" RuntimeDictionaries="0" Folders="0" CustomDictionaries="0" DefaultDictionary="应用程序级" UseSingleDictionary="true" transferred="true" />
  <component name="TaskManager">
    <task active="true" id="Default" summary="默认任务">
      <changelist id="a1ec1d63-8b2a-4e80-bad8-90ba8f8f21ec" name="变更" comment="" />
      <created>1677897468929</created>
      <option name="number" value="Default" />
      <option name="presentableId" value="Default" />
      <updated>1677897468929</updated>
      <workItem from="1677897470645" duration="32000" />
      <workItem from="1678502743319" duration="9124000" />
      <workItem from="1678680877239" duration="2191000" />
      <workItem from="1678728108125" duration="297000" />
      <workItem from="1678728430584" duration="1142000" />
      <workItem from="1678844044274" duration="1199000" />
      <workItem from="1679213942978" duration="2661000" />
      <workItem from="1679219703477" duration="857000" />
      <workItem from="1679220591424" duration="177000" />
      <workItem from="1679220793860" duration="259000" />
      <workItem from="1679221085684" duration="189000" />
      <workItem from="1679221294796" duration="2336000" />
      <workItem from="1679284053615" duration="2449000" />
      <workItem from="1679395578198" duration="2630000" />
      <workItem from="1680066863138" duration="97000" />
      <workItem from="1680068475709" duration="661000" />
      <workItem from="1680070827298" duration="303000" />
      <workItem from="1680071146864" duration="222000" />
      <workItem from="1680085924384" duration="941000" />
      <workItem from="1680316205636" duration="2692000" />
      <workItem from="1680686663316" duration="1062000" />
      <workItem from="1680754272223" duration="4287000" />
      <workItem from="1680855499933" duration="602000" />
      <workItem from="1680922953167" duration="1466000" />
      <workItem from="1680926586527" duration="4272000" />
      <workItem from="1681007002630" duration="4780000" />
      <workItem from="1681015571982" duration="6492000" />
      <workItem from="1681125152671" duration="739000" />
      <workItem from="1681263670100" duration="3253000" />
      <workItem from="1681360859770" duration="599000" />
      <workItem from="1681476770286" duration="482000" />
      <workItem from="1681477261940" duration="35000" />
      <workItem from="1681477306527" duration="144000" />
      <workItem from="1681477474799" duration="2708000" />
      <workItem from="1681544120295" duration="599000" />
      <workItem from="1681629130241" duration="2045000" />
      <workItem from="1681707092380" duration="4512000" />
      <workItem from="1681787360147" duration="1303000" />
      <workItem from="1681962955436" duration="599000" />
      <workItem from="1681966409692" duration="4757000" />
      <workItem from="1682139703094" duration="678000" />
      <workItem from="1682147813576" duration="3272000" />
      <workItem from="1682301058506" duration="2402000" />
      <workItem from="1682396061131" duration="2657000" />
      <workItem from="1682476782935" duration="2596000" />
      <workItem from="1682559970578" duration="8602000" />
      <workItem from="1682647466601" duration="672000" />
      <workItem from="1682649011970" duration="9357000" />
      <workItem from="1682731050371" duration="4800000" />
      <workItem from="1682818189434" duration="599000" />
      <workItem from="1682910341677" duration="1807000" />
      <workItem from="1682997829209" duration="1587000" />
      <workItem from="1683082864012" duration="2566000" />
      <workItem from="1683167947940" duration="599000" />
      <workItem from="1683269928745" duration="599000" />
      <workItem from="1683348898092" duration="1199000" />
      <workItem from="1683425022201" duration="877000" />
      <workItem from="1683461593738" duration="2978000" />
      <workItem from="1683510046413" duration="163000" />
      <workItem from="1683510219693" duration="21268000" />
      <workItem from="1683597064484" duration="16541000" />
      <workItem from="1683683110668" duration="15033000" />
      <workItem from="1683770167795" duration="2118000" />
      <workItem from="1683854709139" duration="11681000" />
      <workItem from="1683949456249" duration="612000" />
      <workItem from="1683956479589" duration="2173000" />
      <workItem from="1684046667875" duration="6764000" />
      <workItem from="1684118170116" duration="11610000" />
      <workItem from="1684201537989" duration="5555000" />
      <workItem from="1684322528227" duration="6868000" />
      <workItem from="1684373056617" duration="7057000" />
      <workItem from="1684409928437" duration="2959000" />
      <workItem from="1684428678953" duration="5085000" />
      <workItem from="1684455836213" duration="6189000" />
      <workItem from="1684542499991" duration="4682000" />
      <workItem from="1684645468173" duration="5078000" />
      <workItem from="1684729857486" duration="3055000" />
      <workItem from="1685423409629" duration="599000" />
      <workItem from="1685523847642" duration="598000" />
      <workItem from="1685589298090" duration="599000" />
      <workItem from="1685766247049" duration="678000" />
      <workItem from="1685810571621" duration="905000" />
      <workItem from="1685843591166" duration="5992000" />
      <workItem from="1685923035173" duration="18591000" />
      <workItem from="1686046795992" duration="2485000" />
      <workItem from="1686108490469" duration="17961000" />
      <workItem from="1686183142207" duration="13503000" />
      <workItem from="1686280265914" duration="785000" />
      <workItem from="1686386707445" duration="3542000" />
      <workItem from="1686447665737" duration="9073000" />
      <workItem from="1686559239131" duration="780000" />
      <workItem from="1686612951370" duration="24857000" />
      <workItem from="1686706994581" duration="7128000" />
      <workItem from="1686732575269" duration="15264000" />
      <workItem from="1686809092721" duration="3082000" />
      <workItem from="1686883253425" duration="22157000" />
      <workItem from="1686916072936" duration="556000" />
      <workItem from="1686916639656" duration="11147000" />
      <workItem from="1686972994663" duration="2387000" />
      <workItem from="1687148736085" duration="22125000" />
      <workItem from="1687226883501" duration="3092000" />
      <workItem from="1687312965694" duration="9912000" />
      <workItem from="1687398355740" duration="599000" />
      <workItem from="1687486982823" duration="608000" />
      <workItem from="1687571937653" duration="1328000" />
      <workItem from="1687609374944" duration="2512000" />
      <workItem from="1687750526099" duration="2982000" />
      <workItem from="1687832152261" duration="2546000" />
      <workItem from="1687908579297" duration="4383000" />
      <workItem from="1687994698190" duration="4590000" />
      <workItem from="1688089858954" duration="15104000" />
      <workItem from="1688168236140" duration="1198000" />
      <workItem from="1688366043164" duration="2767000" />
      <workItem from="1688432279969" duration="141000" />
      <workItem from="1688432433913" duration="9925000" />
      <workItem from="1688518887966" duration="23758000" />
      <workItem from="1688599987052" duration="24357000" />
      <workItem from="1688720458385" duration="2300000" />
      <workItem from="1688725839351" duration="652000" />
      <workItem from="1688779152079" duration="4228000" />
      <workItem from="1688869779883" duration="26971000" />
      <workItem from="1688948526618" duration="1326000" />
      <workItem from="1689030688281" duration="1224000" />
      <workItem from="1689235118586" duration="2029000" />
      <workItem from="1689293605626" duration="4886000" />
      <workItem from="1689383424821" duration="2840000" />
      <workItem from="1689465075230" duration="3234000" />
      <workItem from="1689552289858" duration="1375000" />
      <workItem from="1689731022061" duration="20009000" />
      <workItem from="1689811390303" duration="2291000" />
      <workItem from="1689824040476" duration="35995000" />
      <workItem from="1696995554195" duration="453000" />
      <workItem from="1696996014515" duration="638000" />
      <workItem from="1697068670216" duration="614000" />
      <workItem from="1697161622944" duration="602000" />
      <workItem from="1697797383297" duration="2984000" />
      <workItem from="1697855576781" duration="1204000" />
      <workItem from="1698042718735" duration="714000" />
      <workItem from="1698056644951" duration="914000" />
      <workItem from="1698107143447" duration="742000" />
      <workItem from="1698114244294" duration="1312000" />
      <workItem from="1698136806534" duration="659000" />
      <workItem from="1698209162593" duration="703000" />
      <workItem from="1698210891399" duration="716000" />
      <workItem from="1698289149326" duration="776000" />
      <workItem from="1698329259872" duration="79000" />
      <workItem from="1698329353324" duration="58000" />
      <workItem from="1698329420978" duration="118000" />
      <workItem from="1698329552124" duration="747000" />
      <workItem from="1698383761657" duration="2668000" />
      <workItem from="1698487990176" duration="4421000" />
      <workItem from="1698498888868" duration="3958000" />
      <workItem from="1698555335702" duration="895000" />
      <workItem from="1698556312647" duration="706000" />
      <workItem from="1698557283113" duration="13804000" />
      <workItem from="1698630269985" duration="13791000" />
      <workItem from="1698707650634" duration="8471000" />
      <workItem from="1698795895396" duration="5779000" />
      <workItem from="1699314416323" duration="6899000" />
      <workItem from="1699402015918" duration="2115000" />
      <workItem from="1699513087695" duration="1751000" />
      <workItem from="1699583913754" duration="36326000" />
      <workItem from="1699661234545" duration="10195000" />
      <workItem from="1699841894992" duration="36119000" />
      <workItem from="1699931806896" duration="27847000" />
      <workItem from="1700011160330" duration="9016000" />
      <workItem from="1700095334492" duration="9406000" />
      <workItem from="1700187775298" duration="20242000" />
      <workItem from="1700226609583" duration="7781000" />
      <workItem from="1700268260872" duration="25555000" />
      <workItem from="1700370866682" duration="9311000" />
      <workItem from="1700399460174" duration="7698000" />
      <workItem from="1700445263654" duration="22779000" />
      <workItem from="1700531786458" duration="3409000" />
      <workItem from="1700640648767" duration="30315000" />
      <workItem from="1700700247459" duration="52964000" />
      <workItem from="1700794283839" duration="27885000" />
      <workItem from="1700898312691" duration="686000" />
      <workItem from="1700969602406" duration="25469000" />
      <workItem from="1701053128862" duration="28610000" />
      <workItem from="1701135265423" duration="14920000" />
      <workItem from="1701156921047" duration="20469000" />
      <workItem from="1701215824011" duration="27487000" />
      <workItem from="1701308239282" duration="21326000" />
      <workItem from="1701394190885" duration="11074000" />
      <workItem from="1701579273435" duration="21734000" />
      <workItem from="1701654411306" duration="3617000" />
      <workItem from="1701737242313" duration="32895000" />
      <workItem from="1701827389817" duration="35552000" />
      <workItem from="1701918713927" duration="21888000" />
      <workItem from="1702000410938" duration="18326000" />
      <workItem from="1702089846122" duration="32152000" />
      <workItem from="1702176634260" duration="13580000" />
      <workItem from="1702258755543" duration="26188000" />
      <workItem from="1702341772488" duration="41810000" />
      <workItem from="1702435533745" duration="42405000" />
      <workItem from="1702513306862" duration="5200000" />
      <workItem from="1702633741846" duration="599000" />
      <workItem from="1702698540442" duration="13088000" />
      <workItem from="1702779336069" duration="14796000" />
      <workItem from="1702976785373" duration="10432000" />
      <workItem from="1703035034074" duration="1842000" />
      <workItem from="1703036889419" duration="35540000" />
      <workItem from="1703125144709" duration="20099000" />
      <workItem from="1703218905072" duration="10714000" />
      <workItem from="1703304077831" duration="1834000" />
      <workItem from="1703389841003" duration="8657000" />
      <workItem from="1703477805969" duration="599000" />
      <workItem from="1703551900114" duration="21305000" />
      <workItem from="1703671417260" duration="11550000" />
      <workItem from="1703730896148" duration="7218000" />
      <workItem from="1703766036601" duration="268000" />
      <workItem from="1703766323418" duration="1592000" />
      <workItem from="1703771756113" duration="2658000" />
      <workItem from="1703908695833" duration="4640000" />
      <workItem from="1703994184308" duration="1941000" />
      <workItem from="1704077602015" duration="27959000" />
      <workItem from="1704130284220" duration="337000" />
      <workItem from="1704130642964" duration="434000" />
      <workItem from="1704131089837" duration="1374000" />
      <workItem from="1704165894453" duration="1363000" />
      <workItem from="1704285538207" duration="7686000" />
      <workItem from="1704339796481" duration="7048000" />
      <workItem from="1704420410176" duration="22482000" />
      <workItem from="1704507781975" duration="15747000" />
      <workItem from="1704587679941" duration="20189000" />
      <workItem from="1704673744876" duration="10735000" />
      <workItem from="1704780299326" duration="599000" />
      <workItem from="1704858487558" duration="13360000" />
      <workItem from="1704938965241" duration="14941000" />
      <workItem from="1705026712849" duration="8235000" />
      <workItem from="1705111255832" duration="24522000" />
      <workItem from="1705195538512" duration="12016000" />
      <workItem from="1705283682844" duration="11420000" />
      <workItem from="1705484416838" duration="599000" />
      <workItem from="1705675409861" duration="404000" />
      <workItem from="1706612419236" duration="1994000" />
      <workItem from="1706782345557" duration="599000" />
      <workItem from="1707026475129" duration="6957000" />
      <workItem from="1707044031378" duration="89000" />
      <workItem from="1707044212199" duration="168000" />
      <workItem from="1707044389593" duration="951000" />
      <workItem from="1707127957931" duration="9430000" />
      <workItem from="1708580949329" duration="545000" />
      <workItem from="1708581692927" duration="592000" />
      <workItem from="1709831427983" duration="21000" />
      <workItem from="1709897428296" duration="599000" />
      <workItem from="1709971385224" duration="1120000" />
      <workItem from="1710150640759" duration="1852000" />
      <workItem from="1710219358913" duration="733000" />
      <workItem from="1710325440104" duration="1615000" />
      <workItem from="1710605380238" duration="3521000" />
      <workItem from="1710650153349" duration="1299000" />
      <workItem from="1710669358209" duration="1136000" />
      <workItem from="1710673327028" duration="1442000" />
      <workItem from="1710920578749" duration="3620000" />
      <workItem from="1711647952213" duration="364000" />
      <workItem from="1711677099074" duration="602000" />
      <workItem from="1711787773091" duration="1470000" />
      <workItem from="1711789273084" duration="21387000" />
      <workItem from="1711860137102" duration="1275000" />
      <workItem from="1712118247699" duration="907000" />
      <workItem from="1712124799852" duration="1452000" />
      <workItem from="1712150361531" duration="599000" />
      <workItem from="1712457492607" duration="7188000" />
      <workItem from="1712467957839" duration="98000" />
      <workItem from="1712468069634" duration="49000" />
      <workItem from="1712468132838" duration="1509000" />
      <workItem from="1712496763835" duration="740000" />
      <workItem from="1712498831688" duration="4384000" />
      <workItem from="1712546098251" duration="599000" />
      <workItem from="1713616639013" duration="1444000" />
      <workItem from="1714469199511" duration="788000" />
      <workItem from="1714629570403" duration="558000" />
      <workItem from="1714630146732" duration="8626000" />
      <workItem from="1714730025021" duration="11819000" />
      <workItem from="1714802504477" duration="1360000" />
      <workItem from="1715067330763" duration="1404000" />
      <workItem from="1715153310706" duration="3126000" />
      <workItem from="1715215692131" duration="4332000" />
      <workItem from="1715322792896" duration="14784000" />
      <workItem from="1715396764012" duration="42105000" />
      <workItem from="1715485789032" duration="10160000" />
      <workItem from="1715552571503" duration="283000" />
      <workItem from="1715552865914" duration="565000" />
      <workItem from="1715553444047" duration="3823000" />
      <workItem from="1715587604748" duration="723000" />
      <workItem from="1715607534977" duration="641000" />
      <workItem from="1715682002125" duration="1946000" />
      <workItem from="1715927242749" duration="5091000" />
      <workItem from="1715945891740" duration="9553000" />
      <workItem from="1715995565945" duration="4521000" />
      <workItem from="1716014734157" duration="9354000" />
      <workItem from="1716079997250" duration="4713000" />
      <workItem from="1716099924744" duration="16923000" />
      <workItem from="1716126942711" duration="753000" />
      <workItem from="1716127725392" duration="407000" />
      <workItem from="1716172148728" duration="258000" />
      <workItem from="1716172417330" duration="20298000" />
      <workItem from="1716274596230" duration="292000" />
      <workItem from="1716275815079" duration="13129000" />
      <workItem from="1716300389311" duration="160000" />
      <workItem from="1716541170700" duration="383000" />
      <workItem from="1716541561010" duration="250000" />
      <workItem from="1716541821971" duration="1633000" />
      <workItem from="1716615741413" duration="2843000" />
      <workItem from="1716722064544" duration="562000" />
      <workItem from="1716724488842" duration="463000" />
      <workItem from="1716724961428" duration="12975000" />
      <workItem from="1716788862767" duration="22886000" />
      <workItem from="1716819743923" duration="7120000" />
      <workItem from="1716863693388" duration="9360000" />
      <workItem from="1716951967955" duration="1233000" />
      <workItem from="1716964571286" duration="1234000" />
      <workItem from="1716975693508" duration="3660000" />
      <workItem from="1716983023752" duration="7126000" />
      <workItem from="1717026333002" duration="362000" />
      <workItem from="1717027946665" duration="3519000" />
      <workItem from="1717082825587" duration="608000" />
      <workItem from="1717121991640" duration="676000" />
      <workItem from="1717129586630" duration="1234000" />
      <workItem from="1717238066749" duration="877000" />
      <workItem from="1717398221306" duration="2325000" />
      <workItem from="1717473436812" duration="3151000" />
      <workItem from="1717487550458" duration="3885000" />
      <workItem from="1717831641945" duration="2529000" />
      <workItem from="1718088404266" duration="175000" />
      <workItem from="1718088596743" duration="599000" />
      <workItem from="1718637506235" duration="2168000" />
      <workItem from="1719406472824" duration="116000" />
      <workItem from="1719487796266" duration="4355000" />
      <workItem from="1719564605251" duration="10947000" />
      <workItem from="1719584039386" duration="7353000" />
      <workItem from="1719657370426" duration="436000" />
      <workItem from="1719900520203" duration="319000" />
      <workItem from="1719903124821" duration="6613000" />
      <workItem from="1720246404851" duration="2068000" />
      <workItem from="1720251663467" duration="557000" />
      <workItem from="1720445113319" duration="458000" />
      <workItem from="1720445588196" duration="1372000" />
      <workItem from="1720510878648" duration="2391000" />
      <workItem from="1720529677982" duration="9113000" />
      <workItem from="1720602595367" duration="2637000" />
      <workItem from="1720618095739" duration="501000" />
      <workItem from="1720618606444" duration="897000" />
      <workItem from="1720665959862" duration="302000" />
      <workItem from="1720666280912" duration="1624000" />
      <workItem from="1720683082154" duration="743000" />
      <workItem from="1720750818558" duration="4105000" />
      <workItem from="1720774892038" duration="6203000" />
      <workItem from="1720795956593" duration="12994000" />
      <workItem from="1720865978600" duration="13686000" />
      <workItem from="1720951945008" duration="603000" />
      <workItem from="1721043042363" duration="1213000" />
      <workItem from="1721493923159" duration="634000" />
      <workItem from="1721710214358" duration="2650000" />
      <workItem from="1722681908254" duration="755000" />
      <workItem from="1722682675616" duration="1739000" />
      <workItem from="1722751360716" duration="634000" />
      <workItem from="1722826323331" duration="599000" />
      <workItem from="1723034415811" duration="599000" />
      <workItem from="1723784893606" duration="38000" />
      <workItem from="1723910020186" duration="701000" />
      <workItem from="1723949919809" duration="1804000" />
      <workItem from="1724077368285" duration="16000" />
      <workItem from="1731332536754" duration="1594000" />
      <workItem from="1744683272119" duration="1211000" />
      <workItem from="1744764705586" duration="9619000" />
      <workItem from="1744970003355" duration="7018000" />
      <workItem from="1745025874181" duration="31000" />
      <workItem from="1745025931030" duration="2893000" />
      <workItem from="1745150220748" duration="270000" />
      <workItem from="1745218656441" duration="3727000" />
      <workItem from="1745545843055" duration="362000" />
      <workItem from="1745728312494" duration="18689000" />
      <workItem from="1745808533401" duration="7561000" />
      <workItem from="1745897722058" duration="7788000" />
      <workItem from="1745993705004" duration="27027000" />
      <workItem from="1746158954114" duration="31122000" />
      <workItem from="1746237494510" duration="37813000" />
      <workItem from="1746326329732" duration="33974000" />
      <workItem from="1746433850441" duration="3145000" />
      <workItem from="1746442096832" duration="508000" />
      <workItem from="1746502237517" duration="1269000" />
      <workItem from="1746573564959" duration="8345000" />
      <workItem from="1746623811367" duration="5359000" />
      <workItem from="1746673587347" duration="56000" />
      <workItem from="1746673658894" duration="13079000" />
      <workItem from="1746687533330" duration="11291000" />
      <workItem from="1746723723896" duration="6008000" />
      <workItem from="1746810901069" duration="1235000" />
      <workItem from="1747413134228" duration="1254000" />
      <workItem from="1756946502520" duration="718000" />
    </task>
    <task id="LOCAL-00001" summary="重构前重启项目">
      <created>1677907616108</created>
      <option name="number" value="00001" />
      <option name="presentableId" value="LOCAL-00001" />
      <option name="project" value="LOCAL" />
      <updated>1677907616108</updated>
    </task>
    <task id="LOCAL-00002" summary="动机重构前琐碎">
      <created>1682582639259</created>
      <option name="number" value="00002" />
      <option name="presentableId" value="LOCAL-00002" />
      <option name="project" value="LOCAL" />
      <updated>1682582639259</updated>
    </task>
    <task id="LOCAL-00003" summary="删除一些重构过程文件">
      <created>1682582728599</created>
      <option name="number" value="00003" />
      <option name="presentableId" value="LOCAL-00003" />
      <option name="project" value="LOCAL" />
      <updated>1682582728599</updated>
    </task>
    <task id="LOCAL-00004" summary="将一些nars相关文件纳入版本控制">
      <created>1682582934244</created>
      <option name="number" value="00004" />
      <option name="presentableId" value="LOCAL-00004" />
      <option name="project" value="LOCAL" />
      <updated>1682582934244</updated>
    </task>
    <task id="LOCAL-00005" summary="从感知到动机+自传体+场景判定">
      <created>1683627405681</created>
      <option name="number" value="00005" />
      <option name="presentableId" value="LOCAL-00005" />
      <option name="project" value="LOCAL" />
      <updated>1683627405681</updated>
    </task>
    <task id="LOCAL-00006" summary="自底向上+语法树图+子图新类">
      <created>1685872483707</created>
      <option name="number" value="00006" />
      <option name="presentableId" value="LOCAL-00006" />
      <option name="project" value="LOCAL" />
      <updated>1685872483707</updated>
    </task>
    <task id="LOCAL-00007" summary="完善树图新类+treebag+ChartTreeSet">
      <created>1686656703023</created>
      <option name="number" value="00007" />
      <option name="presentableId" value="LOCAL-00007" />
      <option name="project" value="LOCAL" />
      <updated>1686656703023</updated>
    </task>
    <task id="LOCAL-00008" summary="多线程构建+存treebag+树图首轮调试">
      <created>1686812529123</created>
      <option name="number" value="00008" />
      <option name="presentableId" value="LOCAL-00008" />
      <option name="project" value="LOCAL" />
      <updated>1686812529123</updated>
    </task>
    <task id="LOCAL-00009" summary="term继承node+实现嵌套组装">
      <created>1686922232256</created>
      <option name="number" value="00009" />
      <option name="presentableId" value="LOCAL-00009" />
      <option name="project" value="LOCAL" />
      <updated>1686922232256</updated>
    </task>
    <task id="LOCAL-00010" summary="termnode的实现方法问题如id">
      <created>1686988090545</created>
      <option name="number" value="00010" />
      <option name="presentableId" value="LOCAL-00010" />
      <option name="project" value="LOCAL" />
      <updated>1686988090545</updated>
    </task>
    <task id="LOCAL-00011" summary="语法树图完整嵌套+复杂度">
      <created>1687191821809</created>
      <option name="number" value="00011" />
      <option name="presentableId" value="LOCAL-00011" />
      <option name="project" value="LOCAL" />
      <updated>1687191821809</updated>
    </task>
    <task id="LOCAL-00012" summary="语义图嵌套构建+逐步输出理解">
      <created>1688887091098</created>
      <option name="number" value="00012" />
      <option name="presentableId" value="LOCAL-00012" />
      <option name="project" value="LOCAL" />
      <updated>1688887091098</updated>
    </task>
    <task id="LOCAL-00013" summary="小修改">
      <created>1689731342710</created>
      <option name="number" value="00013" />
      <option name="presentableId" value="LOCAL-00013" />
      <option name="project" value="LOCAL" />
      <updated>1689731342710</updated>
    </task>
    <task id="LOCAL-00014" summary="小修改">
      <created>1689731556369</created>
      <option name="number" value="00014" />
      <option name="presentableId" value="LOCAL-00014" />
      <option name="project" value="LOCAL" />
      <updated>1689731556369</updated>
    </task>
    <task id="LOCAL-00015" summary="nars304升级到310">
      <created>1689738330305</created>
      <option name="number" value="00015" />
      <option name="presentableId" value="LOCAL-00015" />
      <option name="project" value="LOCAL" />
      <updated>1689738330305</updated>
    </task>
    <task id="LOCAL-00016" summary="添加lab，一些小demo，可能会有用">
      <created>1689739479127</created>
      <option name="number" value="00016" />
      <option name="presentableId" value="LOCAL-00016" />
      <option name="project" value="LOCAL" />
      <updated>1689739479127</updated>
    </task>
    <task id="LOCAL-00017" summary="大改的nars类转到独立文件夹">
      <created>1689742814298</created>
      <option name="number" value="00017" />
      <option name="presentableId" value="LOCAL-00017" />
      <option name="project" value="LOCAL" />
      <updated>1689742814298</updated>
    </task>
    <task id="LOCAL-00018" summary="改编码">
      <created>1689745157341</created>
      <option name="number" value="00018" />
      <option name="presentableId" value="LOCAL-00018" />
      <option name="project" value="LOCAL" />
      <updated>1689745157341</updated>
    </task>
    <task id="LOCAL-00019" summary="改编码">
      <created>1689745423191</created>
      <option name="number" value="00019" />
      <option name="presentableId" value="LOCAL-00019" />
      <option name="project" value="LOCAL" />
      <updated>1689745423191</updated>
    </task>
    <task id="LOCAL-00020" summary="改编码">
      <created>1689745727375</created>
      <option name="number" value="00020" />
      <option name="presentableId" value="LOCAL-00020" />
      <option name="project" value="LOCAL" />
      <updated>1689745727375</updated>
    </task>
    <task id="LOCAL-00021" summary="改编码">
      <created>1689745800563</created>
      <option name="number" value="00021" />
      <option name="presentableId" value="LOCAL-00021" />
      <option name="project" value="LOCAL" />
      <updated>1689745800563</updated>
    </task>
    <task id="LOCAL-00022" summary="改编码">
      <created>1689746007327</created>
      <option name="number" value="00022" />
      <option name="presentableId" value="LOCAL-00022" />
      <option name="project" value="LOCAL" />
      <updated>1689746007327</updated>
    </task>
    <task id="LOCAL-00023" summary="改编码">
      <created>1689746904701</created>
      <option name="number" value="00023" />
      <option name="presentableId" value="LOCAL-00023" />
      <option name="project" value="LOCAL" />
      <updated>1689746904701</updated>
    </task>
    <task id="LOCAL-00024" summary="改编码">
      <created>1689747817966</created>
      <option name="number" value="00024" />
      <option name="presentableId" value="LOCAL-00024" />
      <option name="project" value="LOCAL" />
      <updated>1689747817966</updated>
    </task>
    <task id="LOCAL-00025" summary="改编码">
      <created>1689748199388</created>
      <option name="number" value="00025" />
      <option name="presentableId" value="LOCAL-00025" />
      <option name="project" value="LOCAL" />
      <updated>1689748199388</updated>
    </task>
    <task id="LOCAL-00026" summary="改编码">
      <created>1689748371294</created>
      <option name="number" value="00026" />
      <option name="presentableId" value="LOCAL-00026" />
      <option name="project" value="LOCAL" />
      <updated>1689748371294</updated>
    </task>
    <task id="LOCAL-00027" summary="改编码，小注释">
      <created>1689824392193</created>
      <option name="number" value="00027" />
      <option name="presentableId" value="LOCAL-00027" />
      <option name="project" value="LOCAL" />
      <updated>1689824392193</updated>
    </task>
    <task id="LOCAL-00028" summary="treechart做复合term和其他结构桥梁">
      <created>1689840219560</created>
      <option name="number" value="00028" />
      <option name="presentableId" value="LOCAL-00028" />
      <option name="project" value="LOCAL" />
      <updated>1689840219560</updated>
    </task>
    <task id="LOCAL-00029" summary="nodeimpl的属性方法都搬到tnimpl类中，解决部分继承重写方法调用问题，但貌似变笨了">
      <created>1689847885860</created>
      <option name="number" value="00029" />
      <option name="presentableId" value="LOCAL-00029" />
      <option name="project" value="LOCAL" />
      <updated>1689847885860</updated>
    </task>
    <task id="LOCAL-00030" summary="tnimpl类">
      <created>1689848190188</created>
      <option name="number" value="00030" />
      <option name="presentableId" value="LOCAL-00030" />
      <option name="project" value="LOCAL" />
      <updated>1689848190188</updated>
    </task>
    <task id="LOCAL-00031" summary="点边全改pam点边，很多琐碎点边问题解决">
      <created>1689856783904</created>
      <option name="number" value="00031" />
      <option name="presentableId" value="LOCAL-00031" />
      <option name="project" value="LOCAL" />
      <updated>1689856783904</updated>
    </task>
    <task id="LOCAL-00032" summary="原速衰减+边带点">
      <created>1689859032138</created>
      <option name="number" value="00032" />
      <option name="presentableId" value="LOCAL-00032" />
      <option name="project" value="LOCAL" />
      <updated>1689859032138</updated>
    </task>
    <task id="LOCAL-00045" summary="log">
      <changelist id="ef9d0f92-054e-4d42-8946-81a2e19df511" name="log" comment="" />
      <created>1700279273650</created>
      <option name="number" value="00045" />
      <option name="presentableId" value="LOCAL-00045" />
      <option name="project" value="LOCAL" />
      <updated>1700279273650</updated>
    </task>
    <task id="LOCAL-00099" summary="小修改-ai大修前">
      <option name="closed" value="true" />
      <created>1745738834842</created>
      <option name="number" value="00099" />
      <option name="presentableId" value="LOCAL-00099" />
      <option name="project" value="LOCAL" />
      <updated>1745738834842</updated>
    </task>
    <task id="LOCAL-00100" summary="拆分语法类函数">
      <option name="closed" value="true" />
      <created>1745768177416</created>
      <option name="number" value="00100" />
      <option name="presentableId" value="LOCAL-00100" />
      <option name="project" value="LOCAL" />
      <updated>1745768177416</updated>
    </task>
    <task id="LOCAL-00101" summary="用augment分析架构和优化策略+代码零星优化">
      <option name="closed" value="true" />
      <created>1746437083864</created>
      <option name="number" value="00101" />
      <option name="presentableId" value="LOCAL-00101" />
      <option name="project" value="LOCAL" />
      <updated>1746437083865</updated>
    </task>
    <task id="LOCAL-00102" summary="图式扩散+图式搜索+图式投票">
      <option name="closed" value="true" />
      <created>1746442645440</created>
      <option name="number" value="00102" />
      <option name="presentableId" value="LOCAL-00102" />
      <option name="project" value="LOCAL" />
      <updated>1746442645440</updated>
    </task>
    <task id="LOCAL-00103" summary="情感+搜索+nl编译">
      <option name="closed" value="true" />
      <created>1746453817279</created>
      <option name="number" value="00103" />
      <option name="presentableId" value="LOCAL-00103" />
      <option name="project" value="LOCAL" />
      <updated>1746453817279</updated>
    </task>
    <task id="LOCAL-00104" summary="nars整合+理论基础+todo等">
      <option name="closed" value="true" />
      <created>1746462452023</created>
      <option name="number" value="00104" />
      <option name="presentableId" value="LOCAL-00104" />
      <option name="project" value="LOCAL" />
      <updated>1746462452023</updated>
    </task>
    <task id="LOCAL-00105" summary="图程分析优化">
      <option name="closed" value="true" />
      <created>1746464383264</created>
      <option name="number" value="00105" />
      <option name="presentableId" value="LOCAL-00105" />
      <option name="project" value="LOCAL" />
      <updated>1746464383264</updated>
    </task>
    <task id="LOCAL-00106" summary="nl与nars集成+文档优化+todo+开源比较">
      <option name="closed" value="true" />
      <created>1746518138689</created>
      <option name="number" value="00106" />
      <option name="presentableId" value="LOCAL-00106" />
      <option name="project" value="LOCAL" />
      <updated>1746518138689</updated>
    </task>
    <task id="LOCAL-00107" summary="nl文档综合">
      <option name="closed" value="true" />
      <created>1746545075656</created>
      <option name="number" value="00107" />
      <option name="presentableId" value="LOCAL-00107" />
      <option name="project" value="LOCAL" />
      <updated>1746545075657</updated>
    </task>
    <task id="LOCAL-00108" summary="动机+api+动词">
      <option name="closed" value="true" />
      <created>1746606025547</created>
      <option name="number" value="00108" />
      <option name="presentableId" value="LOCAL-00108" />
      <option name="project" value="LOCAL" />
      <updated>1746606025548</updated>
    </task>
    <task id="LOCAL-00109" summary="图式执行+图式结构">
      <option name="closed" value="true" />
      <created>1746620335897</created>
      <option name="number" value="00109" />
      <option name="presentableId" value="LOCAL-00109" />
      <option name="project" value="LOCAL" />
      <updated>1746620335898</updated>
    </task>
    <task id="LOCAL-00110" summary="图式模型整合">
      <option name="closed" value="true" />
      <created>1746622276971</created>
      <option name="number" value="00110" />
      <option name="presentableId" value="LOCAL-00110" />
      <option name="project" value="LOCAL" />
      <updated>1746622276971</updated>
    </task>
    <task id="LOCAL-00111" summary="图式案例模板">
      <option name="closed" value="true" />
      <created>1746630155402</created>
      <option name="number" value="00111" />
      <option name="presentableId" value="LOCAL-00111" />
      <option name="project" value="LOCAL" />
      <updated>1746630155402</updated>
    </task>
    <task id="LOCAL-00112" summary="模拟执行器+操作api">
      <option name="closed" value="true" />
      <created>1746635286633</created>
      <option name="number" value="00112" />
      <option name="presentableId" value="LOCAL-00112" />
      <option name="project" value="LOCAL" />
      <updated>1746635286633</updated>
    </task>
    <task id="LOCAL-00113" summary="加法图式+模型优化">
      <option name="closed" value="true" />
      <created>1746644833424</created>
      <option name="number" value="00113" />
      <option name="presentableId" value="LOCAL-00113" />
      <option name="project" value="LOCAL" />
      <updated>1746644833425</updated>
    </task>
    <task id="LOCAL-00114" summary="加法图式结构-导入图谱形式-细化">
      <option name="closed" value="true" />
      <created>1747413162694</created>
      <option name="number" value="00114" />
      <option name="presentableId" value="LOCAL-00114" />
      <option name="project" value="LOCAL" />
      <updated>1747413162694</updated>
    </task>
    <option name="localTasksCounter" value="115" />
    <option name="trackContextForNewChangelist" value="true" />
    <servers />
  </component>
  <component name="TypeScriptGeneratedFilesManager">
    <option name="version" value="3" />
  </component>
  <component name="Vcs.Log.History.Properties">
    <option name="COLUMN_ID_ORDER">
      <list>
        <option value="Default.Root" />
        <option value="Default.Author" />
        <option value="Default.Date" />
        <option value="Default.Subject" />
        <option value="Space.CommitStatus" />
      </list>
    </option>
  </component>
  <component name="Vcs.Log.Tabs.Properties">
    <option name="TAB_STATES">
      <map>
        <entry key="MAIN">
          <value>
            <State />
          </value>
        </entry>
      </map>
    </option>
  </component>
  <component name="VcsManagerConfiguration">
    <option name="CHECK_CODE_SMELLS_BEFORE_PROJECT_COMMIT" value="false" />
    <option name="CHECK_NEW_TODO" value="false" />
    <option name="ADD_EXTERNAL_FILES_SILENTLY" value="true" />
    <MESSAGE value="动机树概念完善" />
    <MESSAGE value="构建动机树1版" />
    <MESSAGE value="动机链初步跑通" />
    <MESSAGE value="优化搜索匹配答案" />
    <MESSAGE value="nars推理规则注释" />
    <MESSAGE value="尝试整合短语结构" />
    <MESSAGE value="加法时序描述案例" />
    <MESSAGE value="双条件查询操作" />
    <MESSAGE value="更新标签ui" />
    <MESSAGE value="小修改-ai大修前" />
    <MESSAGE value="拆分语法类函数" />
    <MESSAGE value="用augment分析架构和优化策略+代码零星优化" />
    <MESSAGE value="图式扩散+图式搜索+图式投票" />
    <MESSAGE value="情感+搜索+nl编译" />
    <MESSAGE value="nars整合+理论基础+todo等" />
    <MESSAGE value="图程分析优化" />
    <MESSAGE value="nl与nars集成+文档优化+todo+开源比较" />
    <MESSAGE value="nl文档综合" />
    <MESSAGE value="动机+api+动词" />
    <MESSAGE value="图式执行+图式结构" />
    <MESSAGE value="图式模型整合" />
    <MESSAGE value="图式案例模板" />
    <MESSAGE value="模拟执行器+操作api" />
    <MESSAGE value="加法图式+模型优化" />
    <MESSAGE value="加法图式结构-导入图谱形式-细化" />
    <option name="LAST_COMMIT_MESSAGE" value="加法图式结构-导入图谱形式-细化" />
  </component>
  <component name="XDebuggerManager">
    <breakpoint-manager>
      <breakpoints>
        <line-breakpoint type="java-line">
          <url>file://$PROJECT_DIR$/src/main/java/edu/memphis/ccrg/lida/nlanguage/GrammarAnalyzTask.java</url>
          <line>322</line>
          <option name="timeStamp" value="39" />
        </line-breakpoint>
        <line-breakpoint type="java-line">
          <url>file://$PROJECT_DIR$/src/main/java/edu/memphis/ccrg/lida/pam/PAMemoryImpl.java</url>
          <line>1333</line>
          <option name="timeStamp" value="50" />
        </line-breakpoint>
        <line-breakpoint type="java-line">
          <url>file://$PROJECT_DIR$/src/main/java/edu/memphis/ccrg/lida/nlanguage/GrammarAnalyzTask.java</url>
          <line>285</line>
          <option name="timeStamp" value="64" />
        </line-breakpoint>
        <line-breakpoint type="java-line">
          <url>file://$PROJECT_DIR$/src/main/java/edu/memphis/ccrg/lida/nlanguage/GrammarAnalyzTask.java</url>
          <line>380</line>
          <option name="timeStamp" value="86" />
        </line-breakpoint>
        <line-breakpoint type="java-line">
          <url>file://$PROJECT_DIR$/src/main/java/edu/memphis/ccrg/lida/nlanguage/GrammarAnalyzTask.java</url>
          <line>244</line>
          <option name="timeStamp" value="89" />
        </line-breakpoint>
        <line-breakpoint type="java-line">
          <url>file://$PROJECT_DIR$/src/main/java/edu/memphis/ccrg/lida/nlanguage/GrammarAnalyzTask.java</url>
          <line>292</line>
          <option name="timeStamp" value="91" />
        </line-breakpoint>
        <line-breakpoint type="java-line">
          <url>file://$PROJECT_DIR$/src/main/java/edu/memphis/ccrg/lida/nlanguage/GrammarAnalyzTask.java</url>
          <line>295</line>
          <option name="timeStamp" value="92" />
        </line-breakpoint>
        <line-breakpoint type="java-line">
          <url>file://$PROJECT_DIR$/src/main/java/edu/memphis/ccrg/linars/TermNodeImpl.java</url>
          <line>190</line>
          <option name="timeStamp" value="112" />
        </line-breakpoint>
        <line-breakpoint type="java-line">
          <url>file://$PROJECT_DIR$/src/main/java/edu/memphis/ccrg/linars/TermNodeImpl.java</url>
          <line>185</line>
          <option name="timeStamp" value="113" />
        </line-breakpoint>
        <line-breakpoint type="java-line">
          <url>file://$PROJECT_DIR$/src/main/java/edu/memphis/ccrg/linars/TermNodeImpl.java</url>
          <line>158</line>
          <option name="timeStamp" value="114" />
        </line-breakpoint>
        <line-breakpoint type="java-line">
          <url>file://$PROJECT_DIR$/src/main/java/edu/memphis/ccrg/linars/TermNodeImpl.java</url>
          <line>205</line>
          <option name="timeStamp" value="116" />
        </line-breakpoint>
        <line-breakpoint type="java-line">
          <url>file://$PROJECT_DIR$/src/main/java/edu/memphis/ccrg/linars/TermNodeImpl.java</url>
          <line>210</line>
          <option name="timeStamp" value="117" />
        </line-breakpoint>
        <line-breakpoint type="java-line">
          <url>file://$PROJECT_DIR$/src/main/java/edu/memphis/ccrg/linars/TermNodeImpl.java</url>
          <line>197</line>
          <option name="timeStamp" value="120" />
        </line-breakpoint>
        <line-breakpoint type="java-line">
          <url>file://$PROJECT_DIR$/src/main/java/edu/memphis/ccrg/lida/framework/shared/NodeImpl.java</url>
          <line>130</line>
          <option name="timeStamp" value="121" />
        </line-breakpoint>
        <line-breakpoint enabled="true" type="java-line">
          <url>file://$PROJECT_DIR$/src/main/java/edu/memphis/ccrg/lida/framework/shared/NodeImpl.java</url>
          <line>153</line>
          <option name="timeStamp" value="126" />
        </line-breakpoint>
        <line-breakpoint type="java-line">
          <url>file://$PROJECT_DIR$/src/main/java/edu/memphis/ccrg/linars/Term.java</url>
          <line>217</line>
          <option name="timeStamp" value="128" />
        </line-breakpoint>
        <line-breakpoint type="java-line">
          <url>file://$PROJECT_DIR$/src/main/java/edu/memphis/ccrg/linars/Term.java</url>
          <line>302</line>
          <option name="timeStamp" value="129" />
        </line-breakpoint>
        <line-breakpoint enabled="true" type="java-line">
          <url>file://$PROJECT_DIR$/src/main/java/edu/memphis/ccrg/lida/pam/PAMemoryImpl.java</url>
          <line>604</line>
          <option name="timeStamp" value="161" />
        </line-breakpoint>
        <line-breakpoint enabled="true" type="java-line">
          <url>file://$PROJECT_DIR$/src/main/java/org/opennars/operator/mental/FeelSatisfied.java</url>
          <line>50</line>
          <option name="timeStamp" value="164" />
        </line-breakpoint>
        <line-breakpoint enabled="true" type="java-line">
          <url>file://$PROJECT_DIR$/src/main/java/org/opennars/operator/mental/Feel.java</url>
          <line>62</line>
          <option name="timeStamp" value="166" />
        </line-breakpoint>
        <line-breakpoint enabled="true" type="java-line">
          <url>file://$PROJECT_DIR$/src/main/java/org/opennars/operator/mental/FeelBusy.java</url>
          <line>53</line>
          <option name="timeStamp" value="167" />
        </line-breakpoint>
        <line-breakpoint enabled="true" type="java-line">
          <url>file://$PROJECT_DIR$/src/main/java/org/opennars/plugin/mental/InternalExperience.java</url>
          <line>225</line>
          <option name="timeStamp" value="168" />
        </line-breakpoint>
        <line-breakpoint type="java-line">
          <url>file://$PROJECT_DIR$/src/main/java/edu/memphis/ccrg/lida/workspace/WorkspaceImpl.java</url>
          <line>187</line>
          <option name="timeStamp" value="191" />
        </line-breakpoint>
        <line-breakpoint type="java-line">
          <url>file://$PROJECT_DIR$/src/main/java/automenta/vivisect/graph/AbstractGraphVis.java</url>
          <line>97</line>
          <option name="timeStamp" value="280" />
        </line-breakpoint>
        <line-breakpoint type="java-line">
          <url>file://$PROJECT_DIR$/src/main/java/automenta/vivisect/graph/AbstractGraphVis.java</url>
          <line>133</line>
          <option name="timeStamp" value="281" />
        </line-breakpoint>
        <line-breakpoint type="java-line">
          <url>file://$PROJECT_DIR$/src/main/java/org/opennars/gui/output/graph/NARGraphVis.java</url>
          <line>227</line>
          <option name="timeStamp" value="282" />
        </line-breakpoint>
        <line-breakpoint type="java-line">
          <url>file://$PROJECT_DIR$/src/main/java/org/opennars/gui/output/graph/NARGraphVis.java</url>
          <line>255</line>
          <option name="timeStamp" value="283" />
        </line-breakpoint>
        <line-breakpoint type="java-line">
          <url>file://$PROJECT_DIR$/src/main/java/org/opennars/gui/output/graph/NARGraphVis.java</url>
          <line>242</line>
          <option name="timeStamp" value="284" />
        </line-breakpoint>
        <line-breakpoint type="java-line">
          <url>file://$PROJECT_DIR$/src/main/java/org/opennars/gui/output/graph/NARGraphVis.java</url>
          <line>253</line>
          <option name="timeStamp" value="285" />
        </line-breakpoint>
        <line-breakpoint type="java-line">
          <url>file://$PROJECT_DIR$/src/main/java/org/opennars/gui/output/graph/NARGraphVis.java</url>
          <line>102</line>
          <option name="timeStamp" value="286" />
        </line-breakpoint>
        <line-breakpoint type="java-line">
          <url>file://$PROJECT_DIR$/src/main/java/automenta/vivisect/graph/AnimatingGraphVis.java</url>
          <line>46</line>
          <option name="timeStamp" value="288" />
        </line-breakpoint>
        <line-breakpoint type="java-line">
          <url>file://$PROJECT_DIR$/src/main/java/org/opennars/gui/util/NARGraph.java</url>
          <line>337</line>
          <option name="timeStamp" value="292" />
        </line-breakpoint>
        <line-breakpoint type="java-line">
          <url>file://$PROJECT_DIR$/src/main/java/edu/memphis/ccrg/lida/alifeagent/environment/operators/Move.java</url>
          <line>29</line>
          <option name="timeStamp" value="302" />
        </line-breakpoint>
        <line-breakpoint type="java-line">
          <url>file://$PROJECT_DIR$/src/main/java/edu/memphis/ccrg/lida/alifeagent/environment/operators/Right.java</url>
          <line>29</line>
          <option name="timeStamp" value="304" />
        </line-breakpoint>
        <line-breakpoint type="java-line">
          <url>file://$PROJECT_DIR$/src/main/java/edu/memphis/ccrg/lida/alifeagent/environment/operators/Flee.java</url>
          <line>27</line>
          <option name="timeStamp" value="305" />
        </line-breakpoint>
        <line-breakpoint type="java-line">
          <url>file://$PROJECT_DIR$/src/main/java/edu/memphis/ccrg/lida/alifeagent/environment/operations/TurnLeftOperation.java</url>
          <line>31</line>
          <option name="timeStamp" value="306" />
        </line-breakpoint>
        <line-breakpoint type="java-line">
          <url>file://$PROJECT_DIR$/src/main/java/edu/memphis/ccrg/lida/alifeagent/environment/operations/TurnRightOperation.java</url>
          <line>33</line>
          <option name="timeStamp" value="307" />
        </line-breakpoint>
        <line-breakpoint type="java-line">
          <url>file://$PROJECT_DIR$/src/main/java/edu/memphis/ccrg/lida/alifeagent/environment/operations/TurnAroundOperation.java</url>
          <line>31</line>
          <option name="timeStamp" value="308" />
        </line-breakpoint>
        <line-breakpoint type="java-line">
          <url>file://$PROJECT_DIR$/src/main/java/edu/memphis/ccrg/lida/alifeagent/environment/operators/Around.java</url>
          <line>30</line>
          <option name="timeStamp" value="310" />
        </line-breakpoint>
        <line-breakpoint type="java-line">
          <url>file://$PROJECT_DIR$/src/main/java/org/opennars/storage/Buffer.java</url>
          <line>96</line>
          <option name="timeStamp" value="312" />
        </line-breakpoint>
        <line-breakpoint type="java-line">
          <url>file://$PROJECT_DIR$/src/main/java/edu/memphis/ccrg/linars/Memory.java</url>
          <line>426</line>
          <option name="timeStamp" value="314" />
        </line-breakpoint>
        <line-breakpoint type="java-line">
          <url>file://$PROJECT_DIR$/src/main/java/edu/memphis/ccrg/lida/nlanguage/SemanticAnalyzTask0.java</url>
          <line>411</line>
          <option name="timeStamp" value="318" />
        </line-breakpoint>
        <line-breakpoint type="java-line">
          <url>file://$PROJECT_DIR$/src/main/java/edu/memphis/ccrg/lida/nlanguage/SemanticAnalyzTask0.java</url>
          <line>95</line>
          <option name="timeStamp" value="319" />
        </line-breakpoint>
        <line-breakpoint type="java-line">
          <url>file://$PROJECT_DIR$/src/test/java/edu/memphis/ccrg/lida/episodicmemory/neo/Lida2narseseTest.java</url>
          <line>387</line>
          <option name="timeStamp" value="325" />
        </line-breakpoint>
        <line-breakpoint type="java-line">
          <url>file://$PROJECT_DIR$/src/main/java/edu/memphis/ccrg/lida/alifeagent/environment/operators/Eat.java</url>
          <line>30</line>
          <option name="timeStamp" value="330" />
        </line-breakpoint>
        <line-breakpoint enabled="true" type="java-line">
          <url>file://$PROJECT_DIR$/src/main/java/edu/memphis/ccrg/lida/proceduralmemory/ProceduralMemoryImpl.java</url>
          <line>604</line>
          <option name="timeStamp" value="343" />
        </line-breakpoint>
        <line-breakpoint enabled="true" type="java-line">
          <url>file://$PROJECT_DIR$/src/main/java/edu/memphis/ccrg/lida/motivation/proceduralmemory/MotivationProceduralMemory.java</url>
          <line>174</line>
          <option name="timeStamp" value="353" />
        </line-breakpoint>
        <line-breakpoint enabled="true" type="java-line">
          <url>file://$PROJECT_DIR$/src/main/java/edu/memphis/ccrg/lida/proceduralmemory/ProceduralMemoryImpl.java</url>
          <line>546</line>
          <option name="timeStamp" value="364" />
        </line-breakpoint>
        <line-breakpoint enabled="true" type="java-line">
          <url>file://$PROJECT_DIR$/src/main/java/edu/memphis/ccrg/lida/proceduralmemory/ProceduralMemoryImpl.java</url>
          <line>436</line>
          <option name="timeStamp" value="365" />
        </line-breakpoint>
        <line-breakpoint enabled="true" type="java-line">
          <url>file://$PROJECT_DIR$/src/main/java/edu/memphis/ccrg/lida/proceduralmemory/ProceduralMemoryImpl.java</url>
          <line>412</line>
          <option name="timeStamp" value="366" />
        </line-breakpoint>
        <line-breakpoint enabled="true" type="java-line">
          <url>file://$PROJECT_DIR$/src/main/java/edu/memphis/ccrg/lida/proceduralmemory/ProceduralMemoryImpl.java</url>
          <line>336</line>
          <option name="timeStamp" value="367" />
        </line-breakpoint>
        <line-breakpoint enabled="true" type="java-line">
          <url>file://$PROJECT_DIR$/src/main/java/edu/memphis/ccrg/lida/data/NeoUtil.java</url>
          <line>63</line>
          <option name="timeStamp" value="368" />
        </line-breakpoint>
        <line-breakpoint enabled="true" type="java-line">
          <url>file://$PROJECT_DIR$/src/main/java/edu/memphis/ccrg/lida/data/NeoUtil.java</url>
          <line>70</line>
          <option name="timeStamp" value="369" />
        </line-breakpoint>
        <line-breakpoint enabled="true" type="java-line">
          <url>file://$PROJECT_DIR$/src/main/java/org/opennars/main/Nar.java</url>
          <line>443</line>
          <option name="timeStamp" value="387" />
        </line-breakpoint>
        <line-breakpoint enabled="true" type="java-line">
          <url>file://$PROJECT_DIR$/src/main/java/edu/memphis/ccrg/linars/Memory.java</url>
          <line>338</line>
          <option name="timeStamp" value="395" />
        </line-breakpoint>
        <line-breakpoint enabled="true" type="java-line">
          <url>file://$PROJECT_DIR$/src/main/java/org/opennars/operator/mental/Consider.java</url>
          <line>67</line>
          <option name="timeStamp" value="396" />
        </line-breakpoint>
        <line-breakpoint type="java-line">
          <url>file://$PROJECT_DIR$/src/main/java/org/opennars/inference/RuleTables.java</url>
          <line>113</line>
          <option name="timeStamp" value="405" />
        </line-breakpoint>
        <line-breakpoint type="java-line">
          <url>file://$PROJECT_DIR$/src/main/java/org/opennars/inference/RuleTables.java</url>
          <line>126</line>
          <option name="timeStamp" value="409" />
        </line-breakpoint>
        <line-breakpoint enabled="true" type="java-line">
          <url>file://$PROJECT_DIR$/src/main/java/edu/memphis/ccrg/lida/alifeagent/environment/operations/GetFoodOperation.java</url>
          <line>39</line>
          <option name="timeStamp" value="414" />
        </line-breakpoint>
        <line-breakpoint type="java-line">
          <url>file://$PROJECT_DIR$/src/main/java/edu/memphis/ccrg/lida/pam/tasks/ReasonTask.java</url>
          <line>46</line>
          <option name="timeStamp" value="415" />
        </line-breakpoint>
        <line-breakpoint type="java-line">
          <url>file://$PROJECT_DIR$/src/main/java/edu/memphis/ccrg/lida/pam/tasks/PropagationTask.java</url>
          <line>90</line>
          <option name="timeStamp" value="435" />
        </line-breakpoint>
        <line-breakpoint type="java-line">
          <url>file://$PROJECT_DIR$/src/main/java/edu/memphis/ccrg/lida/pam/tasks/PropagationTask.java</url>
          <line>94</line>
          <option name="timeStamp" value="437" />
        </line-breakpoint>
        <line-breakpoint type="java-line">
          <url>file://$PROJECT_DIR$/src/main/java/org/opennars/control/concept/ProcessAnticipation.java</url>
          <line>297</line>
          <option name="timeStamp" value="459" />
        </line-breakpoint>
        <line-breakpoint type="java-line">
          <url>file://$PROJECT_DIR$/src/main/java/edu/memphis/ccrg/linars/Concept.java</url>
          <line>149</line>
          <option name="timeStamp" value="482" />
        </line-breakpoint>
        <line-breakpoint type="java-line">
          <url>file://$PROJECT_DIR$/src/main/java/edu/memphis/ccrg/lida/pam/tasks/ProcessGBufferTask.java</url>
          <line>112</line>
          <option name="timeStamp" value="501" />
        </line-breakpoint>
        <line-breakpoint type="java-line">
          <url>file://$PROJECT_DIR$/src/main/java/edu/memphis/ccrg/lida/pam/tasks/ProcessGBufferTask.java</url>
          <line>114</line>
          <option name="timeStamp" value="503" />
        </line-breakpoint>
        <line-breakpoint type="java-line">
          <url>file://$PROJECT_DIR$/src/main/java/edu/memphis/ccrg/lida/pam/tasks/SelectConceptTask.java</url>
          <line>66</line>
          <option name="timeStamp" value="510" />
        </line-breakpoint>
        <line-breakpoint type="java-line">
          <url>file://$PROJECT_DIR$/src/main/java/org/opennars/main/Nar.java</url>
          <line>693</line>
          <option name="timeStamp" value="513" />
        </line-breakpoint>
        <line-breakpoint type="java-line">
          <url>file://$PROJECT_DIR$/src/main/java/edu/memphis/ccrg/lida/pam/tasks/ProcessGBufferTask.java</url>
          <line>70</line>
          <option name="timeStamp" value="515" />
        </line-breakpoint>
        <line-breakpoint type="java-line">
          <url>file://$PROJECT_DIR$/src/main/java/org/opennars/inference/TemporalRules.java</url>
          <line>228</line>
          <option name="timeStamp" value="516" />
        </line-breakpoint>
        <line-breakpoint type="java-line">
          <url>file://$PROJECT_DIR$/src/main/java/edu/memphis/ccrg/linars/Memory.java</url>
          <line>229</line>
          <option name="timeStamp" value="523" />
        </line-breakpoint>
        <line-breakpoint type="java-line">
          <url>file://$PROJECT_DIR$/src/main/java/org/opennars/main/Nar.java</url>
          <line>398</line>
          <option name="timeStamp" value="534" />
        </line-breakpoint>
        <line-breakpoint type="java-line">
          <url>file://$PROJECT_DIR$/src/main/java/org/opennars/plugin/perception/NarseseChannel.java</url>
          <line>53</line>
          <option name="timeStamp" value="535" />
        </line-breakpoint>
        <line-breakpoint type="java-line">
          <url>file://$PROJECT_DIR$/src/main/java/edu/memphis/ccrg/lida/alifeagent/featuredetectors/ObjectDetector.java</url>
          <line>63</line>
          <option name="timeStamp" value="537" />
        </line-breakpoint>
        <line-breakpoint type="java-line">
          <url>file://$PROJECT_DIR$/src/main/java/edu/memphis/ccrg/linars/Memory.java</url>
          <line>437</line>
          <option name="timeStamp" value="540" />
        </line-breakpoint>
        <line-breakpoint type="java-line">
          <url>file://$PROJECT_DIR$/src/main/java/edu/memphis/ccrg/linars/Concept.java</url>
          <line>354</line>
          <option name="timeStamp" value="552" />
        </line-breakpoint>
        <line-breakpoint type="java-line">
          <url>file://$PROJECT_DIR$/src/main/java/edu/memphis/ccrg/linars/Memory.java</url>
          <line>446</line>
          <option name="timeStamp" value="556" />
        </line-breakpoint>
        <line-breakpoint type="java-line">
          <url>file://$PROJECT_DIR$/src/main/java/org/opennars/gui/input/TextInputPanel.java</url>
          <line>295</line>
          <option name="timeStamp" value="563" />
        </line-breakpoint>
        <line-breakpoint type="java-line">
          <url>file://$PROJECT_DIR$/src/main/java/edu/memphis/ccrg/linars/Concept.java</url>
          <line>466</line>
          <option name="timeStamp" value="567" />
        </line-breakpoint>
        <line-breakpoint enabled="true" type="java-line">
          <url>file://$PROJECT_DIR$/src/main/java/org/opennars/storage/Buffer.java</url>
          <line>77</line>
          <option name="timeStamp" value="570" />
        </line-breakpoint>
        <line-breakpoint type="java-line">
          <url>file://$PROJECT_DIR$/src/main/java/edu/memphis/ccrg/lida/alifeagent/featuredetectors/ListenDetector.java</url>
          <line>113</line>
          <option name="timeStamp" value="617" />
        </line-breakpoint>
        <line-breakpoint type="java-line">
          <url>file://$PROJECT_DIR$/src/main/java/edu/memphis/ccrg/lida/alifeagent/featuredetectors/HealthDetector.java</url>
          <line>93</line>
          <option name="timeStamp" value="618" />
        </line-breakpoint>
        <line-breakpoint type="java-line">
          <url>file://$PROJECT_DIR$/src/main/java/edu/memphis/ccrg/linars/Concept.java</url>
          <line>200</line>
          <option name="timeStamp" value="665" />
        </line-breakpoint>
        <line-breakpoint type="java-line">
          <url>file://$PROJECT_DIR$/src/main/java/edu/memphis/ccrg/linars/ProcessGoal.java</url>
          <line>323</line>
          <option name="timeStamp" value="693" />
        </line-breakpoint>
        <line-breakpoint type="java-line">
          <url>file://$PROJECT_DIR$/src/main/java/edu/memphis/ccrg/linars/ProcessGoal.java</url>
          <line>121</line>
          <option name="timeStamp" value="696" />
        </line-breakpoint>
        <line-breakpoint type="java-line">
          <url>file://$PROJECT_DIR$/src/main/java/edu/memphis/ccrg/lida/pam/tasks/DoSuccTask.java</url>
          <line>73</line>
          <option name="timeStamp" value="716" />
        </line-breakpoint>
        <line-breakpoint type="java-line">
          <url>file://$PROJECT_DIR$/src/main/java/edu/memphis/ccrg/lida/pam/tasks/DoSuccTask.java</url>
          <line>143</line>
          <option name="timeStamp" value="717" />
        </line-breakpoint>
        <line-breakpoint type="java-line">
          <url>file://$PROJECT_DIR$/src/main/java/edu/memphis/ccrg/lida/pam/tasks/DoSuccTask.java</url>
          <line>85</line>
          <option name="timeStamp" value="719" />
        </line-breakpoint>
        <line-breakpoint type="java-line">
          <url>file://$PROJECT_DIR$/src/main/java/edu/memphis/ccrg/lida/framework/shared/NodeStructureImpl.java</url>
          <line>503</line>
          <option name="timeStamp" value="720" />
        </line-breakpoint>
        <line-breakpoint type="java-line">
          <url>file://$PROJECT_DIR$/src/main/java/edu/memphis/ccrg/lida/pam/tasks/DoMindActTask.java</url>
          <line>223</line>
          <option name="timeStamp" value="721" />
        </line-breakpoint>
        <line-breakpoint type="java-line">
          <url>file://$PROJECT_DIR$/src/main/java/org/opennars/inference/RuleTables.java</url>
          <line>105</line>
          <option name="timeStamp" value="722" />
        </line-breakpoint>
        <line-breakpoint type="java-line">
          <url>file://$PROJECT_DIR$/src/main/java/org/opennars/inference/RuleTables.java</url>
          <line>148</line>
          <option name="timeStamp" value="723" />
        </line-breakpoint>
        <line-breakpoint type="java-line">
          <url>file://$PROJECT_DIR$/src/main/java/org/opennars/inference/RuleTables.java</url>
          <line>181</line>
          <option name="timeStamp" value="724" />
        </line-breakpoint>
        <line-breakpoint type="java-line">
          <url>file://$PROJECT_DIR$/src/main/java/org/opennars/inference/RuleTables.java</url>
          <line>212</line>
          <option name="timeStamp" value="725" />
        </line-breakpoint>
        <line-breakpoint type="java-line">
          <url>file://$PROJECT_DIR$/src/main/java/org/opennars/inference/RuleTables.java</url>
          <line>214</line>
          <option name="timeStamp" value="726" />
        </line-breakpoint>
        <line-breakpoint type="java-line">
          <url>file://$PROJECT_DIR$/src/main/java/org/opennars/inference/RuleTables.java</url>
          <line>216</line>
          <option name="timeStamp" value="727" />
        </line-breakpoint>
        <line-breakpoint type="java-line">
          <url>file://$PROJECT_DIR$/src/main/java/edu/memphis/ccrg/lida/pam/PAMemoryImpl.java</url>
          <line>703</line>
          <option name="timeStamp" value="728" />
        </line-breakpoint>
        <line-breakpoint type="java-line">
          <url>file://$PROJECT_DIR$/src/main/java/edu/memphis/ccrg/lida/nlanguage/GrammarTask.java</url>
          <line>88</line>
          <option name="timeStamp" value="752" />
        </line-breakpoint>
        <line-breakpoint type="java-line">
          <url>file://$PROJECT_DIR$/src/main/java/edu/memphis/ccrg/lida/nlanguage/GrammarTask.java</url>
          <line>77</line>
          <option name="timeStamp" value="753" />
        </line-breakpoint>
        <line-breakpoint type="java-line">
          <url>file://$PROJECT_DIR$/src/main/java/edu/memphis/ccrg/lida/nlanguage/GrammarTask.java</url>
          <line>111</line>
          <option name="timeStamp" value="754" />
        </line-breakpoint>
        <line-breakpoint type="java-line">
          <url>file://$PROJECT_DIR$/src/main/java/edu/memphis/ccrg/lida/nlanguage/GrammarTask.java</url>
          <line>115</line>
          <option name="timeStamp" value="755" />
        </line-breakpoint>
        <line-breakpoint type="java-line">
          <url>file://$PROJECT_DIR$/src/main/java/edu/memphis/ccrg/lida/nlanguage/GrammarTask.java</url>
          <line>127</line>
          <option name="timeStamp" value="756" />
        </line-breakpoint>
        <line-breakpoint type="java-line">
          <url>file://$PROJECT_DIR$/src/main/java/edu/memphis/ccrg/lida/nlanguage/GrammarTask.java</url>
          <line>62</line>
          <option name="timeStamp" value="757" />
        </line-breakpoint>
        <line-breakpoint type="java-line">
          <url>file://$PROJECT_DIR$/src/main/java/edu/memphis/ccrg/lida/nlanguage/GrammarTask.java</url>
          <line>116</line>
          <option name="timeStamp" value="758" />
        </line-breakpoint>
        <line-breakpoint type="java-line">
          <url>file://$PROJECT_DIR$/src/main/java/edu/memphis/ccrg/lida/nlanguage/GrammarTask.java</url>
          <line>118</line>
          <option name="timeStamp" value="759" />
        </line-breakpoint>
        <line-breakpoint type="java-line">
          <url>file://$PROJECT_DIR$/src/main/java/edu/memphis/ccrg/lida/nlanguage/GrammarTask.java</url>
          <line>218</line>
          <option name="timeStamp" value="760" />
        </line-breakpoint>
        <line-breakpoint type="java-line">
          <url>file://$PROJECT_DIR$/src/main/java/edu/memphis/ccrg/lida/nlanguage/GrammarTask.java</url>
          <line>209</line>
          <option name="timeStamp" value="761" />
        </line-breakpoint>
        <line-breakpoint type="java-line">
          <url>file://$PROJECT_DIR$/src/main/java/edu/memphis/ccrg/lida/nlanguage/GrammarTask.java</url>
          <line>136</line>
          <option name="timeStamp" value="762" />
        </line-breakpoint>
        <line-breakpoint type="java-line">
          <url>file://$PROJECT_DIR$/src/main/java/edu/memphis/ccrg/lida/nlanguage/GrammarTask.java</url>
          <line>139</line>
          <option name="timeStamp" value="763" />
        </line-breakpoint>
        <line-breakpoint type="java-line">
          <url>file://$PROJECT_DIR$/src/main/java/edu/memphis/ccrg/lida/pam/tasks/DoMindActTask.java</url>
          <line>283</line>
          <option name="timeStamp" value="768" />
        </line-breakpoint>
        <line-breakpoint enabled="true" type="java-line">
          <url>file://$PROJECT_DIR$/src/main/java/org/opennars/language/Variable.java</url>
          <line>62</line>
          <option name="timeStamp" value="786" />
        </line-breakpoint>
        <line-breakpoint type="java-line">
          <url>file://$PROJECT_DIR$/src/main/java/edu/memphis/ccrg/lida/pam/tasks/DoSimpleSceneTask.java</url>
          <line>73</line>
          <option name="timeStamp" value="787" />
        </line-breakpoint>
        <line-breakpoint type="java-line">
          <url>file://$PROJECT_DIR$/src/main/java/org/opennars/control/concept/ProcessTask.java</url>
          <line>102</line>
          <option name="timeStamp" value="801" />
        </line-breakpoint>
        <line-breakpoint type="java-line">
          <url>file://$PROJECT_DIR$/src/main/java/edu/memphis/ccrg/lida/pam/tasks/ProcessGBufferTask.java</url>
          <line>95</line>
          <option name="timeStamp" value="804" />
        </line-breakpoint>
        <line-breakpoint type="java-line">
          <url>file://$PROJECT_DIR$/src/main/java/edu/memphis/ccrg/lida/pam/tasks/IsaPamTask.java</url>
          <line>140</line>
          <option name="timeStamp" value="809" />
        </line-breakpoint>
        <line-breakpoint type="java-line">
          <url>file://$PROJECT_DIR$/src/main/java/edu/memphis/ccrg/lida/pam/tasks/DoMindActTask.java</url>
          <line>158</line>
          <option name="timeStamp" value="813" />
        </line-breakpoint>
        <line-breakpoint type="java-line">
          <url>file://$PROJECT_DIR$/src/main/java/org/opennars/operator/FunctionOperator.java</url>
          <line>85</line>
          <option name="timeStamp" value="820" />
        </line-breakpoint>
        <line-breakpoint enabled="true" type="java-line">
          <url>file://$PROJECT_DIR$/src/main/java/org/opennars/io/Narsese.java</url>
          <line>283</line>
          <option name="timeStamp" value="826" />
        </line-breakpoint>
        <line-breakpoint type="java-line">
          <url>file://$PROJECT_DIR$/src/main/java/org/opennars/operator/FunctionOperator.java</url>
          <line>126</line>
          <option name="timeStamp" value="829" />
        </line-breakpoint>
        <line-breakpoint type="java-line">
          <url>file://$PROJECT_DIR$/src/main/java/edu/memphis/ccrg/lida/pam/tasks/DoSuccTask.java</url>
          <line>96</line>
          <option name="timeStamp" value="837" />
        </line-breakpoint>
        <line-breakpoint type="java-line">
          <url>file://$PROJECT_DIR$/src/main/java/edu/memphis/ccrg/lida/pam/tasks/DoSelectTreeTask.java</url>
          <line>222</line>
          <option name="timeStamp" value="846" />
        </line-breakpoint>
        <line-breakpoint type="java-line">
          <url>file://$PROJECT_DIR$/src/main/java/edu/memphis/ccrg/lida/pam/tasks/DoSelectTreeTask.java</url>
          <line>153</line>
          <option name="timeStamp" value="847" />
        </line-breakpoint>
        <line-breakpoint type="java-line">
          <url>file://$PROJECT_DIR$/src/main/java/edu/memphis/ccrg/lida/pam/tasks/DoSelectTreeTask.java</url>
          <line>135</line>
          <option name="timeStamp" value="848" />
        </line-breakpoint>
        <line-breakpoint type="java-line">
          <url>file://$PROJECT_DIR$/src/main/java/edu/memphis/ccrg/lida/pam/tasks/DoSelectSeqTask.java</url>
          <line>45</line>
          <option name="timeStamp" value="849" />
        </line-breakpoint>
        <line-breakpoint type="java-line">
          <url>file://$PROJECT_DIR$/src/main/java/org/opennars/operator/misc/CutStr.java</url>
          <line>94</line>
          <option name="timeStamp" value="850" />
        </line-breakpoint>
        <line-breakpoint type="java-line">
          <url>file://$PROJECT_DIR$/src/main/java/edu/memphis/ccrg/lida/pam/tasks/DoSelectTreeTask.java</url>
          <line>384</line>
          <option name="timeStamp" value="851" />
        </line-breakpoint>
        <line-breakpoint type="java-line">
          <url>file://$PROJECT_DIR$/src/main/java/edu/memphis/ccrg/lida/pam/tasks/DoMindActTask.java</url>
          <line>129</line>
          <option name="timeStamp" value="858" />
        </line-breakpoint>
        <line-breakpoint enabled="true" type="java-line">
          <url>file://$PROJECT_DIR$/src/main/java/org/opennars/language/Conjunction.java</url>
          <line>302</line>
          <option name="timeStamp" value="862" />
        </line-breakpoint>
        <line-breakpoint enabled="true" type="java-line">
          <url>file://$PROJECT_DIR$/src/main/java/org/opennars/language/Conjunction.java</url>
          <line>325</line>
          <option name="timeStamp" value="864" />
        </line-breakpoint>
        <line-breakpoint enabled="true" type="java-line">
          <url>file://$PROJECT_DIR$/src/main/java/org/opennars/language/Conjunction.java</url>
          <line>321</line>
          <option name="timeStamp" value="865" />
        </line-breakpoint>
        <line-breakpoint enabled="true" type="java-line">
          <url>file://$PROJECT_DIR$/src/main/java/org/opennars/language/Conjunction.java</url>
          <line>318</line>
          <option name="timeStamp" value="866" />
        </line-breakpoint>
        <line-breakpoint type="java-line">
          <url>file://$PROJECT_DIR$/src/main/java/edu/memphis/ccrg/lida/pam/tasks/IsaPamTask.java</url>
          <line>134</line>
          <option name="timeStamp" value="876" />
        </line-breakpoint>
        <line-breakpoint type="java-line">
          <url>file://$PROJECT_DIR$/src/main/java/edu/memphis/ccrg/lida/pam/tasks/DoSelectTreeTask.java</url>
          <line>186</line>
          <option name="timeStamp" value="881" />
        </line-breakpoint>
        <line-breakpoint type="java-line">
          <url>file://$PROJECT_DIR$/src/main/java/edu/memphis/ccrg/lida/pam/tasks/DoSelectTreeTask.java</url>
          <line>329</line>
          <option name="timeStamp" value="882" />
        </line-breakpoint>
        <line-breakpoint type="java-line">
          <url>file://$PROJECT_DIR$/src/main/java/edu/memphis/ccrg/lida/pam/tasks/DoSelectTreeTask.java</url>
          <line>396</line>
          <option name="timeStamp" value="884" />
        </line-breakpoint>
        <line-breakpoint type="java-line">
          <url>file://$PROJECT_DIR$/src/main/java/edu/memphis/ccrg/lida/pam/tasks/DoSelectTreeTask.java</url>
          <line>414</line>
          <option name="timeStamp" value="885" />
        </line-breakpoint>
        <line-breakpoint type="java-line">
          <url>file://$PROJECT_DIR$/src/main/java/edu/memphis/ccrg/linars/ProcessGoal.java</url>
          <line>1014</line>
          <option name="timeStamp" value="889" />
        </line-breakpoint>
        <line-breakpoint type="java-line">
          <url>file://$PROJECT_DIR$/src/main/java/edu/memphis/ccrg/linars/ProcessGoal.java</url>
          <line>174</line>
          <option name="timeStamp" value="894" />
        </line-breakpoint>
        <line-breakpoint type="java-line">
          <url>file://$PROJECT_DIR$/src/main/java/edu/memphis/ccrg/lida/pam/tasks/DoMindActTask.java</url>
          <line>166</line>
          <option name="timeStamp" value="896" />
        </line-breakpoint>
        <line-breakpoint type="java-line">
          <url>file://$PROJECT_DIR$/src/main/java/org/opennars/language/Conjunction.java</url>
          <line>287</line>
          <option name="timeStamp" value="904" />
        </line-breakpoint>
        <line-breakpoint type="java-line">
          <url>file://$PROJECT_DIR$/src/main/java/org/opennars/language/Conjunction.java</url>
          <line>172</line>
          <option name="timeStamp" value="905" />
        </line-breakpoint>
        <line-breakpoint type="java-line">
          <url>file://$PROJECT_DIR$/src/main/java/org/opennars/language/Conjunction.java</url>
          <line>155</line>
          <option name="timeStamp" value="907" />
        </line-breakpoint>
        <line-breakpoint type="java-line">
          <url>file://$PROJECT_DIR$/src/main/java/org/opennars/language/Conjunction.java</url>
          <line>167</line>
          <option name="timeStamp" value="909" />
        </line-breakpoint>
        <line-breakpoint type="java-line">
          <url>file://$PROJECT_DIR$/src/test/java/edu/memphis/ccrg/lida/episodicmemory/neo/Lida2narseseTest.java</url>
          <line>344</line>
          <option name="timeStamp" value="910" />
        </line-breakpoint>
        <line-breakpoint type="java-line">
          <url>file://$PROJECT_DIR$/src/main/java/edu/memphis/ccrg/lida/pam/tasks/DoSuccTask.java</url>
          <line>100</line>
          <option name="timeStamp" value="920" />
        </line-breakpoint>
        <line-breakpoint type="java-line">
          <url>file://$PROJECT_DIR$/src/main/java/edu/memphis/ccrg/lida/pam/PamImpl0.java</url>
          <line>864</line>
          <option name="timeStamp" value="921" />
        </line-breakpoint>
        <line-breakpoint type="java-line">
          <url>file://$PROJECT_DIR$/src/main/java/edu/memphis/ccrg/lida/pam/tasks/ForEachTask.java</url>
          <line>79</line>
          <option name="timeStamp" value="926" />
        </line-breakpoint>
        <line-breakpoint type="java-line">
          <url>file://$PROJECT_DIR$/src/main/java/edu/memphis/ccrg/lida/pam/tasks/DoMindActTask.java</url>
          <line>138</line>
          <option name="timeStamp" value="927" />
        </line-breakpoint>
        <line-breakpoint type="java-line">
          <url>file://$PROJECT_DIR$/src/main/java/edu/memphis/ccrg/lida/pam/tasks/DoSelectTreeTask.java</url>
          <line>99</line>
          <option name="timeStamp" value="929" />
        </line-breakpoint>
        <line-breakpoint type="java-line">
          <url>file://$PROJECT_DIR$/src/main/java/edu/memphis/ccrg/lida/pam/tasks/DoSuccTask.java</url>
          <line>108</line>
          <option name="timeStamp" value="932" />
        </line-breakpoint>
        <line-breakpoint type="java-line">
          <url>file://$PROJECT_DIR$/src/main/java/edu/memphis/ccrg/lida/pam/tasks/DoSelectTreeTask.java</url>
          <line>198</line>
          <option name="timeStamp" value="933" />
        </line-breakpoint>
        <line-breakpoint type="java-line">
          <url>file://$PROJECT_DIR$/src/main/java/edu/memphis/ccrg/lida/pam/tasks/DoSelectTreeTask.java</url>
          <line>438</line>
          <option name="timeStamp" value="934" />
        </line-breakpoint>
        <line-breakpoint type="java-line">
          <url>file://$PROJECT_DIR$/src/main/java/edu/memphis/ccrg/lida/pam/tasks/DoSelectTreeTask.java</url>
          <line>456</line>
          <option name="timeStamp" value="935" />
        </line-breakpoint>
        <line-breakpoint type="java-line">
          <url>file://$PROJECT_DIR$/src/main/java/edu/memphis/ccrg/lida/pam/tasks/DoSelectTreeTask.java</url>
          <line>616</line>
          <option name="timeStamp" value="936" />
        </line-breakpoint>
        <line-breakpoint type="java-line">
          <url>file://$PROJECT_DIR$/src/main/java/edu/memphis/ccrg/lida/pam/tasks/DoSuccTask.java</url>
          <line>115</line>
          <option name="timeStamp" value="940" />
        </line-breakpoint>
        <line-breakpoint type="java-line">
          <url>file://$PROJECT_DIR$/src/main/java/edu/memphis/ccrg/lida/pam/tasks/DoSelectTreeTask.java</url>
          <line>158</line>
          <option name="timeStamp" value="941" />
        </line-breakpoint>
        <line-breakpoint type="java-line">
          <url>file://$PROJECT_DIR$/src/main/java/edu/memphis/ccrg/lida/pam/tasks/DoSelectTreeTask.java</url>
          <line>430</line>
          <option name="timeStamp" value="942" />
        </line-breakpoint>
        <line-breakpoint type="java-line">
          <url>file://$PROJECT_DIR$/src/main/java/edu/memphis/ccrg/lida/pam/tasks/DoSelectTreeTask.java</url>
          <line>196</line>
          <option name="timeStamp" value="943" />
        </line-breakpoint>
        <line-breakpoint type="java-line">
          <url>file://$PROJECT_DIR$/src/main/java/edu/memphis/ccrg/lida/pam/tasks/DoSuccTask.java</url>
          <line>128</line>
          <option name="timeStamp" value="944" />
        </line-breakpoint>
        <line-breakpoint enabled="true" type="java-line">
          <url>file://$PROJECT_DIR$/src/main/java/org/opennars/io/Narsese.java</url>
          <line>389</line>
          <option name="timeStamp" value="949" />
        </line-breakpoint>
        <line-breakpoint enabled="true" type="java-line">
          <url>file://$PROJECT_DIR$/src/main/java/org/opennars/language/Statement.java</url>
          <line>106</line>
          <option name="timeStamp" value="950" />
        </line-breakpoint>
        <line-breakpoint enabled="true" type="java-line">
          <url>file://$PROJECT_DIR$/src/main/java/org/opennars/language/Statement.java</url>
          <line>134</line>
          <option name="timeStamp" value="951" />
        </line-breakpoint>
        <line-breakpoint type="java-line">
          <url>file://$PROJECT_DIR$/src/main/java/org/opennars/language/Statement.java</url>
          <line>128</line>
          <option name="timeStamp" value="953" />
        </line-breakpoint>
        <line-breakpoint enabled="true" type="java-line">
          <url>file://$PROJECT_DIR$/src/main/java/org/opennars/language/Equivalence.java</url>
          <line>97</line>
          <option name="timeStamp" value="954" />
        </line-breakpoint>
        <line-breakpoint type="java-line">
          <url>file://$PROJECT_DIR$/src/main/java/edu/memphis/ccrg/lida/pam/tasks/DoSimpleSceneTask.java</url>
          <line>78</line>
          <option name="timeStamp" value="957" />
        </line-breakpoint>
        <line-breakpoint type="java-line">
          <url>file://$PROJECT_DIR$/src/main/java/org/opennars/control/concept/ProcessJudgment.java</url>
          <line>155</line>
          <option name="timeStamp" value="966" />
        </line-breakpoint>
        <line-breakpoint enabled="true" type="java-line">
          <url>file://$PROJECT_DIR$/src/main/java/org/opennars/control/concept/ProcessJudgment.java</url>
          <line>161</line>
          <option name="timeStamp" value="967" />
        </line-breakpoint>
        <line-breakpoint type="java-line">
          <url>file://$PROJECT_DIR$/src/main/java/org/opennars/control/concept/ProcessJudgment.java</url>
          <line>195</line>
          <option name="timeStamp" value="972" />
        </line-breakpoint>
        <line-breakpoint type="java-line">
          <url>file://$PROJECT_DIR$/src/main/java/org/opennars/control/concept/ProcessJudgment.java</url>
          <line>202</line>
          <option name="timeStamp" value="983" />
        </line-breakpoint>
        <line-breakpoint type="java-line">
          <url>file://$PROJECT_DIR$/src/main/java/org/opennars/control/concept/ProcessJudgment.java</url>
          <line>270</line>
          <option name="timeStamp" value="987" />
        </line-breakpoint>
        <line-breakpoint enabled="true" type="java-line">
          <url>file://$PROJECT_DIR$/src/main/java/org/opennars/io/Narsese.java</url>
          <line>289</line>
          <option name="timeStamp" value="1008" />
        </line-breakpoint>
        <line-breakpoint type="java-line">
          <url>file://$PROJECT_DIR$/src/main/java/edu/memphis/ccrg/linars/CompoundTerm.java</url>
          <line>162</line>
          <option name="timeStamp" value="1016" />
        </line-breakpoint>
        <line-breakpoint type="java-line">
          <url>file://$PROJECT_DIR$/src/main/java/edu/memphis/ccrg/linars/CompoundTerm.java</url>
          <line>154</line>
          <option name="timeStamp" value="1017" />
        </line-breakpoint>
        <line-breakpoint type="java-line">
          <url>file://$PROJECT_DIR$/src/main/java/edu/memphis/ccrg/linars/ProcessGoal.java</url>
          <line>317</line>
          <option name="timeStamp" value="1033" />
        </line-breakpoint>
        <line-breakpoint type="java-line">
          <url>file://$PROJECT_DIR$/src/main/java/edu/memphis/ccrg/lida/pam/PamImpl0.java</url>
          <line>90</line>
          <option name="timeStamp" value="1035" />
        </line-breakpoint>
        <line-breakpoint type="java-line">
          <url>file://$PROJECT_DIR$/src/main/java/edu/memphis/ccrg/linars/ProcessGoal.java</url>
          <line>861</line>
          <option name="timeStamp" value="1050" />
        </line-breakpoint>
        <line-breakpoint type="java-line">
          <url>file://$PROJECT_DIR$/src/main/java/edu/memphis/ccrg/lida/pam/PamImpl0.java</url>
          <line>414</line>
          <option name="timeStamp" value="1060" />
        </line-breakpoint>
        <line-breakpoint type="java-line">
          <url>file://$PROJECT_DIR$/src/main/java/edu/memphis/ccrg/lida/pam/PamImpl0.java</url>
          <line>424</line>
          <option name="timeStamp" value="1062" />
        </line-breakpoint>
        <line-breakpoint type="java-line">
          <url>file://$PROJECT_DIR$/src/main/java/edu/memphis/ccrg/lida/pam/PamImpl0.java</url>
          <line>477</line>
          <option name="timeStamp" value="1065" />
        </line-breakpoint>
        <line-breakpoint type="java-line">
          <url>file://$PROJECT_DIR$/src/main/java/edu/memphis/ccrg/lida/pam/PamImpl0.java</url>
          <line>426</line>
          <option name="timeStamp" value="1070" />
        </line-breakpoint>
        <line-breakpoint type="java-line">
          <url>file://$PROJECT_DIR$/src/main/java/edu/memphis/ccrg/lida/pam/PamImpl0.java</url>
          <line>346</line>
          <option name="timeStamp" value="1086" />
        </line-breakpoint>
        <line-breakpoint type="java-line">
          <url>file://$PROJECT_DIR$/src/main/java/edu/memphis/ccrg/linars/ProcessGoal.java</url>
          <line>498</line>
          <option name="timeStamp" value="1091" />
        </line-breakpoint>
        <line-breakpoint type="java-line">
          <url>file://$PROJECT_DIR$/src/main/java/edu/memphis/ccrg/linars/ProcessGoal.java</url>
          <line>943</line>
          <option name="timeStamp" value="1092" />
        </line-breakpoint>
        <line-breakpoint type="java-line">
          <url>file://$PROJECT_DIR$/src/main/java/edu/memphis/ccrg/linars/ProcessGoal.java</url>
          <line>470</line>
          <option name="timeStamp" value="1096" />
        </line-breakpoint>
        <line-breakpoint type="java-line">
          <url>file://$PROJECT_DIR$/src/main/java/edu/memphis/ccrg/linars/ProcessGoal.java</url>
          <line>302</line>
          <option name="timeStamp" value="1098" />
        </line-breakpoint>
        <line-breakpoint type="java-line">
          <url>file://$PROJECT_DIR$/src/main/java/edu/memphis/ccrg/lida/pam/PamImpl0.java</url>
          <line>909</line>
          <option name="timeStamp" value="1100" />
        </line-breakpoint>
        <line-breakpoint type="java-line">
          <url>file://$PROJECT_DIR$/src/main/java/edu/memphis/ccrg/lida/pam/PamImpl0.java</url>
          <line>318</line>
          <option name="timeStamp" value="1104" />
        </line-breakpoint>
        <line-breakpoint type="java-line">
          <url>file://$PROJECT_DIR$/src/main/java/edu/memphis/ccrg/lida/pam/PamImpl0.java</url>
          <line>550</line>
          <option name="timeStamp" value="1106" />
        </line-breakpoint>
        <line-breakpoint type="java-line">
          <url>jar://$MAVEN_REPOSITORY$/org/neo4j/neo4j-kernel/4.4.26/neo4j-kernel-4.4.26.jar!/org/neo4j/kernel/impl/newapi/AllStoreHolder.class</url>
          <line>1090</line>
          <option name="timeStamp" value="1120" />
        </line-breakpoint>
        <line-breakpoint type="java-line">
          <url>jar://$MAVEN_REPOSITORY$/org/neo4j/neo4j-kernel/4.4.26/neo4j-kernel-4.4.26.jar!/org/neo4j/kernel/impl/newapi/AllStoreHolder.class</url>
          <line>1128</line>
          <option name="timeStamp" value="1121" />
        </line-breakpoint>
        <line-breakpoint type="java-line">
          <url>jar://$MAVEN_REPOSITORY$/org/neo4j/neo4j-kernel/4.4.26/neo4j-kernel-4.4.26.jar!/org/neo4j/kernel/impl/newapi/AllStoreHolder.class</url>
          <line>1036</line>
          <option name="timeStamp" value="1122" />
        </line-breakpoint>
        <line-breakpoint type="java-line">
          <url>jar://$MAVEN_REPOSITORY$/org/neo4j/neo4j-kernel/4.4.26/neo4j-kernel-4.4.26.jar!/org/neo4j/kernel/impl/newapi/AllStoreHolder.class</url>
          <line>1059</line>
          <option name="timeStamp" value="1126" />
        </line-breakpoint>
        <line-breakpoint type="java-line">
          <url>file://$PROJECT_DIR$/src/main/java/com/warmer/kgmaker/util/Neo4jUtil.java</url>
          <line>235</line>
          <option name="timeStamp" value="1128" />
        </line-breakpoint>
        <line-breakpoint type="java-line">
          <url>jar://$MAVEN_REPOSITORY$/org/neo4j/neo4j-cypher/4.4.26/neo4j-cypher-4.4.26.jar!/org/neo4j/cypher/internal/ExecutionEngine.class</url>
          <line>158</line>
          <option name="timeStamp" value="1129" />
        </line-breakpoint>
        <line-breakpoint type="java-line">
          <url>jar://$MAVEN_REPOSITORY$/org/neo4j/neo4j-cypher/4.4.26/neo4j-cypher-4.4.26.jar!/org/neo4j/cypher/internal/ExecutionEngine.class</url>
          <line>264</line>
          <option name="timeStamp" value="1135" />
        </line-breakpoint>
        <line-breakpoint type="java-line">
          <url>jar://$MAVEN_REPOSITORY$/org/neo4j/neo4j-cypher/4.4.26/neo4j-cypher-4.4.26.jar!/org/neo4j/cypher/internal/ExecutionEngine.class</url>
          <line>209</line>
          <option name="timeStamp" value="1136" />
        </line-breakpoint>
        <line-breakpoint type="java-line">
          <url>jar://$MAVEN_REPOSITORY$/org/neo4j/neo4j-cypher/4.4.26/neo4j-cypher-4.4.26.jar!/org/neo4j/cypher/internal/CypherCurrentCompiler.class</url>
          <line>836</line>
          <option name="timeStamp" value="1145" />
        </line-breakpoint>
        <line-breakpoint type="java-line">
          <url>file://$PROJECT_DIR$/src/main/java/com/warmer/kgmaker/util/Neo4jUtil.java</url>
          <line>250</line>
          <option name="timeStamp" value="1146" />
        </line-breakpoint>
        <line-breakpoint type="java-line">
          <url>jar://$MAVEN_REPOSITORY$/org/neo4j/neo4j/4.4.26/neo4j-4.4.26.jar!/org/neo4j/graphdb/factory/module/edition/CommunityEditionModule.class</url>
          <line>225</line>
          <option name="timeStamp" value="1148" />
        </line-breakpoint>
        <line-breakpoint type="java-line">
          <url>jar://$MAVEN_REPOSITORY$/org/neo4j/neo4j/4.4.26/neo4j-4.4.26.jar!/org/neo4j/graphdb/facade/DatabaseManagementServiceFactory.class</url>
          <line>123</line>
          <option name="timeStamp" value="1149" />
        </line-breakpoint>
        <line-breakpoint type="java-line">
          <url>file://$PROJECT_DIR$/src/main/java/edu/memphis/ccrg/linars/ProcessGoal.java</url>
          <line>206</line>
          <option name="timeStamp" value="1151" />
        </line-breakpoint>
        <line-breakpoint type="java-line">
          <url>file://$PROJECT_DIR$/src/main/java/edu/memphis/ccrg/linars/ProcessGoal.java</url>
          <line>205</line>
          <option name="timeStamp" value="1152" />
        </line-breakpoint>
        <line-breakpoint type="java-line">
          <url>file://$PROJECT_DIR$/src/main/java/edu/memphis/ccrg/lida/pam/PamImpl0.java</url>
          <line>320</line>
          <option name="timeStamp" value="1156" />
        </line-breakpoint>
        <line-breakpoint type="java-line">
          <url>file://$PROJECT_DIR$/src/main/java/edu/memphis/ccrg/lida/pam/PamImpl0.java</url>
          <line>486</line>
          <option name="timeStamp" value="1159" />
        </line-breakpoint>
        <line-breakpoint type="java-line">
          <url>file://$PROJECT_DIR$/src/main/java/edu/memphis/ccrg/lida/pam/PamImpl0.java</url>
          <line>447</line>
          <option name="timeStamp" value="1162" />
        </line-breakpoint>
        <line-breakpoint type="java-line">
          <url>file://$PROJECT_DIR$/src/main/java/edu/memphis/ccrg/lida/pam/PamImpl0.java</url>
          <line>466</line>
          <option name="timeStamp" value="1163" />
        </line-breakpoint>
        <line-breakpoint type="java-line">
          <url>file://$PROJECT_DIR$/src/main/java/edu/memphis/ccrg/linars/ProcessGoal.java</url>
          <line>467</line>
          <option name="timeStamp" value="1168" />
        </line-breakpoint>
        <line-breakpoint type="java-line">
          <url>file://$PROJECT_DIR$/src/main/java/edu/memphis/ccrg/lida/pam/PamImpl0.java</url>
          <line>321</line>
          <option name="timeStamp" value="1169" />
        </line-breakpoint>
        <line-breakpoint type="java-line">
          <url>file://$PROJECT_DIR$/src/main/java/edu/memphis/ccrg/lida/pam/PamImpl0.java</url>
          <line>316</line>
          <option name="timeStamp" value="1170" />
        </line-breakpoint>
        <line-breakpoint type="java-line">
          <url>file://$PROJECT_DIR$/src/main/java/edu/memphis/ccrg/linars/ProcessGoal.java</url>
          <line>442</line>
          <option name="timeStamp" value="1171" />
        </line-breakpoint>
        <line-breakpoint type="java-line">
          <url>file://$PROJECT_DIR$/src/main/java/edu/memphis/ccrg/linars/ProcessGoal.java</url>
          <line>790</line>
          <option name="timeStamp" value="1172" />
        </line-breakpoint>
        <line-breakpoint type="java-line">
          <url>file://$PROJECT_DIR$/src/main/java/edu/memphis/ccrg/linars/ProcessGoal.java</url>
          <line>778</line>
          <option name="timeStamp" value="1173" />
        </line-breakpoint>
        <line-breakpoint type="java-line">
          <url>file://$PROJECT_DIR$/src/main/java/edu/memphis/ccrg/linars/ProcessGoal.java</url>
          <line>1049</line>
          <option name="timeStamp" value="1175" />
        </line-breakpoint>
        <line-breakpoint type="java-line">
          <url>file://$PROJECT_DIR$/src/main/java/org/opennars/operator/misc/Say.java</url>
          <line>80</line>
          <option name="timeStamp" value="1176" />
        </line-breakpoint>
        <line-breakpoint type="java-line">
          <url>file://$PROJECT_DIR$/src/main/java/org/opennars/operator/misc/Search.java</url>
          <line>166</line>
          <option name="timeStamp" value="1181" />
        </line-breakpoint>
        <line-breakpoint type="java-line">
          <url>file://$PROJECT_DIR$/src/main/java/edu/memphis/ccrg/linars/ProcessGoal.java</url>
          <line>785</line>
          <option name="timeStamp" value="1183" />
        </line-breakpoint>
        <line-breakpoint type="java-line">
          <url>file://$PROJECT_DIR$/src/main/java/edu/memphis/ccrg/linars/ProcessGoal.java</url>
          <line>795</line>
          <option name="timeStamp" value="1184" />
        </line-breakpoint>
        <line-breakpoint type="java-line">
          <url>jar://$MAVEN_REPOSITORY$/org/neo4j/neo4j-configuration/4.4.26/neo4j-configuration-4.4.26.jar!/org/neo4j/configuration/Config.class</url>
          <line>902</line>
          <option name="timeStamp" value="1188" />
        </line-breakpoint>
        <line-breakpoint type="java-line">
          <url>jar://$MAVEN_REPOSITORY$/org/neo4j/neo4j-configuration/4.4.26/neo4j-configuration-4.4.26.jar!/org/neo4j/configuration/Config.class</url>
          <line>83</line>
          <option name="timeStamp" value="1190" />
        </line-breakpoint>
        <line-breakpoint type="java-line">
          <url>jar://$MAVEN_REPOSITORY$/org/neo4j/neo4j-configuration/4.4.26/neo4j-configuration-4.4.26.jar!/org/neo4j/configuration/Config.class</url>
          <line>84</line>
          <option name="timeStamp" value="1191" />
        </line-breakpoint>
        <line-breakpoint type="java-line">
          <url>jar://$MAVEN_REPOSITORY$/org/neo4j/neo4j-common/4.4.26/neo4j-common-4.4.26.jar!/org/neo4j/service/Services.class</url>
          <line>35</line>
          <option name="timeStamp" value="1193" />
        </line-breakpoint>
        <line-breakpoint type="java-line">
          <url>jar://$MAVEN_REPOSITORY$/org/neo4j/neo4j-common/4.4.26/neo4j-common-4.4.26.jar!/org/neo4j/service/Services.class</url>
          <line>36</line>
          <option name="timeStamp" value="1194" />
        </line-breakpoint>
        <line-breakpoint type="java-line">
          <url>jar://$MAVEN_REPOSITORY$/org/neo4j/neo4j-common/4.4.26/neo4j-common-4.4.26.jar!/org/neo4j/service/Services.class</url>
          <line>82</line>
          <option name="timeStamp" value="1196" />
        </line-breakpoint>
        <line-breakpoint type="java-line">
          <url>jar://$MAVEN_REPOSITORY$/org/neo4j/neo4j-common/4.4.26/neo4j-common-4.4.26.jar!/org/neo4j/service/Services.class</url>
          <line>94</line>
          <option name="timeStamp" value="1197" />
        </line-breakpoint>
        <line-breakpoint type="java-line">
          <url>jar://$MAVEN_REPOSITORY$/org/neo4j/neo4j-common/4.4.26/neo4j-common-4.4.26.jar!/org/neo4j/service/Services.class</url>
          <line>34</line>
          <option name="timeStamp" value="1198" />
        </line-breakpoint>
        <line-breakpoint type="java-line">
          <url>file://$PROJECT_DIR$/src/main/java/edu/memphis/ccrg/lida/data/NeoUtil.java</url>
          <line>526</line>
          <option name="timeStamp" value="1202" />
        </line-breakpoint>
        <line-breakpoint enabled="true" type="java-line">
          <url>file://$PROJECT_DIR$/src/main/java/edu/memphis/ccrg/lida/data/NeoUtil.java</url>
          <line>665</line>
          <option name="timeStamp" value="1204" />
        </line-breakpoint>
        <line-breakpoint type="java-line">
          <url>file://$PROJECT_DIR$/src/main/java/org/opennars/operator/misc/Learn.java</url>
          <line>57</line>
          <option name="timeStamp" value="1217" />
        </line-breakpoint>
        <line-breakpoint enabled="true" type="java-line">
          <url>file://$PROJECT_DIR$/src/main/java/com/warmer/kgmaker/util/Neo4jUtil.java</url>
          <line>448</line>
          <option name="timeStamp" value="1221" />
        </line-breakpoint>
        <line-breakpoint enabled="true" type="java-line">
          <url>file://$PROJECT_DIR$/src/main/java/com/warmer/kgmaker/util/Neo4jUtil.java</url>
          <line>446</line>
          <option name="timeStamp" value="1222" />
        </line-breakpoint>
        <line-breakpoint enabled="true" type="java-line">
          <url>file://$PROJECT_DIR$/src/main/java/edu/memphis/ccrg/lida/framework/shared/LinkImpl.java</url>
          <line>637</line>
          <option name="timeStamp" value="1224" />
        </line-breakpoint>
        <line-breakpoint type="java-line">
          <url>file://$PROJECT_DIR$/src/main/java/com/warmer/kgmaker/util/Neo4jUtil.java</url>
          <line>286</line>
          <option name="timeStamp" value="1229" />
        </line-breakpoint>
        <line-breakpoint type="java-line">
          <url>file://$PROJECT_DIR$/src/main/java/com/warmer/kgmaker/util/Neo4jUtil.java</url>
          <line>270</line>
          <option name="timeStamp" value="1231" />
        </line-breakpoint>
        <line-breakpoint enabled="true" type="java-line">
          <url>file://$PROJECT_DIR$/src/main/java/edu/memphis/ccrg/lida/pam/tasks/LearnTask.java</url>
          <line>104</line>
          <option name="timeStamp" value="1232" />
        </line-breakpoint>
        <line-breakpoint enabled="true" type="java-line">
          <url>file://$PROJECT_DIR$/src/main/java/edu/memphis/ccrg/lida/pam/tasks/LearnTask.java</url>
          <line>99</line>
          <option name="timeStamp" value="1235" />
        </line-breakpoint>
        <line-breakpoint enabled="true" type="java-line">
          <url>file://$PROJECT_DIR$/src/main/java/edu/memphis/ccrg/lida/pam/tasks/CreateActRootTask0.java</url>
          <line>187</line>
          <option name="timeStamp" value="1250" />
        </line-breakpoint>
        <line-breakpoint enabled="true" type="java-line">
          <url>file://$PROJECT_DIR$/src/main/java/edu/memphis/ccrg/lida/pam/tasks/CreateActRootTask0.java</url>
          <line>208</line>
          <option name="timeStamp" value="1251" />
        </line-breakpoint>
        <line-breakpoint enabled="true" type="java-line">
          <url>file://$PROJECT_DIR$/src/main/java/edu/memphis/ccrg/lida/pam/tasks/CreateActRootTask0.java</url>
          <line>124</line>
          <option name="timeStamp" value="1259" />
        </line-breakpoint>
        <line-breakpoint enabled="true" type="java-line">
          <url>file://$PROJECT_DIR$/src/main/java/edu/memphis/ccrg/lida/pam/tasks/CreateActRootTask0.java</url>
          <line>130</line>
          <option name="timeStamp" value="1260" />
        </line-breakpoint>
        <line-breakpoint enabled="true" type="java-line">
          <url>file://$PROJECT_DIR$/src/main/java/edu/memphis/ccrg/lida/pam/tasks/CreateActRootTask0.java</url>
          <line>133</line>
          <option name="timeStamp" value="1261" />
        </line-breakpoint>
        <line-breakpoint enabled="true" type="java-line">
          <url>file://$PROJECT_DIR$/src/main/java/edu/memphis/ccrg/lida/pam/tasks/CreateSimpleSceneTask.java</url>
          <line>77</line>
          <option name="timeStamp" value="1265" />
        </line-breakpoint>
        <line-breakpoint enabled="true" type="java-line">
          <url>file://$PROJECT_DIR$/src/main/java/edu/memphis/ccrg/lida/pam/tasks/CreateSimpleSceneTask.java</url>
          <line>90</line>
          <option name="timeStamp" value="1266" />
        </line-breakpoint>
        <line-breakpoint enabled="true" type="java-line">
          <url>file://$PROJECT_DIR$/src/main/java/edu/memphis/ccrg/lida/pam/tasks/CreateSuccTask.java</url>
          <line>73</line>
          <option name="timeStamp" value="1267" />
        </line-breakpoint>
        <line-breakpoint type="java-line">
          <url>file://$PROJECT_DIR$/src/main/java/com/warmer/kgmaker/util/Neo4jUtil.java</url>
          <line>304</line>
          <option name="timeStamp" value="1268" />
        </line-breakpoint>
        <line-breakpoint enabled="true" type="java-line">
          <url>file://$PROJECT_DIR$/src/main/java/com/warmer/kgmaker/util/Neo4jUtil.java</url>
          <line>83</line>
          <option name="timeStamp" value="1271" />
        </line-breakpoint>
        <line-breakpoint enabled="true" type="java-line">
          <url>file://$PROJECT_DIR$/src/main/java/com/warmer/kgmaker/dal/impl/KGraphRepository.java</url>
          <line>158</line>
          <option name="timeStamp" value="1272" />
        </line-breakpoint>
        <line-breakpoint enabled="true" type="java-line">
          <url>file://$PROJECT_DIR$/src/test/java/some/csv2neo.java</url>
          <line>118</line>
          <option name="timeStamp" value="1278" />
        </line-breakpoint>
        <line-breakpoint enabled="true" type="java-line">
          <url>file://$PROJECT_DIR$/src/test/java/some/csv2neo.java</url>
          <line>135</line>
          <option name="timeStamp" value="1279" />
        </line-breakpoint>
        <line-breakpoint enabled="true" type="java-line">
          <url>file://$PROJECT_DIR$/src/test/java/some/csv2neo.java</url>
          <line>165</line>
          <option name="timeStamp" value="1280" />
        </line-breakpoint>
        <line-breakpoint enabled="true" type="java-line">
          <url>file://$PROJECT_DIR$/src/test/java/some/csv2neo.java</url>
          <line>78</line>
          <option name="timeStamp" value="1281" />
        </line-breakpoint>
        <line-breakpoint enabled="true" type="java-line">
          <url>file://$PROJECT_DIR$/src/test/java/some/TreeConverter.java</url>
          <line>68</line>
          <option name="timeStamp" value="1282" />
        </line-breakpoint>
        <line-breakpoint type="java-line">
          <url>file://$PROJECT_DIR$/src/main/java/org/opennars/operator/misc/Say.java</url>
          <line>64</line>
          <option name="timeStamp" value="1283" />
        </line-breakpoint>
        <line-breakpoint type="java-line">
          <url>file://$PROJECT_DIR$/src/main/java/org/opennars/operator/misc/Search.java</url>
          <line>63</line>
          <option name="timeStamp" value="1284" />
        </line-breakpoint>
        <line-breakpoint type="java-line">
          <url>file://$PROJECT_DIR$/src/main/java/edu/memphis/ccrg/lida/pam/PamImpl0.java</url>
          <line>922</line>
          <option name="timeStamp" value="1287" />
        </line-breakpoint>
        <line-breakpoint type="java-line">
          <url>file://$PROJECT_DIR$/src/main/java/edu/memphis/ccrg/linars/ProcessGoal.java</url>
          <line>822</line>
          <option name="timeStamp" value="1290" />
        </line-breakpoint>
        <line-breakpoint type="java-line">
          <url>file://$PROJECT_DIR$/src/main/java/edu/memphis/ccrg/linars/ProcessGoal.java</url>
          <line>456</line>
          <option name="timeStamp" value="1291" />
        </line-breakpoint>
        <line-breakpoint type="java-line">
          <url>file://$PROJECT_DIR$/src/main/java/edu/memphis/ccrg/linars/ProcessGoal.java</url>
          <line>155</line>
          <option name="timeStamp" value="1293" />
        </line-breakpoint>
        <line-breakpoint type="java-line">
          <url>file://$PROJECT_DIR$/src/main/java/edu/memphis/ccrg/linars/ProcessGoal.java</url>
          <line>125</line>
          <option name="timeStamp" value="1297" />
        </line-breakpoint>
        <line-breakpoint type="java-line">
          <url>file://$PROJECT_DIR$/src/main/java/edu/memphis/ccrg/linars/ProcessGoal.java</url>
          <line>113</line>
          <option name="timeStamp" value="1298" />
        </line-breakpoint>
        <line-breakpoint enabled="true" type="java-line">
          <url>file://$PROJECT_DIR$/src/main/java/edu/memphis/ccrg/linars/Memory.java</url>
          <line>433</line>
          <option name="timeStamp" value="1305" />
        </line-breakpoint>
        <line-breakpoint enabled="true" type="java-line">
          <url>file://$PROJECT_DIR$/src/main/java/edu/memphis/ccrg/linars/Memory.java</url>
          <line>425</line>
          <option name="timeStamp" value="1306" />
        </line-breakpoint>
        <line-breakpoint type="java-line">
          <url>file://$PROJECT_DIR$/src/main/java/org/opennars/control/concept/ProcessJudgment.java</url>
          <line>247</line>
          <option name="timeStamp" value="1332" />
        </line-breakpoint>
        <line-breakpoint type="java-line">
          <url>file://$PROJECT_DIR$/src/main/java/org/opennars/control/concept/ProcessJudgment.java</url>
          <line>250</line>
          <option name="timeStamp" value="1333" />
        </line-breakpoint>
        <line-breakpoint type="java-line">
          <url>file://$PROJECT_DIR$/src/main/java/edu/memphis/ccrg/linars/ProcessGoal.java</url>
          <line>211</line>
          <option name="timeStamp" value="1334" />
        </line-breakpoint>
        <line-breakpoint type="java-line">
          <url>file://$PROJECT_DIR$/src/main/java/edu/memphis/ccrg/linars/ProcessGoal.java</url>
          <line>107</line>
          <option name="timeStamp" value="1335" />
        </line-breakpoint>
        <line-breakpoint type="java-line">
          <url>file://$PROJECT_DIR$/src/main/java/edu/memphis/ccrg/lida/pam/tasks/ForEachTask.java</url>
          <line>74</line>
          <option name="timeStamp" value="1348" />
        </line-breakpoint>
        <line-breakpoint type="java-line">
          <url>file://$PROJECT_DIR$/src/main/java/edu/memphis/ccrg/lida/pam/PamImpl0.java</url>
          <line>867</line>
          <option name="timeStamp" value="1349" />
        </line-breakpoint>
        <line-breakpoint type="java-line">
          <url>file://$PROJECT_DIR$/src/main/java/edu/memphis/ccrg/lida/pam/tasks/DoMindActTask.java</url>
          <line>136</line>
          <option name="timeStamp" value="1362" />
        </line-breakpoint>
        <line-breakpoint type="java-line">
          <url>file://$PROJECT_DIR$/src/main/java/edu/memphis/ccrg/lida/pam/tasks/DoSuccTask.java</url>
          <line>158</line>
          <option name="timeStamp" value="1363" />
        </line-breakpoint>
        <line-breakpoint enabled="true" type="java-line">
          <url>jar://$MAVEN_REPOSITORY$/org/neo4j/neo4j-graph-algo/5.16.0/neo4j-graph-algo-5.16.0-sources.jar!/org/neo4j/graphalgo/impl/path/Dijkstra.java</url>
          <line>117</line>
          <option name="timeStamp" value="1381" />
        </line-breakpoint>
        <line-breakpoint enabled="true" type="java-line">
          <url>jar://$MAVEN_REPOSITORY$/org/neo4j/neo4j-graph-algo/5.16.0/neo4j-graph-algo-5.16.0-sources.jar!/org/neo4j/graphalgo/impl/path/Dijkstra.java</url>
          <line>96</line>
          <option name="timeStamp" value="1382" />
        </line-breakpoint>
        <line-breakpoint enabled="true" type="java-line">
          <url>jar://$MAVEN_REPOSITORY$/org/neo4j/neo4j-graph-algo/5.16.0/neo4j-graph-algo-5.16.0-sources.jar!/org/neo4j/graphalgo/GraphAlgoFactory.java</url>
          <line>58</line>
          <option name="timeStamp" value="1383" />
        </line-breakpoint>
        <line-breakpoint enabled="true" type="java-line">
          <url>jar://$MAVEN_REPOSITORY$/org/neo4j/neo4j-graph-algo/5.16.0/neo4j-graph-algo-5.16.0-sources.jar!/org/neo4j/graphalgo/GraphAlgoFactory.java</url>
          <line>92</line>
          <option name="timeStamp" value="1384" />
        </line-breakpoint>
        <line-breakpoint enabled="true" type="java-line">
          <url>file://$PROJECT_DIR$/src/main/java/edu/memphis/ccrg/lida/data/NeoUtil.java</url>
          <line>555</line>
          <option name="timeStamp" value="1385" />
        </line-breakpoint>
        <line-breakpoint enabled="true" type="java-line">
          <url>jar://$MAVEN_REPOSITORY$/org/neo4j/neo4j-graph-algo/5.16.0/neo4j-graph-algo-5.16.0-sources.jar!/org/neo4j/graphalgo/impl/util/WeightedPathImpl.java</url>
          <line>44</line>
          <option name="timeStamp" value="1386" />
        </line-breakpoint>
        <line-breakpoint enabled="true" type="java-line">
          <url>jar://$MAVEN_REPOSITORY$/org/neo4j/neo4j-graph-algo/5.16.0/neo4j-graph-algo-5.16.0-sources.jar!/org/neo4j/graphalgo/impl/path/AllPaths.java</url>
          <line>36</line>
          <option name="timeStamp" value="1387" />
        </line-breakpoint>
        <line-breakpoint enabled="true" type="java-line">
          <url>jar://$MAVEN_REPOSITORY$/org/neo4j/neo4j-graph-algo/5.16.0/neo4j-graph-algo-5.16.0-sources.jar!/org/neo4j/graphalgo/impl/path/AllSimplePaths.java</url>
          <line>32</line>
          <option name="timeStamp" value="1388" />
        </line-breakpoint>
        <line-breakpoint enabled="true" type="java-line">
          <url>jar://$MAVEN_REPOSITORY$/org/neo4j/neo4j-graph-algo/5.16.0/neo4j-graph-algo-5.16.0-sources.jar!/org/neo4j/graphalgo/impl/path/ShortestPath.java</url>
          <line>113</line>
          <option name="timeStamp" value="1389" />
        </line-breakpoint>
        <line-breakpoint enabled="true" type="java-line">
          <url>jar://$MAVEN_REPOSITORY$/org/neo4j/neo4j-graph-algo/5.16.0/neo4j-graph-algo-5.16.0-sources.jar!/org/neo4j/graphalgo/impl/path/ShortestPath.java</url>
          <line>238</line>
          <option name="timeStamp" value="1390" />
        </line-breakpoint>
        <line-breakpoint enabled="true" type="java-line">
          <url>jar://$MAVEN_REPOSITORY$/org/neo4j/neo4j-graph-algo/5.16.0/neo4j-graph-algo-5.16.0-sources.jar!/org/neo4j/graphalgo/impl/path/TraversalPathFinder.java</url>
          <line>43</line>
          <option name="timeStamp" value="1391" />
        </line-breakpoint>
        <line-breakpoint enabled="true" type="java-line">
          <url>jar://$MAVEN_REPOSITORY$/org/neo4j/neo4j-graph-algo/5.16.0/neo4j-graph-algo-5.16.0-sources.jar!/org/neo4j/graphalgo/impl/shortestpath/Dijkstra.java</url>
          <line>240</line>
          <option name="timeStamp" value="1392" />
        </line-breakpoint>
        <line-breakpoint enabled="true" type="java-line">
          <url>jar://$MAVEN_REPOSITORY$/org/neo4j/neo4j-graph-algo/5.16.0/neo4j-graph-algo-5.16.0-sources.jar!/org/neo4j/graphalgo/impl/util/BestFirstSelectorFactory.java</url>
          <line>42</line>
          <option name="timeStamp" value="1393" />
        </line-breakpoint>
        <line-breakpoint enabled="true" type="java-line">
          <url>jar://$MAVEN_REPOSITORY$/org/neo4j/neo4j-graph-algo/5.16.0/neo4j-graph-algo-5.16.0-sources.jar!/org/neo4j/graphalgo/impl/util/DijkstraBranchCollisionDetector.java</url>
          <line>36</line>
          <option name="timeStamp" value="1394" />
        </line-breakpoint>
        <line-breakpoint enabled="true" type="java-line">
          <url>jar://$MAVEN_REPOSITORY$/org/neo4j/neo4j-graph-algo/5.16.0/neo4j-graph-algo-5.16.0-sources.jar!/org/neo4j/graphalgo/impl/util/PathImpl.java</url>
          <line>47</line>
          <option name="timeStamp" value="1396" />
        </line-breakpoint>
        <line-breakpoint enabled="true" type="java-line">
          <url>jar://$MAVEN_REPOSITORY$/org/neo4j/neo4j-graph-algo/5.16.0/neo4j-graph-algo-5.16.0-sources.jar!/org/neo4j/graphalgo/impl/util/PathInterest.java</url>
          <line>68</line>
          <option name="timeStamp" value="1397" />
        </line-breakpoint>
        <line-breakpoint enabled="true" type="java-line">
          <url>jar://$MAVEN_REPOSITORY$/org/neo4j/neo4j-graph-algo/5.16.0/neo4j-graph-algo-5.16.0-sources.jar!/org/neo4j/graphalgo/impl/path/TraversalShortestPath.java</url>
          <line>45</line>
          <option name="timeStamp" value="1401" />
        </line-breakpoint>
        <line-breakpoint enabled="true" type="java-line">
          <url>jar://$MAVEN_REPOSITORY$/org/neo4j/neo4j-graph-algo/5.16.0/neo4j-graph-algo-5.16.0-sources.jar!/org/neo4j/graphalgo/impl/path/TraversalShortestPath.java</url>
          <line>50</line>
          <option name="timeStamp" value="1402" />
        </line-breakpoint>
        <line-breakpoint enabled="true" type="java-line">
          <url>jar://$MAVEN_REPOSITORY$/org/neo4j/neo4j-graph-algo/5.16.0/neo4j-graph-algo-5.16.0-sources.jar!/org/neo4j/graphalgo/impl/shortestpath/Dijkstra.java</url>
          <line>79</line>
          <option name="timeStamp" value="1403" />
        </line-breakpoint>
        <line-breakpoint enabled="true" type="java-line">
          <url>jar://$MAVEN_REPOSITORY$/org/neo4j/neo4j-graph-algo/5.16.0/neo4j-graph-algo-5.16.0-sources.jar!/org/neo4j/graphalgo/impl/path/ShortestPath.java</url>
          <line>104</line>
          <option name="timeStamp" value="1404" />
        </line-breakpoint>
        <line-breakpoint enabled="true" type="java-line">
          <url>jar://$MAVEN_REPOSITORY$/org/neo4j/neo4j-graph-algo/5.16.0/neo4j-graph-algo-5.16.0-sources.jar!/org/neo4j/graphalgo/impl/path/ShortestPath.java</url>
          <line>118</line>
          <option name="timeStamp" value="1405" />
        </line-breakpoint>
        <line-breakpoint enabled="true" type="java-line">
          <url>jar://$MAVEN_REPOSITORY$/org/neo4j/neo4j-graph-algo/5.16.0/neo4j-graph-algo-5.16.0-sources.jar!/org/neo4j/graphalgo/impl/path/TraversalShortestPath.java</url>
          <line>62</line>
          <option name="timeStamp" value="1406" />
        </line-breakpoint>
        <line-breakpoint enabled="true" type="java-line">
          <url>jar://$MAVEN_REPOSITORY$/org/neo4j/neo4j-graph-algo/5.16.0/neo4j-graph-algo-5.16.0-sources.jar!/org/neo4j/graphalgo/impl/path/AStar.java</url>
          <line>166</line>
          <option name="timeStamp" value="1408" />
        </line-breakpoint>
        <line-breakpoint enabled="true" type="java-line">
          <url>jar://$MAVEN_REPOSITORY$/org/neo4j/neo4j-graph-algo/5.16.0/neo4j-graph-algo-5.16.0-sources.jar!/org/neo4j/graphalgo/impl/path/AStar.java</url>
          <line>186</line>
          <option name="timeStamp" value="1409" />
        </line-breakpoint>
        <line-breakpoint enabled="true" type="java-line">
          <url>jar://$MAVEN_REPOSITORY$/org/neo4j/neo4j-graph-algo/5.16.0/neo4j-graph-algo-5.16.0-sources.jar!/org/neo4j/graphalgo/impl/path/Dijkstra.java</url>
          <line>87</line>
          <option name="timeStamp" value="1410" />
        </line-breakpoint>
        <line-breakpoint enabled="true" type="java-line">
          <url>jar://$MAVEN_REPOSITORY$/org/neo4j/neo4j-graph-algo/5.16.0/neo4j-graph-algo-5.16.0-sources.jar!/org/neo4j/graphalgo/impl/path/Dijkstra.java</url>
          <line>107</line>
          <option name="timeStamp" value="1411" />
        </line-breakpoint>
        <line-breakpoint enabled="true" type="java-line">
          <url>jar://$MAVEN_REPOSITORY$/org/neo4j/neo4j-graph-algo/5.16.0/neo4j-graph-algo-5.16.0-sources.jar!/org/neo4j/graphalgo/impl/path/Dijkstra.java</url>
          <line>176</line>
          <option name="timeStamp" value="1412" />
        </line-breakpoint>
        <line-breakpoint enabled="true" type="java-line">
          <url>jar://$MAVEN_REPOSITORY$/org/neo4j/neo4j-graph-algo/5.16.0/neo4j-graph-algo-5.16.0-sources.jar!/org/neo4j/graphalgo/impl/util/WeightedPathIterator.java</url>
          <line>47</line>
          <option name="timeStamp" value="1413" />
        </line-breakpoint>
        <line-breakpoint enabled="true" type="java-line">
          <url>jar://$MAVEN_REPOSITORY$/org/neo4j/neo4j-graph-algo/5.16.0/neo4j-graph-algo-5.16.0-sources.jar!/org/neo4j/graphalgo/BasicEvaluationContext.java</url>
          <line>32</line>
          <option name="timeStamp" value="1414" />
        </line-breakpoint>
        <line-breakpoint type="java-line">
          <url>jar://$MAVEN_REPOSITORY$/org/neo4j/neo4j-graphdb-api/5.16.0/neo4j-graphdb-api-5.16.0.jar!/org/neo4j/graphdb/PathExpanderBuilder.class</url>
          <line>17</line>
          <option name="timeStamp" value="1417" />
        </line-breakpoint>
        <line-breakpoint type="java-line">
          <url>jar://$MAVEN_REPOSITORY$/org/neo4j/neo4j-graphdb-api/5.16.0/neo4j-graphdb-api-5.16.0.jar!/org/neo4j/graphdb/PathExpanderBuilder.class</url>
          <line>21</line>
          <option name="timeStamp" value="1418" />
        </line-breakpoint>
        <line-breakpoint type="java-line">
          <url>jar://$MAVEN_REPOSITORY$/org/neo4j/neo4j-graphdb-api/5.16.0/neo4j-graphdb-api-5.16.0.jar!/org/neo4j/graphdb/PathExpanderBuilder.class</url>
          <line>25</line>
          <option name="timeStamp" value="1419" />
        </line-breakpoint>
        <line-breakpoint type="java-line">
          <url>jar://$MAVEN_REPOSITORY$/org/neo4j/neo4j-graphdb-api/5.16.0/neo4j-graphdb-api-5.16.0.jar!/org/neo4j/graphdb/PathExpanderBuilder.class</url>
          <line>29</line>
          <option name="timeStamp" value="1420" />
        </line-breakpoint>
        <line-breakpoint type="java-line">
          <url>jar://$MAVEN_REPOSITORY$/org/neo4j/neo4j-graphdb-api/5.16.0/neo4j-graphdb-api-5.16.0.jar!/org/neo4j/graphdb/PathExpanders.class</url>
          <line>21</line>
          <option name="timeStamp" value="1421" />
        </line-breakpoint>
        <line-breakpoint type="java-line">
          <url>jar://$MAVEN_REPOSITORY$/org/neo4j/neo4j-graphdb-api/5.16.0/neo4j-graphdb-api-5.16.0.jar!/org/neo4j/graphdb/QueryExecutionException.class</url>
          <line>14</line>
          <option name="timeStamp" value="1422" />
        </line-breakpoint>
        <line-breakpoint type="java-line">
          <url>jar://$MAVEN_REPOSITORY$/org/neo4j/neo4j-graphdb-api/5.16.0/neo4j-graphdb-api-5.16.0.jar!/org/neo4j/graphdb/QueryExecutionType.class</url>
          <line>16</line>
          <option name="timeStamp" value="1423" />
        </line-breakpoint>
        <line-breakpoint type="java-line">
          <url>jar://$MAVEN_REPOSITORY$/org/neo4j/neo4j-graphdb-api/5.16.0/neo4j-graphdb-api-5.16.0.jar!/org/neo4j/graphdb/traversal/Evaluators.class</url>
          <line>40</line>
          <option name="timeStamp" value="1426" />
        </line-breakpoint>
        <line-breakpoint type="java-line">
          <url>jar://$MAVEN_REPOSITORY$/org/neo4j/neo4j-graphdb-api/5.16.0/neo4j-graphdb-api-5.16.0.jar!/org/neo4j/graphdb/traversal/Evaluators.class</url>
          <line>38</line>
          <option name="timeStamp" value="1427" />
        </line-breakpoint>
        <line-breakpoint type="java-line">
          <url>jar://$MAVEN_REPOSITORY$/org/neo4j/neo4j-graphdb-api/5.16.0/neo4j-graphdb-api-5.16.0.jar!/org/neo4j/graphdb/traversal/Evaluator.class</url>
          <line>22</line>
          <option name="timeStamp" value="1428" />
        </line-breakpoint>
        <line-breakpoint type="java-line">
          <url>jar://$MAVEN_REPOSITORY$/org/neo4j/neo4j-graphdb-api/5.16.0/neo4j-graphdb-api-5.16.0.jar!/org/neo4j/graphdb/traversal/Evaluation.class</url>
          <line>33</line>
          <option name="timeStamp" value="1429" />
        </line-breakpoint>
        <line-breakpoint type="java-line">
          <url>jar://$MAVEN_REPOSITORY$/org/neo4j/neo4j-graphdb-api/5.16.0/neo4j-graphdb-api-5.16.0.jar!/org/neo4j/graphdb/impl/ExtendedPath.class</url>
          <line>20</line>
          <option name="timeStamp" value="1430" />
        </line-breakpoint>
        <line-breakpoint type="java-line">
          <url>jar://$MAVEN_REPOSITORY$/org/neo4j/neo4j-graphdb-api/5.16.0/neo4j-graphdb-api-5.16.0.jar!/org/neo4j/graphdb/impl/ExtendedPath.class</url>
          <line>38</line>
          <option name="timeStamp" value="1431" />
        </line-breakpoint>
        <line-breakpoint type="java-line">
          <url>jar://$MAVEN_REPOSITORY$/org/neo4j/neo4j-graphdb-api/5.16.0/neo4j-graphdb-api-5.16.0.jar!/org/neo4j/graphdb/impl/ExtendedPath.class</url>
          <line>48</line>
          <option name="timeStamp" value="1432" />
        </line-breakpoint>
        <line-breakpoint enabled="true" type="java-line">
          <url>jar://$MAVEN_REPOSITORY$/org/neo4j/neo4j-graph-algo/5.16.0/neo4j-graph-algo-5.16.0-sources.jar!/org/neo4j/graphalgo/impl/util/PathImpl.java</url>
          <line>68</line>
          <option name="timeStamp" value="1434" />
        </line-breakpoint>
        <line-breakpoint enabled="true" type="java-line">
          <url>file://$PROJECT_DIR$/src/main/java/edu/memphis/ccrg/lida/data/NeoUtil.java</url>
          <line>553</line>
          <option name="timeStamp" value="1440" />
        </line-breakpoint>
        <line-breakpoint type="java-line">
          <url>jar://$MAVEN_REPOSITORY$/org/neo4j/neo4j-cypher/5.16.0/neo4j-cypher-5.16.0.jar!/org/neo4j/cypher/internal/ExecutionEngine.class</url>
          <line>139</line>
          <option name="timeStamp" value="1441" />
        </line-breakpoint>
        <line-breakpoint type="java-line">
          <url>jar://$MAVEN_REPOSITORY$/org/neo4j/neo4j-cypher/5.16.0/neo4j-cypher-5.16.0.jar!/org/neo4j/cypher/internal/ExecutionEngine.class</url>
          <line>138</line>
          <option name="timeStamp" value="1442" />
        </line-breakpoint>
        <line-breakpoint type="java-line">
          <url>jar://$MAVEN_REPOSITORY$/org/neo4j/neo4j-cypher/5.16.0/neo4j-cypher-5.16.0.jar!/org/neo4j/cypher/internal/ExecutionEngine.class</url>
          <line>205</line>
          <option name="timeStamp" value="1443" />
        </line-breakpoint>
        <line-breakpoint type="java-line">
          <url>jar://$MAVEN_REPOSITORY$/org/neo4j/neo4j-cypher/5.16.0/neo4j-cypher-5.16.0.jar!/org/neo4j/cypher/internal/CypherCurrentCompiler.class</url>
          <line>684</line>
          <option name="timeStamp" value="1444" />
        </line-breakpoint>
        <line-breakpoint type="java-line">
          <url>jar://$MAVEN_REPOSITORY$/org/neo4j/neo4j-cypher/5.16.0/neo4j-cypher-5.16.0.jar!/org/neo4j/cypher/internal/CypherCurrentCompiler.class</url>
          <line>685</line>
          <option name="timeStamp" value="1445" />
        </line-breakpoint>
        <line-breakpoint type="java-line">
          <url>jar://$MAVEN_REPOSITORY$/org/neo4j/neo4j-cypher/5.16.0/neo4j-cypher-5.16.0.jar!/org/neo4j/cypher/internal/CypherCurrentCompiler.class</url>
          <line>683</line>
          <option name="timeStamp" value="1446" />
        </line-breakpoint>
        <line-breakpoint type="java-line">
          <url>jar://$MAVEN_REPOSITORY$/org/neo4j/neo4j-cypher/5.16.0/neo4j-cypher-5.16.0.jar!/org/neo4j/cypher/internal/CypherCurrentCompiler.class</url>
          <line>696</line>
          <option name="timeStamp" value="1447" />
        </line-breakpoint>
        <line-breakpoint type="java-line">
          <url>jar://$MAVEN_REPOSITORY$/org/neo4j/neo4j-cypher/5.16.0/neo4j-cypher-5.16.0.jar!/org/neo4j/cypher/internal/CypherCurrentCompiler.class</url>
          <line>695</line>
          <option name="timeStamp" value="1448" />
        </line-breakpoint>
        <line-breakpoint type="java-line">
          <url>jar://$MAVEN_REPOSITORY$/org/neo4j/neo4j-cypher/5.16.0/neo4j-cypher-5.16.0.jar!/org/neo4j/cypher/internal/CypherCurrentCompiler.class</url>
          <line>697</line>
          <option name="timeStamp" value="1449" />
        </line-breakpoint>
        <line-breakpoint type="java-line">
          <url>file://$PROJECT_DIR$/src/main/java/edu/memphis/ccrg/lida/pam/tasks/GoalBackgroundTask.java</url>
          <line>217</line>
          <option name="timeStamp" value="1451" />
        </line-breakpoint>
        <line-breakpoint type="java-line">
          <url>file://$PROJECT_DIR$/src/main/java/edu/memphis/ccrg/linars/Memory.java</url>
          <line>232</line>
          <option name="timeStamp" value="1460" />
        </line-breakpoint>
        <line-breakpoint type="java-line">
          <url>file://$PROJECT_DIR$/src/main/java/edu/memphis/ccrg/linars/Memory.java</url>
          <line>490</line>
          <option name="timeStamp" value="1463" />
        </line-breakpoint>
        <line-breakpoint type="java-line">
          <url>file://$PROJECT_DIR$/src/main/java/edu/memphis/ccrg/lida/pam/tasks/GoalBackgroundTask.java</url>
          <line>234</line>
          <option name="timeStamp" value="1471" />
        </line-breakpoint>
        <line-breakpoint type="java-line">
          <url>file://$PROJECT_DIR$/src/main/java/edu/memphis/ccrg/lida/pam/tasks/GoalBackgroundTask.java</url>
          <line>292</line>
          <option name="timeStamp" value="1472" />
        </line-breakpoint>
        <line-breakpoint type="java-line">
          <url>file://$PROJECT_DIR$/src/main/java/edu/memphis/ccrg/lida/pam/tasks/GoalBackgroundTask.java</url>
          <line>288</line>
          <option name="timeStamp" value="1475" />
        </line-breakpoint>
        <line-breakpoint type="java-line">
          <url>file://$PROJECT_DIR$/src/main/java/edu/memphis/ccrg/linars/ProcessGoal.java</url>
          <line>321</line>
          <option name="timeStamp" value="1477" />
        </line-breakpoint>
        <line-breakpoint type="java-line">
          <url>file://$PROJECT_DIR$/src/main/java/edu/memphis/ccrg/lida/pam/tasks/GoalBackgroundTask.java</url>
          <line>153</line>
          <option name="timeStamp" value="1484" />
        </line-breakpoint>
        <line-breakpoint type="java-line">
          <url>file://$PROJECT_DIR$/src/main/java/edu/memphis/ccrg/linars/ProcessGoal.java</url>
          <line>469</line>
          <option name="timeStamp" value="1487" />
        </line-breakpoint>
        <line-breakpoint type="java-line">
          <url>file://$PROJECT_DIR$/src/main/java/edu/memphis/ccrg/lida/pam/PamImpl0.java</url>
          <line>315</line>
          <option name="timeStamp" value="1489" />
        </line-breakpoint>
        <line-breakpoint type="java-line">
          <url>file://$PROJECT_DIR$/src/main/java/edu/memphis/ccrg/lida/pam/PamImpl0.java</url>
          <line>478</line>
          <option name="timeStamp" value="1493" />
        </line-breakpoint>
        <line-breakpoint type="java-line">
          <url>file://$PROJECT_DIR$/src/main/java/edu/memphis/ccrg/lida/pam/PamImpl0.java</url>
          <line>482</line>
          <option name="timeStamp" value="1494" />
        </line-breakpoint>
        <line-breakpoint type="java-line">
          <url>file://$PROJECT_DIR$/src/main/java/edu/memphis/ccrg/lida/pam/PamImpl0.java</url>
          <line>496</line>
          <option name="timeStamp" value="1495" />
        </line-breakpoint>
        <line-breakpoint type="java-line">
          <url>file://$PROJECT_DIR$/src/main/java/edu/memphis/ccrg/lida/pam/PamImpl0.java</url>
          <line>510</line>
          <option name="timeStamp" value="1496" />
        </line-breakpoint>
        <line-breakpoint type="java-line">
          <url>file://$PROJECT_DIR$/src/main/java/edu/memphis/ccrg/lida/pam/tasks/ProcessGTreeTask.java</url>
          <line>63</line>
          <option name="timeStamp" value="1500" />
        </line-breakpoint>
        <line-breakpoint type="java-line">
          <url>file://$PROJECT_DIR$/src/main/java/edu/memphis/ccrg/linars/Memory.java</url>
          <line>482</line>
          <option name="timeStamp" value="1501" />
        </line-breakpoint>
        <line-breakpoint type="java-line">
          <url>file://$PROJECT_DIR$/src/main/java/edu/memphis/ccrg/lida/pam/PamImpl0.java</url>
          <line>517</line>
          <option name="timeStamp" value="1502" />
        </line-breakpoint>
        <line-breakpoint type="java-line">
          <url>file://$PROJECT_DIR$/src/main/java/edu/memphis/ccrg/linars/ProcessGoal.java</url>
          <line>477</line>
          <option name="timeStamp" value="1507" />
        </line-breakpoint>
        <line-breakpoint type="java-line">
          <url>file://$PROJECT_DIR$/src/main/java/edu/memphis/ccrg/lida/pam/PamImpl0.java</url>
          <line>327</line>
          <option name="timeStamp" value="1509" />
        </line-breakpoint>
        <line-breakpoint type="java-line">
          <url>file://$PROJECT_DIR$/src/main/java/edu/memphis/ccrg/lida/pam/PamImpl0.java</url>
          <line>350</line>
          <option name="timeStamp" value="1510" />
        </line-breakpoint>
        <line-breakpoint type="java-line">
          <url>file://$PROJECT_DIR$/src/main/java/edu/memphis/ccrg/lida/pam/PamImpl0.java</url>
          <line>598</line>
          <option name="timeStamp" value="1511" />
        </line-breakpoint>
        <line-breakpoint type="java-line">
          <url>file://$PROJECT_DIR$/src/main/java/edu/memphis/ccrg/lida/pam/PamImpl0.java</url>
          <line>534</line>
          <option name="timeStamp" value="1512" />
        </line-breakpoint>
        <line-breakpoint type="java-line">
          <url>file://$PROJECT_DIR$/src/main/java/edu/memphis/ccrg/linars/CompoundTerm.java</url>
          <line>171</line>
          <option name="timeStamp" value="1513" />
        </line-breakpoint>
        <line-breakpoint type="java-line">
          <url>file://$PROJECT_DIR$/src/main/java/edu/memphis/ccrg/lida/pam/PamImpl0.java</url>
          <line>205</line>
          <option name="timeStamp" value="1518" />
        </line-breakpoint>
        <line-breakpoint enabled="true" type="java-line">
          <url>file://$PROJECT_DIR$/src/main/java/org/opennars/operator/misc/Search.java</url>
          <line>77</line>
          <option name="timeStamp" value="1519" />
        </line-breakpoint>
        <line-breakpoint type="java-line">
          <url>file://$PROJECT_DIR$/src/main/java/org/opennars/operator/misc/SearchSOM.java</url>
          <line>101</line>
          <option name="timeStamp" value="1520" />
        </line-breakpoint>
        <line-breakpoint enabled="true" type="java-line">
          <url>file://$PROJECT_DIR$/src/main/java/org/opennars/operator/misc/SearchSOM.java</url>
          <line>238</line>
          <option name="timeStamp" value="1521" />
        </line-breakpoint>
        <line-breakpoint enabled="true" type="java-line">
          <url>file://$PROJECT_DIR$/src/main/java/org/opennars/operator/misc/SearchSOM.java</url>
          <line>244</line>
          <option name="timeStamp" value="1522" />
        </line-breakpoint>
        <line-breakpoint enabled="true" type="java-line">
          <url>file://$PROJECT_DIR$/src/main/java/org/opennars/operator/misc/SearchSOM.java</url>
          <line>275</line>
          <option name="timeStamp" value="1523" />
        </line-breakpoint>
        <line-breakpoint type="java-line">
          <url>file://$PROJECT_DIR$/src/main/java/org/opennars/operator/misc/SearchSOM.java</url>
          <line>138</line>
          <option name="timeStamp" value="1524" />
        </line-breakpoint>
        <line-breakpoint type="java-line">
          <url>file://$PROJECT_DIR$/src/main/java/org/opennars/operator/misc/SearchSOM.java</url>
          <line>123</line>
          <option name="timeStamp" value="1527" />
        </line-breakpoint>
        <line-breakpoint enabled="true" type="java-line">
          <url>file://$PROJECT_DIR$/src/main/java/com/warmer/kgmaker/controller/KGManagerController.java</url>
          <line>200</line>
          <option name="timeStamp" value="1528" />
        </line-breakpoint>
        <line-breakpoint enabled="true" type="java-line">
          <url>file://$PROJECT_DIR$/src/main/java/com/warmer/kgmaker/controller/KGManagerController.java</url>
          <line>197</line>
          <option name="timeStamp" value="1529" />
        </line-breakpoint>
        <line-breakpoint enabled="true" type="java-line">
          <url>file://$PROJECT_DIR$/src/main/java/com/warmer/kgmaker/util/Neo4jUtil.java</url>
          <line>281</line>
          <option name="timeStamp" value="1531" />
        </line-breakpoint>
        <line-breakpoint enabled="true" type="java-line">
          <url>file://$PROJECT_DIR$/src/main/java/com/warmer/kgmaker/controller/KGManagerController.java</url>
          <line>72</line>
          <option name="timeStamp" value="1534" />
        </line-breakpoint>
        <line-breakpoint enabled="true" type="java-line">
          <url>file://$PROJECT_DIR$/src/main/java/com/warmer/kgmaker/util/Neo4jUtil.java</url>
          <line>234</line>
          <option name="timeStamp" value="1535" />
        </line-breakpoint>
        <line-breakpoint enabled="true" type="java-line">
          <url>file://$PROJECT_DIR$/src/main/java/com/warmer/kgmaker/controller/KGManagerController.java</url>
          <line>201</line>
          <option name="timeStamp" value="1536" />
        </line-breakpoint>
        <line-breakpoint type="java-line">
          <url>file://$PROJECT_DIR$/src/main/java/com/warmer/kgmaker/util/Neo4jUtil.java</url>
          <line>300</line>
          <option name="timeStamp" value="1537" />
        </line-breakpoint>
        <line-breakpoint type="java-line">
          <url>file://$PROJECT_DIR$/src/main/java/edu/memphis/ccrg/lida/pam/PAMemoryImpl.java</url>
          <line>569</line>
          <option name="timeStamp" value="1538" />
        </line-breakpoint>
        <line-breakpoint type="java-line">
          <url>file://$PROJECT_DIR$/src/main/java/edu/memphis/ccrg/lida/pam/tasks/ExcitationTask.java</url>
          <line>88</line>
          <option name="timeStamp" value="1549" />
        </line-breakpoint>
        <line-breakpoint type="java-line">
          <url>file://$PROJECT_DIR$/src/main/java/edu/memphis/ccrg/lida/pam/PamImpl0.java</url>
          <line>70</line>
          <option name="timeStamp" value="1550" />
        </line-breakpoint>
        <line-breakpoint type="java-line">
          <url>file://$PROJECT_DIR$/src/main/java/edu/memphis/ccrg/lida/framework/initialization/AgentXmlFactory.java</url>
          <line>152</line>
          <option name="timeStamp" value="1551" />
        </line-breakpoint>
        <line-breakpoint type="java-line">
          <url>file://$PROJECT_DIR$/src/main/java/org/opennars/inference/TemporalRules.java</url>
          <line>271</line>
          <option name="timeStamp" value="1560" />
        </line-breakpoint>
        <line-breakpoint type="java-line">
          <url>file://$PROJECT_DIR$/src/main/java/org/opennars/main/Nar.java</url>
          <line>457</line>
          <option name="timeStamp" value="1561" />
        </line-breakpoint>
        <line-breakpoint type="java-line">
          <url>file://$PROJECT_DIR$/src/main/java/edu/memphis/ccrg/lida/workspace/WorkspaceImpl.java</url>
          <line>254</line>
          <option name="timeStamp" value="1565" />
        </line-breakpoint>
        <line-breakpoint enabled="true" type="java-line">
          <url>file://$PROJECT_DIR$/src/main/java/edu/memphis/ccrg/lida/pam/PamLinkImpl.java</url>
          <line>59</line>
          <option name="timeStamp" value="1566" />
        </line-breakpoint>
        <line-breakpoint enabled="true" type="java-line">
          <url>file://$PROJECT_DIR$/src/main/java/edu/memphis/ccrg/lida/framework/shared/LinkImpl.java</url>
          <line>100</line>
          <option name="timeStamp" value="1567" />
        </line-breakpoint>
        <line-breakpoint enabled="true" type="java-line">
          <url>file://$PROJECT_DIR$/src/main/java/edu/memphis/ccrg/lida/framework/shared/LinkImpl.java</url>
          <line>136</line>
          <option name="timeStamp" value="1568" />
        </line-breakpoint>
        <line-breakpoint enabled="true" type="java-line">
          <url>file://$PROJECT_DIR$/src/main/java/edu/memphis/ccrg/lida/framework/shared/LinkImpl.java</url>
          <line>167</line>
          <option name="timeStamp" value="1569" />
        </line-breakpoint>
        <line-breakpoint enabled="true" type="java-line">
          <url>file://$PROJECT_DIR$/src/main/java/org/opennars/language/Statement.java</url>
          <line>64</line>
          <option name="timeStamp" value="1576" />
        </line-breakpoint>
        <line-breakpoint type="java-line">
          <url>file://$PROJECT_DIR$/src/main/java/org/opennars/control/concept/ProcessJudgment.java</url>
          <line>191</line>
          <option name="timeStamp" value="1580" />
        </line-breakpoint>
        <line-breakpoint type="java-line">
          <url>file://$PROJECT_DIR$/src/main/java/edu/memphis/ccrg/lida/nlanguage/Tree_nest.java</url>
          <line>28</line>
          <option name="timeStamp" value="1583" />
        </line-breakpoint>
        <line-breakpoint type="java-line">
          <url>file://$PROJECT_DIR$/src/main/java/edu/memphis/ccrg/lida/pam/tasks/GoalBackgroundTask.java</url>
          <line>120</line>
          <option name="timeStamp" value="1584" />
        </line-breakpoint>
        <line-breakpoint enabled="true" type="java-line">
          <url>file://$PROJECT_DIR$/src/main/java/org/opennars/operator/mental/Remind.java</url>
          <line>50</line>
          <option name="timeStamp" value="1585" />
        </line-breakpoint>
        <line-breakpoint type="java-line">
          <url>file://$PROJECT_DIR$/src/main/java/edu/memphis/ccrg/linars/Memory.java</url>
          <line>483</line>
          <option name="timeStamp" value="1588" />
        </line-breakpoint>
        <line-breakpoint type="java-line">
          <url>file://$PROJECT_DIR$/src/main/java/edu/memphis/ccrg/linars/ProcessGoal.java</url>
          <line>178</line>
          <option name="timeStamp" value="1589" />
        </line-breakpoint>
        <line-breakpoint type="java-line">
          <url>file://$PROJECT_DIR$/src/main/java/edu/memphis/ccrg/linars/ProcessGoal.java</url>
          <line>180</line>
          <option name="timeStamp" value="1592" />
        </line-breakpoint>
        <line-breakpoint type="java-line">
          <url>file://$PROJECT_DIR$/src/main/java/org/opennars/control/InferenceControl.java</url>
          <line>85</line>
          <option name="timeStamp" value="1594" />
        </line-breakpoint>
        <line-breakpoint type="java-line">
          <url>file://$PROJECT_DIR$/src/main/java/org/opennars/storage/Bag1.java</url>
          <line>119</line>
          <option name="timeStamp" value="1595" />
        </line-breakpoint>
        <line-breakpoint type="java-line">
          <url>file://$PROJECT_DIR$/src/main/java/org/opennars/inference/TemporalRules.java</url>
          <line>269</line>
          <option name="timeStamp" value="1596" />
        </line-breakpoint>
        <line-breakpoint type="java-line">
          <url>file://$PROJECT_DIR$/src/main/java/edu/memphis/ccrg/linars/Memory.java</url>
          <line>473</line>
          <option name="timeStamp" value="1597" />
        </line-breakpoint>
        <line-breakpoint type="java-line">
          <url>file://$PROJECT_DIR$/src/main/java/edu/memphis/ccrg/linars/Memory.java</url>
          <line>306</line>
          <option name="timeStamp" value="1598" />
        </line-breakpoint>
        <line-breakpoint type="java-line">
          <url>file://$PROJECT_DIR$/src/main/java/edu/memphis/ccrg/linars/Memory.java</url>
          <line>275</line>
          <option name="timeStamp" value="1599" />
        </line-breakpoint>
        <line-breakpoint type="java-line">
          <url>file://$PROJECT_DIR$/src/main/java/edu/memphis/ccrg/linars/Memory.java</url>
          <line>289</line>
          <option name="timeStamp" value="1600" />
        </line-breakpoint>
        <line-breakpoint type="java-line">
          <url>file://$PROJECT_DIR$/src/main/java/edu/memphis/ccrg/linars/Memory.java</url>
          <line>539</line>
          <option name="timeStamp" value="1601" />
        </line-breakpoint>
        <line-breakpoint type="java-line">
          <url>file://$PROJECT_DIR$/src/main/java/edu/memphis/ccrg/linars/Concept.java</url>
          <line>242</line>
          <option name="timeStamp" value="1602" />
        </line-breakpoint>
        <line-breakpoint type="java-line">
          <url>file://$PROJECT_DIR$/src/main/java/edu/memphis/ccrg/linars/Memory.java</url>
          <line>248</line>
          <option name="timeStamp" value="1603" />
        </line-breakpoint>
        <line-breakpoint type="java-line">
          <url>file://$PROJECT_DIR$/src/main/java/edu/memphis/ccrg/lida/pam/tasks/GoalBackgroundTask.java</url>
          <line>98</line>
          <option name="timeStamp" value="1605" />
        </line-breakpoint>
        <line-breakpoint type="java-line">
          <url>file://$PROJECT_DIR$/src/main/java/org/opennars/storage/Bag1.java</url>
          <line>104</line>
          <option name="timeStamp" value="1607" />
        </line-breakpoint>
        <line-breakpoint type="java-line">
          <url>file://$PROJECT_DIR$/src/main/java/org/opennars/entity/Stamp.java</url>
          <line>429</line>
          <option name="timeStamp" value="1608" />
        </line-breakpoint>
        <line-breakpoint type="java-line">
          <url>file://$PROJECT_DIR$/src/main/java/edu/memphis/ccrg/lida/pam/PamImpl0.java</url>
          <line>299</line>
          <option name="timeStamp" value="1615" />
        </line-breakpoint>
        <line-breakpoint type="java-line">
          <url>file://$PROJECT_DIR$/src/main/java/edu/memphis/ccrg/linars/ProcessGoal.java</url>
          <line>427</line>
          <option name="timeStamp" value="1617" />
        </line-breakpoint>
        <line-breakpoint type="java-line">
          <url>file://$PROJECT_DIR$/src/main/java/edu/memphis/ccrg/lida/workspace/WorkspaceImpl.java</url>
          <line>228</line>
          <option name="timeStamp" value="1624" />
        </line-breakpoint>
        <line-breakpoint type="java-line">
          <url>file://$PROJECT_DIR$/src/main/java/org/opennars/io/Narsese.java</url>
          <line>349</line>
          <option name="timeStamp" value="1626" />
        </line-breakpoint>
        <line-breakpoint type="java-line">
          <url>file://$PROJECT_DIR$/src/main/java/org/opennars/io/Narsese.java</url>
          <line>263</line>
          <option name="timeStamp" value="1627" />
        </line-breakpoint>
        <line-breakpoint enabled="true" type="java-line">
          <url>file://$PROJECT_DIR$/src/main/java/org/opennars/language/IntersectionExt.java</url>
          <line>118</line>
          <option name="timeStamp" value="1628" />
        </line-breakpoint>
        <line-breakpoint enabled="true" type="java-line">
          <url>file://$PROJECT_DIR$/src/main/java/org/opennars/language/Terms.java</url>
          <line>158</line>
          <option name="timeStamp" value="1629" />
        </line-breakpoint>
        <line-breakpoint type="java-line">
          <url>file://$PROJECT_DIR$/src/main/java/edu/memphis/ccrg/lida/workspace/WorkspaceImpl.java</url>
          <line>269</line>
          <option name="timeStamp" value="1632" />
        </line-breakpoint>
        <line-breakpoint type="java-line">
          <url>file://$PROJECT_DIR$/src/main/java/org/opennars/language/Implication.java</url>
          <line>126</line>
          <option name="timeStamp" value="1636" />
        </line-breakpoint>
        <line-breakpoint type="java-line">
          <url>file://$PROJECT_DIR$/src/main/java/edu/memphis/ccrg/lida/pam/tasks/GoalBackgroundTask.java</url>
          <line>151</line>
          <option name="timeStamp" value="1638" />
        </line-breakpoint>
        <line-breakpoint enabled="true" type="java-line">
          <url>file://$PROJECT_DIR$/src/main/java/edu/memphis/ccrg/lida/pam/PamImpl0.java</url>
          <line>430</line>
          <option name="timeStamp" value="1639" />
        </line-breakpoint>
        <breakpoint enabled="true" type="java-exception">
          <properties class="java.lang.StringIndexOutOfBoundsException" package="java.lang" />
          <option name="timeStamp" value="839" />
        </breakpoint>
        <line-breakpoint enabled="true" type="java-method">
          <url>jar://$MAVEN_REPOSITORY$/org/neo4j/neo4j-graph-algo/5.16.0/neo4j-graph-algo-5.16.0-sources.jar!/org/neo4j/graphalgo/PathFinder.java</url>
          <line>55</line>
          <properties class="org.neo4j.graphalgo.PathFinder">
            <option name="EMULATED" value="true" />
            <option name="WATCH_EXIT" value="false" />
          </properties>
          <option name="timeStamp" value="1398" />
        </line-breakpoint>
        <line-breakpoint enabled="true" type="java-method">
          <url>jar://$MAVEN_REPOSITORY$/org/neo4j/neo4j-graph-algo/5.16.0/neo4j-graph-algo-5.16.0-sources.jar!/org/neo4j/graphalgo/PathFinder.java</url>
          <line>44</line>
          <properties class="org.neo4j.graphalgo.PathFinder">
            <option name="EMULATED" value="true" />
            <option name="WATCH_EXIT" value="false" />
          </properties>
          <option name="timeStamp" value="1399" />
        </line-breakpoint>
        <line-breakpoint enabled="true" type="java-method">
          <url>jar://$MAVEN_REPOSITORY$/org/neo4j/neo4j-graphdb-api/5.16.0/neo4j-graphdb-api-5.16.0.jar!/org/neo4j/graphdb/PathExpander.class</url>
          <line>14</line>
          <properties class="org.neo4j.graphdb.PathExpander" method="reverse">
            <option name="EMULATED" value="true" />
            <option name="WATCH_EXIT" value="false" />
          </properties>
          <option name="timeStamp" value="1415" />
        </line-breakpoint>
        <line-breakpoint enabled="true" type="java-method">
          <url>jar://$MAVEN_REPOSITORY$/org/neo4j/neo4j-graphdb-api/5.16.0/neo4j-graphdb-api-5.16.0.jar!/org/neo4j/graphdb/PathExpander.class</url>
          <line>12</line>
          <properties class="org.neo4j.graphdb.PathExpander" method="expand">
            <option name="EMULATED" value="true" />
            <option name="WATCH_EXIT" value="false" />
          </properties>
          <option name="timeStamp" value="1416" />
        </line-breakpoint>
        <line-breakpoint enabled="true" type="java-method">
          <url>jar://$MAVEN_REPOSITORY$/org/neo4j/neo4j-graphdb-api/5.16.0/neo4j-graphdb-api-5.16.0.jar!/org/neo4j/graphdb/Path.class</url>
          <line>14</line>
          <properties class="org.neo4j.graphdb.Path" method="endNode">
            <option name="EMULATED" value="true" />
            <option name="WATCH_EXIT" value="false" />
          </properties>
          <option name="timeStamp" value="1435" />
        </line-breakpoint>
        <line-breakpoint enabled="true" type="java-method">
          <url>jar://$MAVEN_REPOSITORY$/org/neo4j/neo4j-graphdb-api/5.16.0/neo4j-graphdb-api-5.16.0-sources.jar!/org/neo4j/graphdb/Path.java</url>
          <line>52</line>
          <properties class="org.neo4j.graphdb.Path">
            <option name="EMULATED" value="true" />
            <option name="WATCH_EXIT" value="false" />
          </properties>
          <option name="timeStamp" value="1437" />
        </line-breakpoint>
        <line-breakpoint enabled="true" type="java-method">
          <url>jar://$MAVEN_REPOSITORY$/org/neo4j/neo4j-graphdb-api/5.16.0/neo4j-graphdb-api-5.16.0-sources.jar!/org/neo4j/graphdb/PathExpander.java</url>
          <line>43</line>
          <properties class="org.neo4j.graphdb.PathExpander">
            <option name="EMULATED" value="true" />
            <option name="WATCH_EXIT" value="false" />
          </properties>
          <option name="timeStamp" value="1438" />
        </line-breakpoint>
        <line-breakpoint enabled="true" type="java-field">
          <url>jar://$MAVEN_REPOSITORY$/org/neo4j/neo4j-graph-algo/5.16.0/neo4j-graph-algo-5.16.0-sources.jar!/org/neo4j/graphalgo/impl/path/TraversalShortestPath.java</url>
          <line>39</line>
          <properties field="context" class="org.neo4j.graphalgo.impl.path.TraversalShortestPath" />
          <option name="timeStamp" value="1407" />
        </line-breakpoint>
        <line-breakpoint enabled="true" type="java-field">
          <url>jar://$MAVEN_REPOSITORY$/org/neo4j/neo4j-graphdb-api/5.16.0/neo4j-graphdb-api-5.16.0.jar!/org/neo4j/graphdb/traversal/Paths.class</url>
          <line>170</line>
          <properties field="node" class="org.neo4j.graphdb.traversal.Paths.SingleNodePath" />
          <option name="timeStamp" value="1433" />
        </line-breakpoint>
      </breakpoints>
    </breakpoint-manager>
    <pin-to-top-manager>
      <pinned-members>
        <PinnedItemInfo parentTag="org.opennars.storage.Bag1" memberName="theMap" />
        <PinnedItemInfo parentTag="org.opennars.storage.Bag1" memberName="queue" />
        <PinnedItemInfo parentTag="edu.memphis.ccrg.lida.pam.tasks.ReasonTask" memberName="nextExcecutionTicksPerRun" />
        <PinnedItemInfo parentTag="org.opennars.entity.TermLink" memberName="target" />
        <PinnedItemInfo parentTag="edu.memphis.ccrg.lida.language.TreeBag" memberName="nameTable" />
        <PinnedItemInfo parentTag="edu.memphis.ccrg.lida.language.TreeBag" memberName="completeTerms" />
      </pinned-members>
    </pin-to-top-manager>
    <watches-manager>
      <configuration name="JUnit">
        <watch expression="term" />
      </configuration>
      <configuration name="SpringBootApplicationConfigurationType">
        <watch expression="mem.globalBuffer.mem.concepts" />
      </configuration>
    </watches-manager>
  </component>
  <component name="XSLT-Support.FileAssociations.UIState">
    <expand />
    <select />
  </component>
  <component name="com.intellij.coverage.CoverageDataManagerImpl">
    <SUITE FILE_PATH="coverage/neo_lida_nars310_xr$linars.ic" NAME="linars Coverage Results" MODIFIED="1699627080450" SOURCE_PROVIDER="com.intellij.coverage.DefaultCoverageFileProvider" RUNNER="idea" COVERAGE_BY_TEST_ENABLED="false" COVERAGE_TRACING_ENABLED="false" />
  </component>
</project>