/*******************************************************************************
 * Copyright (c) 2009, 2011 The University of Memphis.  All rights reserved. 
 * This program and the accompanying materials are made available 
 * under the terms of the LIDA Software Framework Non-Commercial License v1.0 
 * which accompanies this distribution, and is available at
 * http://ccrg.cs.memphis.edu/assets/papers/2010/LIDA-framework-non-commercial-v1.0.pdf
 *******************************************************************************/
package edu.memphis.ccrg.lida.nlanguage;

import com.warmer.kgmaker.KgmakerApplication;
import edu.memphis.ccrg.lida.data.NeoUtil;
import edu.memphis.ccrg.lida.framework.ModuleName;
import edu.memphis.ccrg.lida.framework.initialization.AgentStarter;
import edu.memphis.ccrg.lida.framework.shared.Link;
import edu.memphis.ccrg.lida.framework.shared.LinkImpl;
import edu.memphis.ccrg.lida.framework.shared.Node;
import edu.memphis.ccrg.lida.framework.shared.NodeImpl;
import edu.memphis.ccrg.lida.framework.tasks.FrameworkTaskImpl;
import edu.memphis.ccrg.lida.pam.PAMemory;
import edu.memphis.ccrg.lida.pam.PamImpl0;
import edu.memphis.ccrg.lida.pam.PamLink;
import edu.memphis.ccrg.lida.pam.tasks.ExcitationTask;
import edu.memphis.ccrg.lida.pam.tasks.PropagationTask;
import edu.memphis.ccrg.lida.workspace.WorkspaceContent;
import org.opennars.entity.BudgetValue;
import edu.memphis.ccrg.linars.Term;

import java.util.*;
import java.util.concurrent.atomic.AtomicBoolean;

import static edu.memphis.ccrg.lida.framework.initialization.AgentStarter.nar;
import static edu.memphis.ccrg.lida.pam.PamImpl0.mmcache2;

/**
 * A task to add a {@link PamLink} and its sink to the percept.
 * 
 * <AUTHOR> J. McCall
 * @see ExcitationTask creates this task
 * @see PropagationTask creates this task
 */
public class LangGSMergeTask extends FrameworkTaskImpl {
	private PAMemory pam;
	private WorkspaceContent listenNs;
	private WorkspaceContent yufaNs;
	private WorkspaceContent yuyiNs;
	private String message;

	int lsize;    // 对应场景总边数量属性
	int actlsize;// 场景已激活的边的数量
	Set<Link> links; //当前词的词性所在的所有构式边
	Node pos;

	TreeBag treeBag;

	private AtomicBoolean started = new AtomicBoolean(false);
	Set<String> mmcache0 = PamImpl0.gmcache;

	/**
	 * Default constructor
	 * @param link {@link PamLink}
	 * @param pam {@link PAMemory}
	 */
	public LangGSMergeTask(Link link, PAMemory pam) {
		super(1);
		this.pam = pam;
//		this.link = link;

		// 语言高频处理，需要常驻？分别尝试下
		listenNs = pam.getWorkspaceBuffer("listen").getBufferContent(null);
		yufaNs = pam.getWorkspaceBuffer("yufa").getBufferContent(null);
		yuyiNs = pam.getWorkspaceBuffer("yuyi").getBufferContent(null);
		// 另设整合子图？语法子图数据不多，直接整合到语法子图中？
		treeBag = ((ChartTreeSet)yufaNs).chartSet;
	}

	/**
	 * Adds link's sink to the percept and tries to add the link as well then finishes.
	 */
	@Override
	protected synchronized void runThisFrameworkTask() {
//		System.out.println("语义分析任务开始");
		// 不能多线程删除，否则会出现并发问题，删除时，其他线程可能正在遍历，导致遍历出错
		// 解决办法：1.加锁，2.不删除，只标记，3.不删除，只记录，4.不删除，只记录，但是记录的是下标，遍历时跳过
		// 采用加锁方案
		if (started.compareAndSet(false, true)) {
			if (KgmakerApplication.message.size() == mmcache0.size()) {
				// 语义语法整合完才清空。语义线程，语法线程，整合线程？三线程同时，有数据就处理，无数据就等待
				KgmakerApplication.message.clear();
				PamImpl0.gmcache.clear();
				mmcache2.clear();
				treeBag.clear();
				started.set(false);
				return;
			}
		}

		// 考虑衰减问题，不能总从ns查，有些是上周期的，有些则衰减完，没法参与本周期
//		Node mm = listenNs.getNode(message);
//		if (mm == null) {
////			words.add(mm); // 存入一个新序列
////		} else {
//			started.set(false);
//			return;
//		}

		mmcache0.add(message + " - 1"); //new出来的都是放进程堆中，各线程共享的

		// 逐词从节点属性拿到词性，通过词性找到对应的构式，存buffer
		// 点投票模式，逐步动态在buffer里组装框架，而不是通过一条边查出所有关系
//		pos = NeoUtil.getNode((String) mm.getProperty("pos"));
		//todo 有多种词性的情况，方向等问题
//		links = NeoUtil.getLinks(pos);

//		System.out.println("整合分析任务线程 " + Thread.currentThread().getName());
//		System.out.println("mmcache 数量---------" + mmcache0.size());

		if (mmcache0.size() == 0) {
			started.set(false);
			return;
		}
		if (links != null && links.size() > 0) {
//			actsence(links);
		}
		started.set(false);

		// 开始尝试构建语法树+语义树，构式森林先汇总完毕，像漏斗，会有多个局部语法树，筛选要求：无歧义+最大覆盖+最小成本+最大概率


//		cancel();
	}

	private void actsence(Set<Link> links) {
		Node sink;
		String sname;
		String sinkId;// 场景id唯一，完整匹配有多种，只要有一种即可
//		TreeChart treeChart;
//		ArrayList<Term> findingList;
		for (Link parent : links) {
			sink = (Node) parent.getSink();
			sname = sink.getTNname();
			pam.getListener().receivePercept(parent, ModuleName.GrammarGraph);

			lsize = Integer.parseInt(((String) sink.getProperty("size")));
//			sinkId = String.valueOf(sink.getNodeId());

			boolean isAct = false;
			TreeChart matchTreeChart = null;
			// 遍历treebag里nameTable的keyset中，左括号前字符是否匹配sname，也就是是否已经有该sink，有则跳过，没有则加入
			for (String key : treeBag.nameTable.keySet()) {
				if (key.substring(0, key.indexOf("(")).equals(sname)) {
					matchTreeChart = treeBag.nameTable.get(key);
					boolean isFind = false;
					// 判断当前key的value里，findingList是否包含当前parent链接，有则从findingList里移除，在foundList里添加
					// 无论是否嵌套，是否有匹配完整的嵌套，一旦符合根节点和有find边，都移除。是嵌套构式，要在parent替换整体子构式前移除
					if (matchTreeChart.findingList.contains(parent)) {
						matchTreeChart.findingList.remove(parent);
						isFind = true;
					}

					String sourceName = parent.getSource().getTNname();
					String sourceName2 = sourceName.substring(sourceName.indexOf("_") + 1);
					boolean isFound = false;
					// 如果parent的source头节点，name的“_”字符后为双字符，说明是中间层构式，在未匹配完整时就要开始嵌套构建
					if (sourceName2.length() >= 2 && treeBag.completeTerms != null) {
						for (String key2 : treeBag.completeTerms.keySet()) {
							String key2sub = key2.substring(0, key.indexOf("("))
									.substring(sname.indexOf("_") + 1);
							// 在匹配完整的构式里找到根节点匹配此双字符的构式2
							if (key2sub.equals(sourceName2)) {
								TreeChart matchTreeChart2 = treeBag.completeTerms.get(key2);

								if (isFind){
									// 用构式2整体替换foundList里的parent的source节点
									parent.setSource(matchTreeChart2);
									((LinkImpl)parent).init(((LinkImpl)parent).term);
									// 如果是已有匹配完整嵌套构式，先移除foundlist的对应部分，再加入构建新嵌套，保留已有的
									matchTreeChart.foundList.add((Term) parent);
								}

								Collection<Term> foundcopy = new ArrayList<>();
								foundcopy.addAll(matchTreeChart.foundList);
								// 如果是正在find的构式，不是已有匹配完整嵌套构式，直接加入构建新嵌套
								if (!isFind) {
									for (Term link : foundcopy) {
										Node ss = ((Link)link).getSource();
										String rr ;
										if(ss instanceof TreeChart){
											rr = ((TreeChart) ss).sceneRoot.getTNname();
										}else {
											rr = ss.getTNname();
										}
										// 无论是否已嵌套，只要找到匹配的，就替换
										if (rr.substring(sname.indexOf("_") + 1).equals(key2sub)) {
											isFound = true;
											((Link) link).setSource(matchTreeChart2);
											((LinkImpl)link).init(((LinkImpl)link).term);
	//										matchTreeChart.foundList = foundcopy;	// 替换后更新foundList
											break;
										}
									}
								}

								// 将foundList和findingList里的链接整合为新list
								List<Link> links2 = new ArrayList<>();
								for (Term link : matchTreeChart.foundList) {
									links2.add((Link) link);
								}
								for (Term link : matchTreeChart.findingList) {
									links2.add((Link) link);
								}

								NodeImpl root = (NodeImpl) matchTreeChart.sceneRoot;
								// 然后重新组装并更新matchTreeChart的sceneTerm
								matchTreeChart = bulidTree(root, root.getTNname(), foundcopy, matchTreeChart.foundList.size(), links2);
								matchTreeChart.init(matchTreeChart.term);
//								break; // 匹配替换一个即可，即使还有其他匹配，也不需要再继续，比如vv？
							}
						}
					}

					// 上面加了，这里就不用加了。如已有完整嵌套构式，也要加入foundList，把已匹配的对应部分替换成parent
					if (!matchTreeChart.foundList.contains(parent) && !isFound) {
						matchTreeChart.foundList.add((Term) parent);
					}

					// 如果findingList为空，说明该构式已经完整匹配，加入treebag的完整构式列表
					if (matchTreeChart.findingList.size() == 0) {
						// 不能无限嵌套，复杂度不能大于句子长度
						if (matchTreeChart.complexity < KgmakerApplication.message.size() * 6){
							treeBag.completeTerms.put(matchTreeChart.toString(),matchTreeChart);
							// 继续往下激活，中间层构式，直接用成分而不是词性，虽然词性与成分一一对应
							String noden = "pos_" + sname.substring(sname.indexOf("_") + 1);
							Node node = new NodeImpl(noden);
							Set<Link> links01 = NeoUtil.getLinks(node);
							if (links01 != null && links01.size() > 0) {
								actsence(links01);
							}
						}
					}
//					}
					isAct = true;
					break; // 构式根节点都唯一，找到一个就跳出
				}
			}
			// 根据根节点sink判断buffer里是否有两条边或以上构式，有则尝试构建语法树
			// 第一个词通常无完整构式，也有可能首词非句首=语句被截断，乱序输入等，越靠近句首=构式角色越靠前
			if (!isAct) {
				// 未全激活，加入新链接后，看是否完整激活
				Collection<Term> scenels = ((ChartTreeSet)yufaNs).getLinksOfSinkT(sname);
				actlsize = scenels.size();

				// 一个词也能匹配单构式？只有单个词如感叹+拟声等，连词=但和与？np=n等也是单构式
				if ((double) actlsize / lsize >= 0.5) {
					// 如果构式总边数与已激活边数达到一半，则直接激活整个框架为完整构式，优先级最高，放进确定集合里
					// 未匹配的可当预测，也可先不管，不完整构式不好嵌套，完整的嵌套能一次成型，无需每次更新总构式，再有新嵌套直接拼接
					Set<Link> links0 = NeoUtil.getSomeLinks(sink,null, "<", null, null);
					List<Link> links00 = new ArrayList<>(links0);

					bulidTree((Term) sink, sname, scenels, lsize, links00);
				}

				// 已全激活，看是否匹配完整
			}
		}
	}

	private TreeChart bulidTree(Term sceneRoot, String rootName, Collection<Term> foundList,
						   int lsize , List<Link> links00) {
		List<Term> findingList = new ArrayList<>();
		Term[] components = new Term[lsize];
		Link ll;
		Map<Integer,String> orderMap = new HashMap();
		int order;
		if(links00.size() != lsize){
			order = links00.size();
		}else {
			order = lsize;
		}
		String subSceneStr = "";
		for (int i = 0; i < order; i++) {
			ll = links00.get(i);
			components[i] = (Term) ll;    // 只是遍历次序，实际次序在元素属性里
			pam.getListener().receivePercept(ll, ModuleName.GrammarGraph);
			if (!foundList.contains(ll)) {
				findingList.add((Term) ll); // 未激活的边，都放预测边集里
			}
//			if (ll.getSource() instanceof TreeChart){
//				subSceneStr = ((TreeChart) ll.getSource()).sceneStr;
//			}else {
//				subSceneStr = (String) ll.getSource().getProperty("name");
//			}
//			orderMap.put(Integer.valueOf((String) (ll.getProperty("order"))), subSceneStr);
		}
		StringBuffer buf = new StringBuffer();
		// 生成场景文本序列=产生式规则，场景为左部，构式其他成分为右部，要注意词语次序，有些框架本身带有嵌套
		// 两种方案，原始单产生式直存到场景节点；或实时生成。后者更好，因为存储费空间，而且不好改，再者嵌套需要实时生成
//		buf.append(rootName + "(");
//		for (int i = 0; i < lsize; i++) {
//			if(buf.length() > 8) buf.append(" , ");// 按字符数，非词数
//			buf.append(orderMap.get(i));
//		}
//		buf.append(")");

//		Term sceneTerm = new Product(components); // 无关系元素集合，次序关系在元素属性里，可能有子节点

		TreeChart treeChart = new TreeChart(new BudgetValue(0.99f, 0.1f, 0.1f, AgentStarter.nar.narParameters),
				sceneRoot, components, foundList, findingList);

		treeBag.putBack(treeChart, 10f, nar.memory);

		return treeChart;
	}
}

