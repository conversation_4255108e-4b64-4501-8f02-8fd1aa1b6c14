/* 
 * The MIT License
 *
 * Copyright 2018 The OpenNARS authors.
 *
 * Permission is hereby granted, free of charge, to any person obtaining a copy
 * of this software and associated documentation files (the "Software"), to deal
 * in the Software without restriction, including without limitation the rights
 * to use, copy, modify, merge, publish, distribute, sublicense, and/or sell
 * copies of the Software, and to permit persons to whom the Software is
 * furnished to do so, subject to the following conditions:
 *
 * The above copyright notice and this permission notice shall be included in
 * all copies or substantial portions of the Software.
 *
 * THE SOFTWARE IS PROVIDED "AS IS", WITHOUT WARRANTY OF ANY KIND, EXPRESS OR
 * IMPLIED, INCLUDING BUT NOT LIMITED TO THE WARRANTIES OF MERCHANTABILITY,
 * FITNESS FOR A PARTICULAR PURPOSE AND NONINFRINGEMENT. IN NO EVENT SHALL THE
 * AUTHORS OR COPYRIGHT HOLDERS BE LIABLE FOR ANY CLAIM, DAMAGES OR OTHER
 * LIABILITY, WHETHER IN AN ACTION OF CONTRACT, TORT OR OTHERWISE, ARISING FROM,
 * OUT OF OR IN CONNECTION WITH THE SOFTWARE OR THE USE OR OTHER DEALINGS IN
 * THE SOFTWARE.
 */
package edu.memphis.ccrg.linars;

import org.apache.commons.lang3.StringUtils;
import org.opennars.inference.TemporalRules;
import org.opennars.io.Symbols;
import org.opennars.io.Symbols.NativeOperator;
import org.opennars.io.Texts;
import org.opennars.language.*;
import org.opennars.main.Debug;
import org.opennars.operator.ImaginationSpace;
import org.opennars.operator.Operation;
import org.opennars.operator.Operator;

import java.io.Serializable;
import java.util.*;

/**
 * Term is the basic component of Narsese, and the object of processing in NARS.
 * <p>
 * A Term may have an associated Concept containing relations with other Terms.
 * It is not linked in the Term, because a Concept may be forgot while the Term
 * exists. Multiple objects may represent the same Term.
 * 术语是Narsese的基本组成部分，是NARS中处理的对象。术语可能具有关联的概念，其中包含与其他术语的关系。
 * 在术语中未链接，因为术语存在时可能会忘记概念。多个对象可以代表同一个术语
 * <AUTHOR> Wang
 * <AUTHOR> Hammer
 */
public class Term extends TermNodeImpl implements Serializable, AbstractTerm {
    public ImaginationSpace imagination;
    private static final Map<CharSequence,Term> atoms = new LinkedHashMap();

    final public static Term SELF = SetExt.make(Term.get("SELF"));
    final public static Term SEQ_SPATIAL = Term.get("#");
    final public static Term SEQ_TEMPORAL = Term.get("&/");

    private int termId;
    private int parentId;
    private List<Term> children = new ArrayList<>();

    public List<Term> getChildren() {
        return children;
    }

    public void setChildren(List<Term> children) {
        this.children = children;
    }

    public int getTermId() {
        return termId;
    }

    public void setTermId(int termId) {
        this.termId = termId;
    }

    public int getParentId() {
        return parentId;
    }

    public void setParentId(int parentId) {
        this.parentId = parentId;
    }

    // private to cache it
    private CharSequence Tname = null;

//    public Map<String, Object> getProperties() {
//        return properties;
//    }
//
//    public void setProperties(Map<String, Object> properties) {
//        this.properties = properties;
//    }
//
//    private Map<String, Object> properties = new HashMap<>();

    /**
     * Default constructor that build an internal Term
     */
    public Term() {
        super();
//        setName("default");
    }

    /**
     * Constructor with a given name
     *
     * @param name A String as the name of the Term
     */
    //命名词项
    public Term(final CharSequence name) {
        super();
        setTermName(name);
        setNodeName((String) name);
    }

//    public Term(PamNodeImpl pn) {
//        super(pn);
//    }

    //是否为自身
    final public static boolean isSelf(final Term t) {
        return SELF.equals(t);
    }

	//算子
    public NativeOperator operator() {
        return NativeOperator.ATOM;
    }
    //是否为高阶语句
    public boolean isHigherOrderStatement() { //==> <=>
        return (this instanceof Equivalence) || (this instanceof Implication);
    }
    //可否执行
    public boolean isExecutable(final Memory mem) {
        //don't allow ^want and ^believe to be active/have an effect, 
        //which means its only used as monitor
		///不允许^ want和^ believe处于活动状态/具有效果，这意味着其仅用作监视器
        final boolean isOp = this instanceof Operation;
        if(isOp) {
            final Operator op = ((Operation)this).getOperator(); //the following part may be refactored after we
            //know more about how the NAL9 concepts should really interact together:
            /*if(op.equals(mem.getOperator("^want")) || op.equals(mem.getOperator("^believe"))) {
                return false;
            }*/
        }
        return isOp;
    }


    public interface TermVisitor {
        void visit(Term t, Term superterm);
    }

    /** gets the atomic term given a name */
	//获得原子项名
    public final static Term get(final CharSequence name) {
        //仅当它不是索引词项时返回
		Term x = atoms.get(name); //only
        if (x != null && !x.toString().endsWith("]")) { //return only if it isn't an index term仅当它不是索引词项时返回
            return x;
        }

        final String namestr = name.toString();
        //p[s,i,j]
        int[] term_indices = null;
        String before_indices_str = null;
        if(namestr.endsWith("]") && namestr.contains("[")) { //simple check, failing for most terms
            String indices_str = namestr.split("\\[")[1].split("\\]")[0];
            before_indices_str = namestr.split("\\[")[0];
            String[] inds = indices_str.split(",");
            if(inds.length == 2) { //only position info given仅给出位置信息
                indices_str="1,1,"+indices_str;
                inds = indices_str.split(",");
            }
            term_indices = new int[inds.length];
            for(int i=0;i<inds.length;i++) {
                if(StringUtils.isNumeric(inds[i]))
                    term_indices[i] = Integer.valueOf(inds[i]);
                else {
                    term_indices = null;
                    break;
                }
            }
        }
        
        CharSequence name2 = name;
        if(term_indices != null) { //only on conceptual level not
            name2 = before_indices_str + "[i,j,k,l]";
        }
        x = new Term(name2);
        x.term_indices = term_indices;
        x.index_variable = before_indices_str;
        atoms.put(name2, x);
        
        return x;
    }
    
    /** gets the atomic term of an integer */
	//获取整数的原子词项
    public final static Term get(final int i) {
        return get(Integer.toString(i));
    }

    /**
     * Reporting the name of the current Term.
     *  报告当前术语的名称
     * @return The name of the term as a String
     */
	//报告当前词项名
    @Override
    public CharSequence name() {
        if (nameInternal() == null) {
//            setTermName("name");
            return "";
        }
        return this.nameInternal();
    }
    //内部名称
    protected CharSequence nameInternal() {
//        if (name == null) {
//            return getName();
//        }else {
//            return Tname;
        return TNname;
//        }
    }
    
    public int[] term_indices = null;
    public String index_variable = "";

    /**
     * Make a new Term with the same name.
     *
     * @return The new Term
     */
	//命名相同的新词项。
    @Override
    public Term clone() {
        final Term t = new Term();
        if(term_indices != null) {
            t.term_indices = term_indices.clone();
            t.index_variable = index_variable;
        }
        t.setTermName((String) name());
        t.imagination = imagination;
        return t;
    }
    //克隆 深度
    public Term cloneDeep() {
        return clone();
    }

    /**
     * Equal terms have identical name, though not necessarily the same reference.
     *
     * @return Whether the two Terms are equal
     * @param that The Term to be compared with the current Term
     */
	//相同的词项具有相同的名称，尽管不一定是相同的引用。
    @Override
    public boolean equals(final Object that) {
        if (that == this) return true;
        if (getClass() != this.getClass()) return false; //optimization, if complexity is different they cant be equal
        String thisName = (String) name();
        if (thisName == null) {
            thisName = (String) name();
        }
        if (that instanceof Term){
            String thatName = (String) ((Term)that).name();
            if (thatName == null) {
                thatName = (String) ((Term) that).name();
            }
            return this.getComplexity() == ((Term) that)
                                                    .getComplexity() && thisName
                                                                        .equals(thatName);
        }else if (that instanceof String){
            return thisName.equals(that);
        }else {
            return false;
        }
//        String thatName = (String) ((Term)that).name();
//        if (thatName == null) {
//            thatName = (String) ((Term) that).name();
//        }
//        return this.getComplexity() == ((Term) that)
//                                                .getComplexity() && thisName
//                                                                    .equals(thatName);
    }

    /**
     * Produce a hash code for the term
     *
     * @return An integer hash code
     */
	//哈希化
    @Override
    public int hashCode() {
        if (name() == null) {
            return nodeId;
        }
        return name().hashCode();
    }

    /**
     * Check whether the current Term can name a Concept.
     * isConstant means if the term contains free variable
     * True if:
     *   has zero variables, or
     *   uses several instances of the same variable
     * False if it uses one instance of a variable ("free" like a "free radical" in chemistry).
     * Therefore it may be considered Constant, yet actually contain variables.
     * 
     * @return A Term is constant by default
     */
	//检查当前词项是否可以命名概念。为常数表示词项包含自由变量。如具有零个变量，或使用同一变量的多个实例为真；
    //如使用变量的一个实例（像“自由基”那样“自由”，则为假）。因此，可以将其视为常量，但实际上包含变量。
    @Override
    public boolean isConstant() {        
        return true;
    }
    //获取时序
    public int getTemporalOrder() {
        return TemporalRules.ORDER_NONE;
    }
    //得到空间
    public boolean getIsSpatial() {
        return false;
    }

	//递归词项
    public void recurseTerms(final TermVisitor v, final Term parent) {
        v.visit(this, parent);
        if (this instanceof CompoundTerm) {
            for (final Term t : ((CompoundTerm)this).term) {
                t.recurseTerms(v, this);
            }
        }
    }
    
	//包含变量的递归子项
    public void recurseSubtermsContainingVariables(final TermVisitor v) {
        recurseTerms(v, null);
    }
    
    public void recurseSubtermsContainingVariables(final TermVisitor v, final Term parent) {
        if (!hasVar()) return;
        v.visit(this, parent);
        if (this instanceof CompoundTerm) {
            for (final Term t : ((CompoundTerm)this).term) {
                t.recurseSubtermsContainingVariables(v, this);
            }
        }
    }
         
    /**
     * @return The complexity of the term, an integer
     */
    // the syntactic complexity, for constant atomic Term, is 1
    //获取词项复杂度，恒定原子项，语法复杂度为1
	public short getComplexity() {
        return 1;
    }

    /** set the name
     */
    // only method that should modify Term.name
	//命名
//    @Override
    public void setTermName(final CharSequence newName) {
//        this.Tname = newName;
        this.TNname = (String) newName;
    }
    
    /**
     * @param that The Term to be compared with the current Term
     * @return The same as compareTo as defined on Strings
     */
	//比较
    @Override
    public int compareTo(final AbstractTerm that) {
        if (that==this) {
            return 0;
        }
        //previously: Orders among terms: variable < atomic < compound
		//优先顺序：词项之间的顺序：变量<原子词项<复合词
        if ((that instanceof Variable) && (getClass()!=Variable.class)) {
            return 1;
        }
        else if ((this instanceof Variable) && (that.getClass()!=Variable.class)) {
            return -1;
        }
        return Texts.compareTo(name(), that.name());            
    }

    
    //包含时间关系,0
    public int containedTemporalRelations() {
        return 0;
    }
    
    /**
     * Recursively check if a compound contains a term
     *
     * @param target The term to be searched
     * @return Whether the two have the same content
     */
	//递归检查复合词是否包含词项
    public boolean containsTermRecursively(final Term target) {
        if(target==null) {
            return false;
        }
        return equals(target);
    }
    
    /**
     * Recursively count how often the terms are contained
     * 递归计算包含这些词项的频率
     * @param map The count map that will be created to count how often each term occurs
     * @return The counts of the terms
     */
    public Map<Term, Integer> countTermRecursively(Map<Term,Integer> map) { 
        if(map == null) {
            map = new LinkedHashMap<Term, Integer>();
        }
        map.put(this, map.getOrDefault(this, 0) + 1);
        return map;
    }

    /** whether this contains a term in its components. */
	//它的组成部分是否包含词项。
    public boolean containsTerm(final Term target) {
        return equals(target);
    }

    /**
     * The same as getName by default, used in display only.
     *
     * @return The name of the term as a String
     */
	//字符串化
    @Override
    public String toString() {
        return name().toString();
    }

//    public String getStringName(){
//        return super.getStringName();
//    }

    /** Creates a quote-escaped term from a string. Useful for an atomic term that is meant to contain a message as its name */
    //从字符串创建用引号引起来的词项。对于旨在包含消息名称的原子词项很有用
	public static Term text(final String t) {
        return Term.get("\"" + t + "\"");
    }


    /**
     * Whether this compound term contains any variable term
     *
     * @return Whether the name contains a variable
     */
	//
	//此复合词是否包含任何变量
    @Override public boolean hasVar() {
        return false;
    }
    
    public boolean hasVar(final char type) {
        switch (type) {
            case Symbols.VAR_DEPENDENT: return hasVarDep();
            case Symbols.VAR_INDEPENDENT: return hasVarIndep();
            case Symbols.VAR_QUERY: return hasVarQuery();
        }
        throw new IllegalStateException("Invalid variable type: " + type);
    }
    
	//此复合词是否包含自变量
    public boolean hasVarIndep() {
        return false;
    }
    //此复合词是否有间隔
    public boolean hasInterval() {
        return false;
    }
	//此复合词是否包含因变量
    public boolean hasVarDep() {
        return false;
    }
   //此复合词是否包询问可变项
    public boolean hasVarQuery() {
        return false;
    }

	//到排序集
    public static NavigableSet<Term> toSortedSet(final Term... arg) {
        //use toSortedSetArray where possible
        final NavigableSet<Term> t = new TreeSet();
        Collections.addAll(t, arg);
        return t;        
    }
    
    public final static Term[] EmptyTermArray = new Term[0];
    //排序集数组
    public static Term[] toSortedSetArray(final Term... arg) {
        switch (arg.length) {
            case 0: return EmptyTermArray;                
            case 1: return new Term[] { arg[0] };
            case 2: 
                final Term a = arg[0];
                final Term b = arg[1];
                final int c = a.compareTo(b);

                if (Debug.DETAILED) {
                    //verify consistency of compareTo() and equals()
                    final boolean equal = a.equals(b);
                    if ((equal && (c!=0)) || (!equal && (c==0))) {
                        throw new IllegalStateException("invalid order: " + a + " = " + b);
                    }
                }

                if (c < 0) return new Term[] { a, b };
                else if (c > 0) return new Term[] { b, a };
                else if (c == 0) return new Term[] { a }; //equal
                
        }
        
        //TODO fast sorted array for arg.length == 3

        //terms > 2:        
        final NavigableSet<Term> s = new TreeSet();
        //SortedList<Term> s = new SortedList(arg.length);
        //s.setAllowDuplicate(false);

        Collections.addAll(s, arg);
        
        return s.toArray(new Term[0]);
    }

    /** performs a thorough check of the validity of a term (by cloneDeep it) to see if it's valid */
    //对词项的有效性进行彻底检查（通过cloneDeep进行检查）以查看其是否有效* /
	public static boolean valid(final Term content) {
        final Term cloned = content.cloneDeep();
        return cloned != null;
    }

	
	//主语或谓语是否为自变量
    public boolean subjectOrPredicateIsIndependentVar() {
        if(this instanceof Statement) {
            final Statement cont=(Statement)this;
            if(cont.getSubject() instanceof Variable) {
                final Variable v=(Variable) cont.getSubject();
                if(v.hasVarIndep()) {
                    return true;
                }
            }
            if(cont.getPredicate()instanceof Variable) {
                final Variable v=(Variable) cont.getPredicate();
                return v.hasVarIndep();
            }
        }
        return false;
    }

    public CompoundTerm toCompoundTerm() {
        //将本词项转为复合词项
        if (this instanceof CompoundTerm) {
            return (CompoundTerm) this;
        }
        return null;
    }
}
