/* 
 * The MIT License
 *
 * Copyright 2018 The OpenNARS authors.
 *
 * Permission is hereby granted, free of charge, to any person obtaining a copy
 * of this software and associated documentation files (the "Software"), to deal
 * in the Software without restriction, including without limitation the rights
 * to use, copy, modify, merge, publish, distribute, sublicense, and/or sell
 * copies of the Software, and to permit persons to whom the Software is
 * furnished to do so, subject to the following conditions:
 *
 * The above copyright notice and this permission notice shall be included in
 * all copies or substantial portions of the Software.
 *
 * THE SOFTWARE IS PROVIDED "AS IS", WITHOUT WARRANTY OF ANY KIND, EXPRESS OR
 * IMPLIED, INCLUDING BUT NOT LIMITED TO THE WARRANTIES OF MERCHANTABILITY,
 * FITNESS FOR A PARTICULAR PURPOSE AND NONINFRINGEMENT. IN NO EVENT SHALL THE
 * AUTHORS OR COPYRIGHT HOLDERS BE LIABLE FOR ANY CLAIM, DAMAGES OR OTHER
 * LIABILITY, WHETHER IN AN ACTION OF CONTRACT, TORT OR OTHERWISE, ARISING FROM,
 * OUT OF OR IN CONNECTION WITH THE SOFTWARE OR THE USE OR OTHER DEALINGS IN
 * THE SOFTWARE.
 */
package edu.memphis.ccrg.linars;

import com.google.common.collect.Iterators;
import edu.memphis.ccrg.lida.framework.initialization.AgentStarter;
import org.opennars.entity.*;
import org.opennars.inference.TemporalRules;
import org.opennars.io.Symbols;
import org.opennars.io.Symbols.NativeOperator;
import org.opennars.language.*;
import org.opennars.main.Debug;

import java.nio.CharBuffer;
import java.util.*;

import static edu.memphis.ccrg.lida.framework.initialization.AgentStarter.nar;
import static org.opennars.io.Symbols.NativeOperator.COMPOUND_TERM_CLOSER;
import static org.opennars.io.Symbols.NativeOperator.COMPOUND_TERM_OPENER;


/**
 * Compound term as defined in the NARS-theory
 *
 * <AUTHOR> Wang
 * <AUTHOR> Hammer
 */
public abstract class CompoundTerm extends Term implements Iterable<Term> {
    /**
     * list of (direct) term
     */
    // TODO make final again
    public final Term[] term;
    
    /**
     * syntactic complexity of the compound, the sum of those of its term plus 1
     * 化合物的句法复杂度，该术语的总和加1
     */
    // TODO make final again
    public short complexity;

    /** Whether contains a variable */
    private boolean hasVariables, hasVarQueries, hasVarIndeps, hasVarDeps, hasIntervals;
    
    int containedTemporalRelations = -1;
    public int hash;
    private boolean normalized;

    public CompoundTerm() {
        term = new Term[3];
    }

    /**
     * method to get the operator of the compound
     */
    @Override public abstract NativeOperator operator();

    /**
     * clone method
     *
     * @return A clone of the compound term
     */
    @Override
    public abstract CompoundTerm clone();

    /**
     * subclasses should be sure to call init() in their constructors;
     * it is not done here to allow subclass constructors to set data before calling init()
     * 子类应该确保在其构造函数中调用init()；这里不这样做是为了允许子类构造函数在调用init()之前设置数据
     */
	public CompoundTerm(final Term[] components) {
        super();
        this.term = components;
    }

    public Set<String> getTermNames(Set<String> termNames) {
        if (termNames == null)
            termNames = new LinkedHashSet<>();
        for (final Term t : term) {
            if (t instanceof CompoundTerm)
                ((CompoundTerm)t).getTermNames(termNames);
            else
                termNames.add(t.name().toString());
        }
        return termNames;
    }

    // 递归找到所有组件，包括变量，并统计第几层第几个，同一个组件有多个，用list，组件用字符串做map的key，组件list做value
    // list的方案：1、第一个是层数，第二个是第几个；2、第一个是层数，第二个是第几个，第三个是变量名；3、元素为字符串，如第一层第一个是1_1，第二层第二个是2_2
    // list方案1的优劣势：优势是简单，都是数字，变量名由map的key来表示，劣势是不好找到变量名，需要遍历map的value
    // 以上是精确对应两个复合项的方案，还有其他方案：直接将复合项分割转为数组，然后一一对比？但位置不对应，不好找到变量名
    // todo 层级还需考虑和位置结合嵌套，不能只是层级和位置，从第一层开始，第三个里的第二个里的第五个，用13_22_35，
    //  层级和位置结合嵌套，不是简单的层级和位置，层级可省略，下划线代替层级，标记位置，如3_2_5
    public Map<String, List<Integer>> getTermNamesWithIndex(Map<String, List<Integer>> termNames, int level, int index) {
        if (termNames == null)
            termNames = new LinkedHashMap<>();
        for (final Term t : term) {
            if (t instanceof CompoundTerm) {
                ((CompoundTerm) t).getTermNamesWithIndex(termNames, level + 1, index);
            } else {
                String name = t.name().toString();
                List<Integer> list = termNames.get(name);
                if(list == null){
                    list = new ArrayList<>();
                    termNames.put(name, list);
                }
                list.add(level);
                list.add(index);
            }
            index++;
        }
        return termNames;
    }

    // 可能有用，从词项链接角度找部分
//    public List<TermLink> getTermLinks(List<TermLink> termLinks) {
//        if (termLinks == null)
//            termLinks = new ArrayList<>();
//        for (final Term t : term) {
//            if (t instanceof CompoundTerm)
//                ((CompoundTerm)t).getTermLinks(termLinks);
//            else
//                termLinks.add(new TermLink(t, this));
//        }
//        return termLinks;
//    }

    public void deepReplaceVar(Term varTerm1, Term term1) {
        if (term1 == null || varTerm1 == null) return;
        boolean replaced = false;
        for (int i = 0; i < term.length; i++) {
            final Term t = term[i];
            if (t instanceof CompoundTerm) {
                ((CompoundTerm)t).deepReplaceVar(varTerm1, term1);
            } else if (t.equals(varTerm1)) {
                term[i] = term1;
                replaced = true;
            }
        }
        if (replaced) {
            // 也要改tname，先拼接，再设置
            setTermName(makeName());
        }
    }
    public void deepReplaceTerm(Term[] array) {
        if (array == null) return;
        // array长度可能跟term不一样，不替换
//        if (array.length != term.length) return;
        for (int i = 0; i < array.length; i++) {
            // todo 如果类别一致，则直接替换，否则先转为目标类别再替换？父类转，而不是子类？
            if (term[i].getClass() == array[i].getClass()) {
                term[i] = array[i];
            } else {
                term[i] = array[i].clone();
            }
        }
        // 也要改tname，先拼接，再设置
        setTermName(makeName());
    }
    // 输入两个复合项，递归深度对比，精确对应两个复合项，
    public boolean deepEquals(CompoundTerm term1) {
        if (term1 == null) return false;
        if (term.length != term1.term.length) return false;
        for (int i = 0; i < term.length; i++) {
            final Term t = term[i];
            final Term t1 = term1.term[i];
            if (t instanceof CompoundTerm && t1 instanceof CompoundTerm) {
                if (!((CompoundTerm)t).deepEquals((CompoundTerm)t1)) return false;
            } else if (t instanceof CompoundTerm || t1 instanceof CompoundTerm) {
                return false;
            } else if (!t.equals(t1)) {
                return false;
            }
        }
        return true;
    }

    // 如果有变量，可跳过，对比非变量即可，变量的话，需要找到变量名，然后找到变量名对应的值，然后再对比
    public boolean deepEqualsVar(CompoundTerm term1) {
        if (term1 == null) return false;
        if (term.length != term1.term.length) return false;
        for (int i = 0; i < term.length; i++) {
            final Term t = term[i];
            final Term t1 = term1.term[i];
            if (t instanceof CompoundTerm && t1 instanceof CompoundTerm) {
                if (!((CompoundTerm)t).deepEqualsVar((CompoundTerm)t1)) return false;
            } else if (t instanceof Variable || t1 instanceof Variable) {
                // todo 变量的话，需要找到变量名，然后找到变量名对应的值，然后再对比
                continue;
            } else if (t instanceof CompoundTerm || t1 instanceof CompoundTerm) {
                return false;
            } else if (!t.equals(t1)) {
                return false;
            }
        }
        return true;
    }

    public String getComponentsStrs() {
        StringBuilder sb = new StringBuilder();
        for (final Term t : term) {
            if (t instanceof CompoundTerm) {
                sb.append(((CompoundTerm) t).getComponentsStrs());
            } else {
                sb.append(t.name().toString());
            }
        }
        return sb.toString();
    }

    public Term[] getTerms() {
        return term;
    }

    public Task toTask(char punctuation) {
        Sentence s = new Sentence<>(this, punctuation, new TruthValue((float) 1.0, 0.9, AgentStarter.nar.narParameters), new Stamp(nar, nar.memory));
        Task t = new Task(s, new BudgetValue(0.8f, 0.5f, 1, AgentStarter.nar.narParameters), Task.EnumType.INPUT);
        return t;
    }

    public static class ConvRectangle {
        public String index_variable = null;
        //size X, size Y, pos X, pos Y, min size X, min size Y
        public int[] term_indices = null;
        // the latter two for being able to assing a relative index for size too
        // 后两个也能够为尺寸分配相对索引
        public ConvRectangle(){}
    }

	//更新转换矩阵，根据索引处理复合词项
    public static ConvRectangle UpdateConvRectangle(final Term[] term) {
        String index_last_var = null;
        int minX = Integer.MAX_VALUE, minY = Integer.MAX_VALUE, maxX = 0, maxY = 0, 
                minsX = Integer.MAX_VALUE, minsY = Integer.MAX_VALUE;
        boolean hasTermIndices = false;
        boolean calculateTermIndices = true;
        for (final Term t : term) {
            if(t != null && t.term_indices != null) {
                if(!calculateTermIndices || 
                        (t.index_variable != null && index_last_var != null &&
                        (!t.index_variable.equals(index_last_var)))) {
                    calculateTermIndices = false;
                    hasTermIndices = false;
                    // different "channels", don't calculate term indices
                    // 不同的“通道”，不计算词项索引
                    continue;
                }
                hasTermIndices = true;
                final int size_X = t.term_indices[0];
                if(size_X < minsX)
                    minsX = size_X;
                final int size_Y = t.term_indices[1];
                if(size_Y < minsY)
                    minsY = size_Y;
                final int pos_X = t.term_indices[2];
                final int pos_Y = t.term_indices[3];
                if(pos_X < minX)
                    minX = pos_X;
                if(pos_Y < minY)
                    minY = pos_Y;
                if(pos_X+size_X > maxX)
                    maxX = pos_X+size_X;
                if(pos_Y+size_Y > maxY)
                    maxY = pos_Y+size_Y;
                
                index_last_var = t.index_variable;
            }
        }
        final ConvRectangle rect = new ConvRectangle();// = new ConvRectangle();
        if(hasTermIndices) {
            rect.term_indices = new int[6];
            rect.term_indices[0] = maxX-minX;
            rect.term_indices[1] = maxY-minY;
            rect.term_indices[2] = minX;
            rect.term_indices[3] = minY;
            rect.term_indices[4] = minsX;
            rect.term_indices[5] = minsY;
            rect.index_variable = index_last_var;
        }
        return rect;
    }
    
    /** call this after changing Term[] contents */
	//更改词项[]内容后调用此函数，初始化设置复杂度和变量。
    public void init(final Term[] term) {
        this.complexity = 1;
        this.hasVariables = this.hasVarDeps = this.hasVarIndeps = this.hasVarQueries = false;
        
        if(this.term_indices == null) {
            final ConvRectangle rect = UpdateConvRectangle(term);
            this.index_variable = rect.index_variable;
            this.term_indices = rect.term_indices;
        }
        
        for (final Term t : term) {
            this.complexity += t.getComplexity();
            hasVariables |= t.hasVar();
            hasVarDeps |= t.hasVarDep();
            hasVarIndeps |= t.hasVarIndep();
            hasVarQueries |= t.hasVarQuery();
            hasIntervals |= t.hasInterval();
        }
        invalidateName();
        if (!hasVar()) setNormalized(true);
    }

    //使名称无效
    public void invalidateName() {        
        this.setTermName(null); //invalidate name so it will be (re-)created lazily
        for (final Term t : term) {
            if (t.hasVar())
                if (t instanceof CompoundTerm)
                    ((CompoundTerm)t).invalidateName();
        }     
        setNormalized(false);
    }

    /** Must be Term return type because the type of Term may change with different arguments */
    //克隆。必须为词项返回类型，因为词项的类型可能会因不同的参数而改变 
	abstract public Term clone(final Term[] replaced);
    //深度克隆。
    @Override
    public CompoundTerm cloneDeep() {
        final Term c = clone(cloneTermsDeep());
        if (c == null)
            return null;
        if (Debug.DETAILED && c.getClass()!=getClass())
            // debug relevant, while it is natural due to interval
            // simplification to reduce to other term type, other cases should not appear
            // debug相关，虽然由于间隔简化而自然地减少到其他词项类型，但其他情况下不应出现
			System.out.println("cloneDeep resulted in different class: " + c + " from " + this);
        if (isNormalized())
            ((CompoundTerm)c).setNormalized(true);
        if(!(c instanceof CompoundTerm)) {
            return null;
        }
        return (CompoundTerm)c;
    }
    //将自变量转换为因变量
    public static void transformIndependentVariableToDependent(final CompoundTerm T) {
        //a special instance of transformVariableTermsDeep in 1.7
        final Term[] term=T.term;
        for (int i = 0; i < term.length; i++) {
            final Term t = term[i];
            if (t.hasVar()) {
                if (t instanceof CompoundTerm) {
                    transformIndependentVariableToDependent((CompoundTerm) t);
                } else if (t instanceof Variable && ((Variable)t).isIndependentVariable()) {
                    /* it's a variable */
                    term[i] = new Variable(""+Symbols.VAR_DEPENDENT+t.name().subSequence(1, t.name().length()));
                    // vars.get(t.toString());
                    assert term[i] != null;
                }
            }
        }
    }
    
    static final Interval conceptival = new Interval(1);
	//更换间隔
    private static void ReplaceIntervals(final CompoundTerm comp) {
        if (!comp.hasIntervals) {return;}
        comp.invalidateName();
        for(int i=0; i < comp.term.length; i++) {
            final Term t = comp.term[i];
            if(t instanceof Interval) {
                assert conceptival != null;
                comp.term[i] = conceptival;
                comp.invalidateName();
            } else if(t instanceof CompoundTerm) {
                ReplaceIntervals((CompoundTerm) t);
            }
        }
    }
    //更换间隔,对副本进行操作
    public static Term replaceIntervals(Term T) {
        if(T instanceof CompoundTerm) {
//            if (!(T instanceof LinkImpl)){
//                T=T.cloneDeep(); //we will operate on a copy
//            }
            if(T == null) {
                return null; //not a valid concept term
            }
            ReplaceIntervals((CompoundTerm) T);
        }
        return T;
    }
    //提取间隔
    private static void ExtractIntervals(final Memory mem, final List<Long> ivals, final CompoundTerm comp) {
        for(int i=0; i<comp.term.length; i++) {
            final Term t = comp.term[i];
            if(t instanceof Interval) {
                ivals.add(((Interval) t).time);
            } else if(t instanceof CompoundTerm) {
                ExtractIntervals(mem, ivals, (CompoundTerm) t);
            }
        }
    }
    //提取间隔
    public static List<Long> extractIntervals(final Memory mem, final Term T) {
        final List<Long> ret = new ArrayList<>();
        if(T instanceof CompoundTerm) {
            ExtractIntervals(mem, ret, (CompoundTerm) T);
        }
        return ret;
    }

    public static class UnableToCloneException extends RuntimeException {
        //无法克隆异常
        public UnableToCloneException(final String message) {
            super(message);
        }
        //填写堆栈跟踪
        @Override
        public synchronized Throwable fillInStackTrace() {
            if (Debug.DETAILED) {
                return super.fillInStackTrace();
            } else {
                // avoid recording stack trace for efficiency reasons
                // 出于效率原因，避免记录堆栈跟踪
                return this;
            }
        }
    }
	
    //深度克隆。
    public CompoundTerm cloneDeepVariables() {                
        final Term c = clone( cloneVariableTermsDeep() );
        if (c == null)
            return null;
        if (Debug.DETAILED && c.getClass()!=getClass())
            System.out.println("cloneDeepVariables resulted in different class: " + c + " from " + this);                
        
        final CompoundTerm cc = (CompoundTerm)c;
        cc.setNormalized(isNormalized());
        return cc;
    }

	//包含时间关系
    @Override
    public int containedTemporalRelations() {
        if (containedTemporalRelations == -1) {
            containedTemporalRelations = 0;
            if ((this instanceof Equivalence) || (this instanceof Implication)) {
                final int temporalOrder = this.getTemporalOrder();
                switch (temporalOrder) {
                    case TemporalRules.ORDER_FORWARD:
                    case TemporalRules.ORDER_CONCURRENT:
                    case TemporalRules.ORDER_BACKWARD:
                        containedTemporalRelations = 1;
                }                
            }
            for (final Term t : term)
                containedTemporalRelations += t.containedTemporalRelations();
        }
        return this.containedTemporalRelations;
    }

    /**
     * build a component list from terms
     * @return the component list
     */
	//根据项目建立词项数组
    public static Term[] termArray(final Term... t) {
        return t;
    }
	//根据项目建立词项数组列表
    public static List<Term> termList(final Term... t) {
        return Arrays.asList((Term[])t);
    }

    /* ----- utilities for oldName ----- */
    /**
     * default method to make the oldName of the current term from existing
     * fields.  needs overridden in certain subclasses
     * 添加操作。从现有字段中获取当前词项旧名的默认方法。在某些子类中需要重写
     * @return the oldName of the term
     */
    protected CharSequence makeName() {
        final NativeOperator op = operator();
        if (op == null){
            return "null_neo";
        }
        return makeCompoundName(op, term);
    }

	//若无内部名，则命名
    @Override
    public CharSequence name() {
        if (this.nameInternal() == null) {
            this.setTermName((String) makeName());
        }
        return this.nameInternal();
    }

    /**
     * default method to make the oldName of a compound term from given fields
     * 从给定字段制作复合词的旧名称的默认方法
     * @param op the term operator
     * @param arg the list of term
     * @return the oldName of the term
     */
    protected static CharSequence makeCompoundName(final NativeOperator op, final Term... arg) {
        int size = 1 + 1;
        final String opString = op.toString();
        size += opString.length();
        CharSequence tname = null;
        for (final Term t : arg) {
            tname = t.name();
            if (tname == null){
                tname = t.name();
            }
            size += 1 + tname.length();
        }
        final CharBuffer n = CharBuffer.allocate(size)
            .append(COMPOUND_TERM_OPENER.ch).append(opString);
            
        for (final Term t : arg) {            
            n.append(Symbols.ARGUMENT_SEPARATOR).append(t.name());
        }
        n.append(COMPOUND_TERM_CLOSER.ch);
        return n.compact().toString();
    }

    /* ----- utilities for other fields ----- */
    /**
     * report the term's syntactic complexity
     * 获取词项句法复杂性
     * @return the complexity value
     */
    @Override
    public short getComplexity() {
        return complexity;
    }

    /**
     * Check if the order of the term matters
     * <p>
     * commutative CompoundTerms: Sets, Intersections Commutative Statements:
     * Similarity, Equivalence (except the one with a temporal order)
     * Commutative CompoundStatements: Disjunction, Conjunction (except the one
     * with a temporal order)
     *
     * @return The default value is false
     */
	// 检查是否对等可交换复合词：集合，交集可交换语句：相似，等价（具有时间顺序的除外）。
    // 可交换复合状态：析取，合取（具有时间顺序的除外），product不能交换，所有前向顺序的都不能交换
    public boolean isCommutative() {
        return false;
    }

    /* ----- extend Collection methods to component list ----- */
    /**
     * get the number of term
     * 获取组件中的词项数
     * @return the size of the component list
     */
    final public int size() {
        return term.length;
    }

    /** Gives a set of all contained term, recursively */
   //递归给出一组所有包含的词项
	public Set<Term> getContainedTerms() {
        final Set<Term> s = new LinkedHashSet(getComplexity());
        for (final Term t : term) {
            s.add(t);
            if (t instanceof CompoundTerm)
                s.addAll( ((CompoundTerm)t).getContainedTerms() );
        }
        return s;
    }

    /**
     * Clone the component list
     * 克隆组件列表
     * @return The cloned component list
     */
    public Term[] cloneTerms(final Term... additional) {
        return cloneTermsAppend(term, additional);
    }

    /**
     * Cloned array of Terms, except for one or more Terms.
     * 克隆词项数组，除了一个或多个词项
     * @param toRemove
     * @return the cloned array with the missing terms removed, OR null if no terms were actually removed when requireModification=true
     *         克隆数组，删除缺少的词项，如果requireModification=true时没有实际删除词项，则返回null
     */
    public Term[] cloneTermsExcept(final boolean requireModification, final Term[] toRemove) {
        //TODO if deep, this wastes created clones that are then removed.  correct this inefficiency?
        // 如果深则浪费创建的克隆，然后删除它们。纠正这种低效率？
        final List<Term> l = asTermList();
        boolean removed = false;
                
        for (final Term t : toRemove) {
            if (l.remove(t))
                removed = true;
        }
        if ((!removed) && (requireModification))
            return null;
        return l.toArray(new Term[0]);
    }
    
    /**
     * Deep clone an array list of terms
     * 深度克隆词项数组列表
     * @param original The original component list
     * @return an identical and separate copy of the list
     */
    public static Term[] cloneTermsAppend(final Term[] original, final Term[] additional) {
        if (original == null) {
            return null;
        }
        final int L = original.length + additional.length;
        if (L == 0)
            return original;
        
        //TODO apply preventUnnecessaryDeepCopy to more cases
        final Term[] arr = new Term[L];
        
        int i;
        int j = 0;
        Term[] srcArray = original;
        for (i = 0; i < L; i++) {            
            if (i == original.length) {
                srcArray = additional;
                j = 0;
            }
            arr[i] = srcArray[j++];
        }
        return arr;
    }
    //作为词项列表
    public List<Term> asTermList() {        
        final List l = new ArrayList(term.length);
        addTermsTo(l);
        return l;
    }

    /** forced deep clone of terms */
	//强制深层克隆词项
    public Term[] cloneTermsDeep() {
        final Term[] l = new Term[term.length];
        for (int i = 0; i < l.length; i++) {
            l[i] = term[i].cloneDeep();
            if(l[i] == null) {
                return null;
            }
        }
        return l;        
    }    
    //强制深层克隆词项变量
	public Term[] cloneVariableTermsDeep() {
        final Term[] l = new Term[term.length];
        for (int i = 0; i < l.length; i++)  {     
            Term t = term[i];
            if (t.hasVar()) {
                if (t instanceof CompoundTerm) {
                    t = ((CompoundTerm)t).cloneDeepVariables();
                } else  /* it's a variable */
                    t = t.clone();
            }
            l[i] = t;            
        }
        return l;
    }
    
    /** forced deep clone of terms */
	//强制深层克隆词项列表
    public List<Term> cloneTermsListDeep() {
        final List<Term> l = new ArrayList(term.length);
        for (final Term t : term)
            l.add(t.clone());
        return l;        
    }
    //洗牌
    public static void shuffle(final Term[] ar, final Random randomNumber) {
        if (ar.length < 2)  {return;}

        for (int i = ar.length - 1; i > 0; i--) {
            final int index = randomNumber.nextInt(i + 1);
            // Simple swap 简单交换
            final Term a = ar[index];
            ar[index] = ar[i];
            ar[i] = a;
        }
    }

    /**
     * Check whether the compound contains a certain component
     * Also matches variables, ex: (&&,<a --> b>,<b --> c>) also contains <a --> #1>
     * 检查复合词项是否包含某种成分，还匹配变量。
     *
     * extra comment because it is a Implementation detail - question:
     * ^^^ is this right? if so then try containsVariablesAsWildcard
     * 额外的评论，因为它是一个实现细节 - 问题：^^^这是正确的吗？如果是这样，那么尝试包含变量作为通配符
     *
     * @param t The component to be checked
     * @return Whether the component is in the compound
     */
    @Override
    public boolean containsTerm(final Term t) {        
        return Terms.contains(term, t);
        //return Terms.containsVariablesAsWildcard(term, t);
    }

    /**
     * Recursively check if a compound contains a term
     * 递归检查复合词是否包含词项
     * @param target The term to be searched
     * @return Whether the target is in the current term
     */
    @Override
    public boolean containsTermRecursively(final Term target) { 
        if (super.containsTermRecursively(target))
            return true;
        for (final Term term : term) {            
            if (term.containsTermRecursively(target)) {
                return true;
            }
        }
        return false;
    }

    /**
     * Recursively count how often the terms are contained
     * 递归计算包含这些词项的次数
     * @param map The count map that will be created to count how often each term occurs
     * @return The counts of the terms
     */
    @Override
    public Map<Term, Integer> countTermRecursively(Map<Term,Integer> map) { 
        if(map == null) {
            map = new LinkedHashMap<>();
        }
        map.put(this, map.getOrDefault(this, 0) + 1);
        for (final Term term : term) {            
            term.countTermRecursively(map);
        }
        return map;
    }
    
    /**
     * Add all the components of term t into components recursively
     * 将词项所有组件递归添加到组件中
     * @param t The term
     * @param components The components
     * @return 
     */
    public static Set<Term> addComponentsRecursively(Term t, Set<Term> components) {
        if(components == null) {
            components = new LinkedHashSet<Term>();
        }
        components.add(t);
        if(t instanceof CompoundTerm) {
            CompoundTerm cTerm = (CompoundTerm) t;
            for(Term component : cTerm) {
                addComponentsRecursively(component, components);
            }
        }
        return components;
    }

    /**
     * Check whether the compound contains all term of another term, or that term as a whole
     * 检查复合词是否包含另一个词项的所有部分，或将其作为一个整体
     * @param t The other term
     * @return Whether the term are all in the compound
     */
    public boolean containsAllTermsOf(final Term t) {
        if (getClass() == t.getClass()) { //(t instanceof CompoundTerm) {
            return Terms.containsAll(term, ((CompoundTerm) t).term );
        } else {
            return Terms.contains(term, t);
        }
    }

    /**
     * Try to replace a component in a compound at a given index by another one
     * 尝试用给定的索引替换另一索引中的复合词中的组件
     * @param index The location of replacement
     * @param t The new component
     * @param memory Reference to the memory
     * @return The new compound
     */
    public Term setComponent(final int index, final Term t, final Memory memory) {
        final List<Term> list = asTermList();//Deep();
        list.remove(index);
        if (t != null) {
            if (getClass() != t.getClass()) {
                list.add(index, t);
            } else {
                //final List<Term> list2 = ((CompoundTerm) t).cloneTermsList();
                final Term[] tt = ((CompoundTerm)t).term;
                for (int i = 0; i < tt.length; i++) {
                    list.add(index + i, tt[i]);
                }
            }
        }
        if (this.isCommutative()) {
            Term[] ret = list.toArray(new Term[0]);
            return Terms.term(this, ret);
        }
        return Terms.term(this, list);
    }

    /* ----- variable-related utilities ----- */
    /**
     * Whether this compound term contains any variable term
     * 此复合词是否包含变量
     * @return Whether the name contains a variable
     */
    @Override
    public boolean hasVar() {
        return hasVariables;    
    }
    //此复合词是否包含因变量
    @Override
    public boolean hasVarDep() {
        return hasVarDeps;
    }
    //此复合词是否包含自变量
    @Override
    public boolean hasVarIndep() {
        return hasVarIndeps;
    }
     //此复合词是否包含询问变量
    @Override
    public boolean hasVarQuery() {
        return hasVarQueries;
    }
    //此复合词是否包含转换
    @Override
    public boolean hasInterval() {
        return hasIntervals;
    }
    
    /**
     * Recursively apply a substitute to the current CompoundTerm May return null if the term can not be created
     * 递归地将替代项应用于当前的复合词。如果无法创建该词，则可能返回null
     * @param subs
     */
    public Term applySubstitute(final Map<Term, Term> subs) {   
        if ((subs == null) || (subs.isEmpty())) {            
            return this;//.clone();
        }
        final Term[] tt = new Term[term.length];
        boolean modified = false;
        
        for (int i = 0; i < tt.length; i++) {
            final Term t1 = tt[i] = term[i];
            
            if (subs.containsKey(t1)) {
                Term t2 = subs.get(t1);                            
                while (subs.containsKey(t2)) {
                    t2 = subs.get(t2);
                }
                // prevents infinite recursion 防止无限递归
                if (!t2.containsTerm(t1)) {
                    tt[i] = t2; //t2.clone();
                    modified = true;
                }
            } else if (t1 instanceof CompoundTerm) {
                final Term ss = ((CompoundTerm) t1).applySubstitute(subs);
                if (ss!=null) {
                    tt[i] = ss;
                    if (!tt[i].equals(term[i]))
                        modified = true;
                }
            }
        }
        if (!modified) return this;
        
        if (this.isCommutative()) {         
            Arrays.sort(tt);
        }
        return this.clone(tt);
    }

    /** returns result of applySubstitute, if and only if it's a CompoundTerm. 
     * otherwise it is null */
	//当且仅当其为复合词时，返回apply替代的结果。否则为null
    public CompoundTerm applySubstituteToCompound(final Map<Term, Term> substitute) {
        final Term t = applySubstitute(substitute);
        if (t instanceof CompoundTerm)
            return ((CompoundTerm)t);
        return null;
    }
    
    /* ----- link CompoundTerm and its term ----- */
    /**
     * Build TermLink templates to constant term and subcomponents
     * <p>
     * The compound type determines the link type; the component type determines
     * whether to build the link.
     *
     * @return A list of TermLink templates
     */
	//将词项链接模板构建为常数词项和子组件。组件类型确定是否构建链接。
    public List<TermLink> prepareComponentLinks() {
        //complexity seems like an upper bound for the resulting number of componentLinks. 
        //so use it as an initial size for the array list
		//复杂度似乎是所产生的componentLink数量的上限。 所以用它作为数组列表的初始大小
//        final List<TermLink> componentLinks = new ArrayList<>(getComplexity());
        final List<TermLink> componentLinks = Collections.synchronizedList(new ArrayList<TermLink>(getComplexity()));
        return Terms.prepareComponentLinks(componentLinks, this);
    }
     //添加词项
    final public void addTermsTo(final Collection<Term> c) {
        Collections.addAll(c, term);
    }

    //哈希化
    @Override
    public int hashCode() {
        CharSequence n = name();
        if (n == null) {
            n = name();
        }
        return n.hashCode();
    }
    //比较
    @Override
    public int compareTo(final AbstractTerm that) {
        if (that==this) { 
            return 0;
        }
        return super.compareTo(that);
    }
    //相等否
    @Override
    public boolean equals(final Object that) {
        if (that==this) return true;                
        if (!(that instanceof Term))
            return false;
        CharSequence n = name();
        if (n == null)
            n = name();
        CharSequence n2 = ((Term)that).name();
        if (n2 == null)
            n2 = ((Term)that).name();
        return n.equals(n2);
//        return name()
//                .equals(((Term)that).name());
    }   
    //设置归一化
    public void setNormalized(final boolean b) {
        this.normalized = b;
    }
    //归一化
    public boolean isNormalized() {
        return normalized;
    }
    //克隆词项替换
    public Term[] cloneTermsReplacing(final Term from, final Term to) {
        final Term[] y = new Term[term.length];
        int i = 0;
        for (Term x : term) {
            if (x.equals(from))
                x = to;
            y[i++] = x;
        }
        return y;
    }
    //迭代器
    @Override
    public Iterator<Term> iterator() {
        return Iterators.forArray(term);
    }
}
