/* 
 * The MIT License
 *
 * Copyright 2018 The OpenNARS authors.
 *
 * Permission is hereby granted, free of charge, to any person obtaining a copy
 * of this software and associated documentation files (the "Software"), to deal
 * in the Software without restriction, including without limitation the rights
 * to use, copy, modify, merge, publish, distribute, sublicense, and/or sell
 * copies of the Software, and to permit persons to whom the Software is
 * furnished to do so, subject to the following conditions:
 *
 * The above copyright notice and this permission notice shall be included in
 * all copies or substantial portions of the Software.
 *
 * THE SOFTWARE IS PROVIDED "AS IS", WITHOUT WARRANTY OF ANY KIND, EXPRESS OR
 * IMPLIED, INCLUDING BUT NOT LIMITED TO THE WARRANTIES OF MERCHANTABILITY,
 * FITNESS FOR A PARTICULAR PURPOSE AND NONINFRINGEMENT. IN NO EVENT SHALL THE
 * AUTHORS OR COPYRIGHT HOLDERS BE LIABLE FOR ANY CLAIM, DAMAGES OR OTHER
 * LIABILITY, WHETHER IN AN ACTION OF CONTRACT, TORT OR OTHERWISE, ARISING FROM,
 * OUT OF OR IN CONNECTION WITH THE SOFTWARE OR THE USE OR OTHER DEALINGS IN
 * THE SOFTWARE.
 */
package org.opennars.operator.mental;

import edu.memphis.ccrg.lida.framework.initialization.AgentStarter;
import org.opennars.control.DerivationContext;
import org.opennars.control.InferenceControl;
import org.opennars.entity.BudgetValue;
import edu.memphis.ccrg.linars.Concept;
import org.opennars.entity.Task;
import org.opennars.interfaces.Timable;
import edu.memphis.ccrg.linars.Term;
import org.opennars.operator.Operation;
import org.opennars.operator.Operator;
import edu.memphis.ccrg.linars.Memory;

import java.util.List;

/**
 * Operator that activates a concept
 */
public class Consider extends Operator {

    public static BudgetValue budgetMentalConcept(final Operation o) {
        return o.getTask().budget.clone();
    }
    
    public Consider() {
        super("^consider");
    }

    /**
     * To activate a concept as if a question has been asked about it
     *
     * @param args Arguments, a Statement followed by an optional tense
     * @param memory The memory in which the operation is executed
     * @return Immediate results as Tasks
     */
    @Override
    public List<Task> execute(final Operation operation, final Term[] args, final Memory memory, final Timable time) {
        final Term term = args[1];
        
        final Concept concept = memory.conceptualize(Consider.budgetMentalConcept(operation), term);
        
        final DerivationContext cont = new DerivationContext(memory, AgentStarter.nar.narParameters, time);
        cont.setCurrentConcept(concept);
        InferenceControl.fireConcept(cont, 1);
        
        return null;
    }

}
