package edu.memphis.ccrg.lida.motivation.workspace;

import edu.memphis.ccrg.lida.framework.ModuleName;
import edu.memphis.ccrg.lida.framework.shared.*;
import edu.memphis.ccrg.lida.motivation.shared.FeelingNode;
import edu.memphis.ccrg.lida.motivation.shared.FeelingNodeImpl;
import edu.memphis.ccrg.lida.workspace.Workspace;
import edu.memphis.ccrg.lida.workspace.WorkspaceContent;
import edu.memphis.ccrg.lida.workspace.WorkspaceImpl;
import edu.memphis.ccrg.lida.workspace.workspacebuffers.BroadcastQueue;
import edu.memphis.ccrg.lida.workspace.workspacebuffers.WorkspaceBuffer;

import java.util.ArrayList;
import java.util.Collection;
import java.util.HashSet;
import java.util.logging.Level;
import java.util.logging.Logger;

/**
 * This {@link Workspace} adds percepts directly to the CSM as opposed to the PerceptualBuffer. It also builds
 * temporal links whenever a non-feeling node is added to the CSM.
 * <AUTHOR>
 */
public class MotivationWorkspace extends WorkspaceImpl {
	
	private static final Logger logger = Logger.getLogger(MotivationWorkspace.class.getCanonicalName());
	private LinkCategory temporalCategory;
	private Object csmLock = new Object();	// 同时只一个

	WorkspaceBuffer csm = (WorkspaceBuffer)getSubmodule(ModuleName.CurrentSM);
	NodeStructure csmStructure = csm.getBufferContent(null);

	/**
	 * Sets the temporal {@link LinkCategory}.
	 * @param c a {@link LinkCategory}
	 */
	void setTemporalCategory(LinkCategory c){
		temporalCategory = c;
	}
	
	@Override
	public void receivePercept(Link l) {
		WorkspaceBuffer buffer = (WorkspaceBuffer)getSubmodule(ModuleName.CurrentSM);
		NodeStructure ns = buffer.getBufferContent(null);
		synchronized (csmLock) {
			ns.addLink(l,l.getFactoryType());
		}
	}
	@Override
	public void receivePercept(NodeStructure ns) {
		WorkspaceBuffer buffer = (WorkspaceBuffer)getSubmodule(ModuleName.CurrentSM);
		synchronized (csmLock) {
			buffer.addBufferContent((WorkspaceContent) ns);
		}
	}

	@Override
	public void receivePercept(Node n) {
		csmStructure.addNode(n, n.getFactoryType());
		// 不是感觉节点，则添加临时链接
		if(!(n instanceof FeelingNode)){
			buildTemporalLink(n, csmStructure);
		}
	}

	/*
	 * It appears preferable to perform this operation right when a node is added to the CSM as opposed to performing it cyclically in a task.
	 * 似乎最好在将节点添加到CSM中时正确执行此操作，而不是在任务中循环执行它
	 */
	private void buildTemporalLink(Node sink, NodeStructure csmStructure) {
		// 与前广播联系
		BroadcastQueue queue = (BroadcastQueue) getSubmodule(ModuleName.BroadcastQueue);
		NodeStructure latestBroadcast = queue.getPositionContent(0);
		if (latestBroadcast != null) {
			// 新信息
			if(!latestBroadcast.containsNode(sink)){ 
				Node source = getFirstEventNode(latestBroadcast, temporalCategory);
				if(source != null){
					// Avoid endlessly exciting source
					if(!csmStructure.containsNode(source)){
						csmStructure.addNode(source, source.getFactoryType());
					}
					Collection<Node> visitedNodes = new HashSet<Node>();
					//Initialize by marking source as already visited, if encountered again there's a cycle
					//通过将源标记为已访问进行初始化，如果再次遇到，则存在一个循环
					visitedNodes.add(source);
					synchronized (csmLock) {
						boolean createsCycle = isCycle(sink, csmStructure, visitedNodes);
						if(!createsCycle){						
							csmStructure.addDefaultLink(source, sink, temporalCategory, 1.0, 0.0);						
						}else{
							logger.log(Level.INFO, "Cycle avoided: Source: {0}, Sink: {1}", new Object[]{source, sink});
							logger.log(Level.INFO, "BQ structure: {0}",latestBroadcast);
							logger.log(Level.INFO, "CSM structure: {0}",csmStructure);
						}
					}
				}	
			}
		}
	}
	/**
	 * Returns true if a traversal of 'graph' from 'currentNode' encounters a Node in 'visitedNodes'.
	 * 如果从“ currentNode”遍历“ graph”时遇到“ visitedNodes”中的Node，则返回true
	 * @param currentNode current node being explored
	 * @param graph a NodeStructure graph
	 * @param visitedNodes Collection of explored nodes
	 * @return true if a node is encountered twice, else false
	 */
	static boolean isCycle(Node currentNode, NodeStructure graph, Collection<Node> visitedNodes){
		if(visitedNodes.contains(currentNode)){
			return true;
		}
		visitedNodes.add(currentNode);
		for(Linkable lnk: graph.getConnectedSinks(currentNode)){
			//Just to ensure lnk is a plain node
			if(lnk instanceof Node && !(lnk instanceof FeelingNode)){
				//recursive call using sink of currentNode
				if(isCycle((Node)lnk, graph, visitedNodes)){
					return true; //As soon as any cycle is found, then return
				}
			}
		}
		return false;
	}
	
	/*
	 * Returns the earliest (based on temporal links) and maximally activated event node in the
	 * specified {@link NodeStructure}.
	 */
	private static Node getFirstEventNode(NodeStructure ns, LinkCategory temporalCategory){
		Collection<Node> temporalSources = new HashSet<Node>();
		Collection<Linkable> temporalSinks = new HashSet<Linkable>();
		for(Link l: ns.getLinks()){
			if(l.getCategory().equals(temporalCategory)){
				temporalSources.add(l.getSource());
				temporalSinks.add(l.getSink());
			}
		}
		Collection<Node> firstEventNodes = new ArrayList<Node>();
		for(Node n: temporalSources){
			if(!temporalSinks.contains(n)){ //First in the chain
				firstEventNodes.add(n);
			}
		}
		Node result = null;
		if(firstEventNodes.isEmpty()){
			//No temporal link are in the NodeStructure, get the max.
			result = getMaxEventNode(ns.getNodes()); 
		}else{
			//There may be multiple nodes occurring first in a chain
			result = getMaxEventNode(firstEventNodes); 
		}
		return result;
	}
	/*
	 * Returns the maximally activated non-feeling node among the specific Collection.
	 */
	private static Node getMaxEventNode(Collection<Node> nodes) {
		double maxActivation = 0.0;
		Node maxEventNode = null;
		for (Node n: nodes) {
			if (!(n instanceof FeelingNodeImpl)) {
				if (n.getActivation() > maxActivation) {
					maxActivation = n.getActivation();
					maxEventNode = n;
				}
			}
		}
		return maxEventNode;
	}	
}