/* 
 * The MIT License
 *
 * Copyright 2018 The OpenNARS authors.
 *
 * Permission is hereby granted, free of charge, to any person obtaining a copy
 * of this software and associated documentation files (the "Software"), to deal
 * in the Software without restriction, including without limitation the rights
 * to use, copy, modify, merge, publish, distribute, sublicense, and/or sell
 * copies of the Software, and to permit persons to whom the Software is
 * furnished to do so, subject to the following conditions:
 *
 * The above copyright notice and this permission notice shall be included in
 * all copies or substantial portions of the Software.
 *
 * THE SOFTWARE IS PROVIDED "AS IS", WITHOUT WARRANTY OF ANY KIND, EXPRESS OR
 * IMPLIED, INCLUDING BUT NOT LIMITED TO THE WARRANTIES OF MERCHANTABILITY,
 * FITNESS FOR A PARTICULAR PURPOSE AND NONINFRINGEMENT. IN NO EVENT SHALL THE
 * AUTHORS OR COPYRIGHT HOLDERS BE LIABLE FOR ANY CLAIM, DAMAGES OR OTHER
 * LIABILITY, WHETHER IN AN ACTION OF CONTRACT, TORT OR OTHERWISE, ARISING FROM,
 * OUT OF OR IN CONNECTION WITH THE SOFTWARE OR THE USE OR OTHER DEALINGS IN
 * THE SOFTWARE.
 */
package edu.memphis.ccrg.linars;

import edu.memphis.ccrg.lida.framework.ModuleName;
import edu.memphis.ccrg.lida.framework.initialization.AgentStarter;
import edu.memphis.ccrg.lida.framework.shared.NodeStructure;
import edu.memphis.ccrg.lida.framework.shared.NodeStructureImpl;
import edu.memphis.ccrg.lida.pam.tasks.ProcessGBufferTask;
import edu.memphis.ccrg.lida.pam.tasks.SelectConceptTask;
import edu.memphis.ccrg.lida.workspace.WorkspaceImpl;
import edu.memphis.ccrg.lida.workspace.workspacebuffers.WorkspaceBuffer;
import org.opennars.control.DerivationContext;
import org.opennars.control.concept.ProcessTask;
import org.opennars.entity.*;
import org.opennars.entity.Stamp.BaseEntry;
import org.opennars.inference.BudgetFunctions;
import org.opennars.interfaces.Resettable;
import org.opennars.interfaces.Timable;
import org.opennars.io.Channel;
import org.opennars.io.Symbols;
import org.opennars.io.events.EventEmitter;
import org.opennars.io.events.Events;
import org.opennars.io.events.Events.ResetEnd;
import org.opennars.io.events.Events.ResetStart;
import org.opennars.io.events.Events.TaskRemove;
import org.opennars.io.events.OutputHandler.DEBUG;
import org.opennars.io.events.OutputHandler.IN;
import org.opennars.io.events.OutputHandler.OUT;
import org.opennars.language.*;
import org.opennars.main.Debug;
import org.opennars.main.Nar;
import org.opennars.main.Parameters;
import org.opennars.operator.Operation;
import org.opennars.operator.Operator;
import org.opennars.plugin.mental.Emotions;
import org.opennars.plugin.mental.InternalExperience;
import org.opennars.plugin.perception.NarseseChannel;
import org.opennars.storage.Bag1;
import org.opennars.storage.Buffer;
import org.opennars.storage.InternalExperienceBuffer;

import java.io.Serializable;
import java.util.Iterator;
import java.util.LinkedHashMap;
import java.util.Map;
import java.util.Random;

import static edu.memphis.ccrg.lida.framework.FrameworkModuleImpl.taskSpawner;
import static edu.memphis.ccrg.lida.framework.initialization.AgentStarter.nar;
import static edu.memphis.ccrg.lida.framework.initialization.AgentStarter.pam;
import static org.opennars.inference.BudgetFunctions.truthToQuality;


/**
 * Memory consists of the run-time state of a Nar, including: 内存由Nar的运行时状态组成
 *   * term and concept memory
 *   * reasoner state
 *   * etc.
 * <br>
 * Excluding input/output channels which are managed by a Nar. 不包括由Nar管理的输入/输出通道
 * <br>
 * A memory is controlled by zero or one Nar's at a given time. 在给定的时间，内存受零或一个Nar的控制
 * <br>
 * Memory is serializable so it can be persisted and transported. 内存是可序列化的，因此可以持久存储和传输
 */
public class Memory extends NodeStructureImpl implements Serializable, Iterable<Concept>, Resettable {
     /* Nar parameters */
    public Parameters narParameters;
    public transient EventEmitter event;
    // 内部经验操作
    public InternalExperience internalExperience = null;
    public Task lastDecision = null;

    public boolean allowExecution = true;
    public boolean multiAction = false;
    public String lastAction = "default";
    public String last2Action = "default";

    public final long randomSeed = 1;
    public final Random randomNumber = new Random(randomSeed);
    
    // todo make sense of this class and de-obfuscate 理解此类并消除混淆
    public Bag1<Concept,Term> concepts;
    public long narId = 0;
    //emotion meter keeping track of global emotion 情绪计跟踪全球情绪
    public Emotions emotion = null;

    /* InnateOperator registry. Containing all registered operators of the system 注册表。包含系统的所有注册算子*/
    public Map<CharSequence, Operator> operators;
    
    /* a mutex for novel and new taskks 新奇和新任务的互斥体*/
    private final Boolean tasksMutex = Boolean.TRUE;
    
    /* New tasks with novel composed terms, for delayed and selective processing*/
    public final Buffer/*<Task<Term>,Sentence<Term>>*/ globalBuffer;
    public InternalExperienceBuffer internalExperienceBuffer;
    public NarseseChannel narseseChannel = null;
    
    /* Input event tasks that were either input events or derived sequences*/
    public final Bag1<Task<Term>,Sentence<Term>> recent_operations;  //only used for the optional legacy handling for comparison purposes
    
    final Boolean localInferenceMutex = false;

    public boolean checked = false;
    public boolean isjUnit = false;
    
    /* ---------- Constructor ---------- */

    public Memory(){
        this(AgentStarter.nar.narParameters,
                new Bag1(AgentStarter.nar.narParameters.CONCEPT_BAG_SIZE),
                new Buffer(nar, AgentStarter.nar.narParameters.GLOBAL_BUFFER_LEVELS,AgentStarter.nar.narParameters.GLOBAL_BUFFER_SIZE, AgentStarter.nar.narParameters),
                new Buffer(nar, AgentStarter.nar.narParameters.SEQUENCE_BAG_LEVELS,AgentStarter.nar.narParameters.SEQUENCE_BAG_SIZE, AgentStarter.nar.narParameters),
                new Bag1<>(AgentStarter.nar.narParameters.OPERATION_BAG_SIZE));
    }

    /**
     * Create a new memory
     */
    public Memory(final Parameters narParameters, final Bag1<Concept,Term> concepts,
                  final Buffer globalBuffer, final Buffer seq_current,
                  final Bag1<Task<Term>,Sentence<Term>> recent_operations) {
        this.narParameters = narParameters;
        this.event = new EventEmitter();
        this.concepts = concepts;
        this.globalBuffer = globalBuffer;
        this.globalBuffer.mem = this;
        this.recent_operations = recent_operations;
        this.globalBuffer.seq_current = seq_current;
        this.globalBuffer.seq_current.mem = this;
        this.operators = new LinkedHashMap<>();

        int levels = narParameters.INTERNAL_BUFFER_LEVELS;
        int capacity = narParameters.INTERNAL_BUFFER_SIZE;
        this.internalExperienceBuffer = new InternalExperienceBuffer(AgentStarter.nar, levels, capacity, narParameters);
        this.internalExperienceBuffer.mem = this;
//        this.internalExperienceBuffer = null; //overwritten
        reset();
    }
    
    public void reset() {
        event.emit(ResetStart.class);
        synchronized (concepts) {
            concepts.clear();
        }
        synchronized (tasksMutex) {
            globalBuffer.clear();
        }
        synchronized(this.globalBuffer.seq_current) {
            this.globalBuffer.seq_current.clear();
        }
        if(emotion != null) {
            emotion.resetEmotions();
        }
        if(internalExperienceBuffer != null) {
            internalExperienceBuffer.clear();
        }
        this.lastDecision = null;
        randomNumber.setSeed(randomSeed);
        event.emit(ResetEnd.class);
    }

    /* ---------- conversion utilities ---------- */
    /**
     * Get an existing Concept for a given name
     * <p>
     * called from Term and ConceptWindow.
     *
     * @param t the name of a concept
     * @return a Concept or null
     */
	//获取给定名称的现有概念
    public Concept concept(final Term t) {
        synchronized (concepts) {
            return concepts.get(CompoundTerm.replaceIntervals(t).toString());
        }
    }

    /**
     * Get the Concept associated to a Term, or create it.
     * 
     *   Existing concept: apply tasklink activation (remove from bag, adjust budget, reinsert)
     *   New concept: set initial activation, insert
     *   Subconcept: extract from cache, apply activation, insert
     * 
     * If failed to insert as a result of null bag, returns null
     * A displaced Concept resulting from insert is forgotten (but may be stored in optional  subconcept memory
     * 
     * @param term indicating the concept
     * @return an existing Concept, or a new one, or null 
     */
	//获取或创建与词项相关概念。现有概念：激活任务链接（从包中删除，调整预算，重新插入）
    //新概念.设置初始激活，插入子概念：从缓存中提取，应用激活，插入
	//如果由于空包导致插入失败，则返回空A 因插入而导致的置换概念被遗忘（但可以存储在可选的子概念存储器中）
    public Concept conceptualize(final BudgetValue budget, Term term) {   
        // 概念单元化，复合词先舍弃？  || term instanceof CompoundTerm
        if(term instanceof Interval) {
            return null;
        }
        term = CompoundTerm.replaceIntervals(term);

        final Concept displaced;
        Concept concept;

        synchronized (concepts) {
            // 也算任务词项和概念词项一对一映射，在选推理的另一个前提时，选上下文相关=事件驱动
            // 概念应该是单词项，而不是复合词，复合词是事件？事件只是有时间的词项？
            concept = concepts.pickOut(term.toString());

            //see if concept is active 查看概念是否有效
            if (concept == null) {
                //create new concept, with the applied budget
                // 根据预算应用创建新概念 唯一新建概念的地方
                concept = new Concept(budget, term, this);
                //if (memory.logic!=null)
                //    memory.logic.CONCEPT_NEW.commit(term.getComplexity());
                emit(Events.ConceptNew.class, concept);
//            } else if (concept != null) {
            }else{
                //apply budget to existing concept 将预算应用于现有概念
                //memory.logic.CONCEPT_ACTIVATE.commit(term.getComplexity());
                BudgetFunctions.activate(concept.budget, budget, BudgetFunctions.Activating.TaskLink);
//            } else {
                //unable to create, ex: has variables
//                return null;
            }
            displaced = concepts.putBack(concept, cycles(narParameters.CONCEPT_FORGET_DURATIONS), this);//10
        }
        if (displaced == null) {
            //added without replacing anything 添加而不替换任何东西
            return concept;
        } else if (displaced == concept) {
            //not able to insert 无法插入，踢出了自己
            conceptRemoved(displaced);
            return null;
        } else {
            conceptRemoved(displaced);
            return concept;
        }
    }
    
    /* ---------- new task entries ---------- */
    /**
     * add new task that waits to be processed in the next cycleMemory
     */
	// 添加新任务等待下一个周期处理
    // todo 将task纳入buffer，受注意力调控，受动机管理，受pam处理
    // 来自记忆的任务直接处理了，这里是派生和新建的任务，类似海马的新记忆生成？海马buffer衰减要慢
    // 任务来源：想象、推理、感知、记忆，想象、推理前就要注意力调控，聚焦
    // 目前是符号推理，以后有其他模态
    public void addNewTask(final Task t, final String reason) {
//        synchronized (tasksMutex) {
//        putInBuffer(t, reason, this);
        this.globalBuffer.putIn(t);
//        }

        Sentence ts = t.sentence;
        // 如果词项是相似性、蕴涵、等价式，则加入buffer
        boolean isGoalRelated = ts.term instanceof Similarity || ts.term instanceof Implication || ts.term instanceof Equivalence;

        if (t.sentence.isGoal() || isGoalRelated) {
            WorkspaceBuffer buffer = (WorkspaceBuffer) ((WorkspaceImpl) pam.getListener()).getSubmodule(ModuleName.GoalGraph);
            NodeStructure nsmem = buffer.getBufferContent(null);
            Memory mem = (Memory) nsmem;
            // 如果mem就是this，则不加入
            if (mem != this) {
//                putInBuffer(t, reason, mem);
                mem.globalBuffer.putIn(t);
            }
        }

      //  logic.TASK_ADD_NEW.commit(t.getPriority());
        emit(Events.TaskAdd.class, t, reason);
        output(t);
    }

    private void putInBuffer(Task t, String reason, Memory memory) {
        if(reason.equals("Executed") || reason.equals("Derived") || reason.equals("emotion")  || reason.equals("Internal")) {
            // these go to internal experience first, and only after go to global buffer:
            // 这些首先进入内部经验，然后才进入全局缓冲区：
            // todo 因为常驻动机线程直接处理，不调用mem的循环，所以不触及。是否分内外待定，看情绪情感等方面
            memory.internalExperienceBuffer.putIn(t);
        } else {
            //todo 遍历term的所有termlinks，假如termlink的“cpstr”属性存在且不为空，则对termlink进行maketask
            memory.globalBuffer.putIn(t);
        }
    }

    public static boolean isJUnitTest() {
        final StackTraceElement[] stackTrace = Thread.currentThread().getStackTrace();
        final StackTraceElement[] list = stackTrace;
        for (final StackTraceElement element : list) {
            if (element.getClassName().startsWith("org.junit.")) {
                return true;
            }           
        }
        return false;
    }

    /**
     * Input task processing. Invoked by the outside or inside environment.
     * Outside: StringParser (addInput);
     * Inside: InnateOperator (feedback).
     *
     * @param time indirection to retrieve time
     * @param task The addInput task
     *
     * There are several types of new tasks, all added into the
     * newTasks list, to be processed in the next cycleMemory.
     * Some of them are reported and/or logged.
     * Input tasks with low priority are ignored, and the others are put into task buffer.
     */
	// 输入任务处理。 由外部或内部环境调用。外部：字符串解析器（添加输入）；内部：固有运算符（反馈）。
    // 有几种类型的新任务，都已添加到新的任务列表中，将在下一个周期内存中进行处理。 其中一些已报告和/或记录。
    // 低优先级的输入任务将被忽略，其他任务将被放入任务缓冲区。
	public void inputTask(final Timable time, final Task task, final boolean emitIn) {
        if(!checked) {
            checked = true;
            isjUnit = isJUnitTest();
        }
        if (task != null) {
            final Stamp s = task.sentence.stamp;
            if (s.getCreationTime() == -1)
                s.setCreationTime(time.time(), narParameters.DURATION);

            if(emitIn) {
                emit(IN.class, task);
            }
        }
    }

    /**
     * @param time indirection to retrieve time
     */
    public void inputTask(final Timable time, final Task t) {
        inputTask(time, t, true);
    }

    public void removeTask(final Task task, final String reason) {        
        emit(TaskRemove.class, task, reason);    
    }
    
    /**
     * ExecutedTask called in Operator.call
     *
     * @param operation The operation just executed
     * @param time indirection to retrieve time
     */
	//报告执行任务
    public void executedTask(final Timable time, final Operation operation, final TruthValue truth) {
        final Task opTask = operation.getTask();
       // logic.TASK_EXECUTED.commit(opTask.budget.getPriority());
                
        final Stamp stamp = new Stamp(time, this, Tense.Present);
        final Sentence sentence = new Sentence(
            operation,
            Symbols.JUDGMENT_MARK,
            truth,
            stamp);

        final BudgetValue budgetForNewTask = new BudgetValue(narParameters.DEFAULT_FEEDBACK_PRIORITY,
            narParameters.DEFAULT_FEEDBACK_DURABILITY,
            truthToQuality(sentence.getTruth()), narParameters);
        final Task newTask = new Task(sentence, budgetForNewTask, Task.EnumType.INPUT);

        newTask.setElemOfSequenceBuffer(true);

        // 当前类名和方法名
        System.out.print("Memory.executedTask: " + newTask + "\n");

        // 只是提示执行了这么个动作？动作完成，一个断言
        addNewTask(newTask, "Executed");
    }

	//输出
    public void output(final Task t) {
        
        final float budget = t.budget.summary();
        final float noiseLevel = 1.0f - (narParameters.VOLUME / 100.0f);
        
        if (budget >= noiseLevel) {  // only report significant derived Tasks
            emit(OUT.class, t);
            if (Debug.PARENTS) {
                emit(DEBUG.class, "Parent Belief\t" + t.parentBelief);
                emit(DEBUG.class, "Parent Task\t" + t.fromDerive + "\n\n");
            }
        }        
    }
    
    final public void emit(final Class c, final Object... signal) {        
        event.emit(c, signal);
    }

    final public boolean emitting(final Class channel) {
        return event.isActive(channel);
    }
    
    public void conceptRemoved(final Concept c) {
        emit(Events.ConceptForget.class, c);
    }
    
    public void cycle(final Nar nar) {
        event.emit(Events.CycleStart.class);
        channel2Task(nar);
        doAll(nar);
        event.emit(Events.CycleEnd.class);
        event.synch();
    }

    public void channel2Task(Nar nar) {
        //1. Channels to global buffer
        for(Channel c : nar.sensoryChannels.values()) {
            if(c.itemSize() == 0) {
                continue;
            }
            Task task = c.takeOut(); //retrieve an item from the Narsese channel
            if(task != null) {
                //optional: re-routing feature for "vision Narsese". not necessary but nice
                if(c == this.narseseChannel) {
                    if(nar.dispatchToSensoryChannel(task)) {
                        continue; //commented
                    }
                }
                // 当前类名和方法名
                System.out.print("Memory.channel2Task: " + task + "\n");

                //if it's not meant to enter another channel just put into global buffer
                //goes to global buffer, but printing it
                // todo 根据模态和虚实添加到不同的buffer
                this.addNewTask(task, "Perceived");
            }
        }
    }

    public void doAll(Nar nar) {

        internal2GB();

        doGBuffer("both");

        doReason(nar);
    }

    public void internal2GB() {
        if (internalExperienceBuffer.mem == null){
            internalExperienceBuffer.mem = this;
        }
        //2. Internal experience buffer to global buffer
        Task t_internal = internalExperienceBuffer.takeOut();
        //might have more than 1 item to take out
        if(t_internal != null) {
            globalBuffer.putIn(t_internal);
            internalExperienceBuffer.putBack(t_internal, narParameters.INTERNAL_BUFFER_FORGET_DURATIONS, this);
        }
    }

    public void doGBuffer(String target) {
        //3. process a task of global buffer
//        this.processGlobalBufferTask(AgentStarter.nar.narParameters, nar);

        ProcessGBufferTask processGBufferTask = new ProcessGBufferTask(pam, this, target);
        taskSpawner.addTask(processGBufferTask);
    }

    public void doReason(Nar nar) {
        //4. apply general inference step
//        InferenceControl.selectConceptForInference(this, AgentStarter.nar.narParameters, nar);

        SelectConceptTask selectConceptTask = new SelectConceptTask(this, nar);
        taskSpawner.addTask(selectConceptTask);
    }

    /**
     *
     * @param task task to be processed
     * @param narParameters parameters for the Reasoner instance
     * @param time indirection to retrieve time
     */
    public void localInference(final Task task, Parameters narParameters, final Timable time, Memory mem) {
        synchronized (localInferenceMutex) { // 本地推理互斥体，多线程问题导致name空值
            final DerivationContext cont = new DerivationContext(this, narParameters, time);
            cont.setCurrentTask(task);
            cont.setCurrentTerm(task.getTerm());
            Concept concept = conceptualize(task.budget, cont.getCurrentTerm());
            cont.setCurrentConcept(concept);
            if (cont.getCurrentConcept() != null) {
                final boolean processed = ProcessTask.processTask(cont.getCurrentConcept(), cont, task, time, mem);
                if (processed) {
                    event.emit(Events.ConceptDirectProcessedTask.class, task);
                }
            }

            if (!task.sentence.isEternal() && !(task.sentence.term instanceof Operation)) {
                //can be triggered by Buffer itself in the future
                globalBuffer.eventInference(task, cont, false);
            }

            //memory.logic.TASK_IMMEDIATE_PROCESS.commit();
            emit(Events.TaskImmediateProcess.class, task, cont);
        }
    }

//    /**
//     * Select a novel task to process
//     *
//     * @param narParameters parameters for the Reasoner instance
//     * @param time indirection to retrieve time
//     */
//    public void processGlobalBufferTask(Parameters narParameters, final Timable time) {
////        synchronized (tasksMutex) {
//            // globalbuffer 死锁
//            final Task task = globalBuffer.takeOut();
//            if (task != null) {
//                if(!task.processed) {
//                    task.processed = true;
//                    localInference(task, narParameters, time, this);
//                }
//                globalBuffer.putBack(task, narParameters.GLOBAL_BUFFER_FORGET_DURATIONS, this);
//            }
//
////        TaskProcessTask taskProcessTask = new TaskProcessTask(task, AgentStarter.pam, this, time);
////        taskSpawner.addTask(taskProcessTask);
//
////        }
//    }

     public Operator getOperator(final String op) {
        return operators.get(op);
     }
     
     public Operator addOperator(final Operator op) {
         operators.put(op.name(), op);
         return op;
     }
     
     public Operator removeOperator(final Operator op) {
         return operators.remove(op.name());
     }

	//新时间戳系列
    private long currentStampSerial = 0;
    public BaseEntry newStampSerial() {
        return new BaseEntry(this.narId, currentStampSerial++);
    }   

    /** converts durations to cycles */
	//将持续时间转换为周期
    public final float cycles(final double durations) {
        return narParameters.DURATION * (float) durations;
    }

    @Override
    public Iterator<Concept> iterator() {
        return concepts.iterator();
    }
}
