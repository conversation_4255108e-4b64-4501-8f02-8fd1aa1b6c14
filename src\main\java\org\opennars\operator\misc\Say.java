/*
 * The MIT License
 *
 * Copyright 2019 OpenNARS.
 *
 * Permission is hereby granted, free of charge, to any person obtaining a copy
 * of this software and associated documentation files (the "Software"), to deal
 * in the Software without restriction, including without limitation the rights
 * to use, copy, modify, merge, publish, distribute, sublicense, and/or sell
 * copies of the Software, and to permit persons to whom the Software is
 * furnished to do so, subject to the following conditions:
 *
 * The above copyright notice and this permission notice shall be included in
 * all copies or substantial portions of the Software.
 *
 * THE SOFTWARE IS PROVIDED "AS IS", WITHOUT WARRANTY OF ANY KIND, EXPRESS OR
 * IMPLIED, INCLUDING BUT NOT LIMITED TO THE WARRANTIES OF MERCHANTABILITY,
 * FITNESS FOR A PARTICULAR PURPOSE AND NONINFRINGEMENT. IN NO EVENT SHALL THE
 * AUTHORS OR COPYRIGHT HOLDERS BE LIABLE FOR ANY CLAIM, DAMAGES OR OTHER
 * LIABILITY, WHETHER IN AN ACTION OF CONTRACT, TORT OR OTHERWISE, ARISING FROM,
 * OUT OF OR IN CONNECTION WITH THE SOFTWARE OR THE USE OR OTHER DEALINGS IN
 * THE SOFTWARE.
 */
package org.opennars.operator.misc;

import edu.memphis.ccrg.linars.Memory;
import edu.memphis.ccrg.linars.Term;
import org.opennars.entity.Task;
import org.opennars.interfaces.Timable;
import org.opennars.operator.Operation;
import org.opennars.operator.Operator;

import java.util.List;

import static edu.memphis.ccrg.lida.framework.initialization.AgentStarter.nar;
import static edu.memphis.ccrg.lida.pam.PAMemoryImpl.goalNs;

public class Say extends Operator {
    public Say() {
        super("^say");
    }
    public Say(final String name) {
        super(name);
    }

    @Override
    public List<Task> execute(Operation operation, Term[] args, Memory memory, Timable time) {
        String message = "";
        if (args.length == 0) { //{SELF} car3 message
            return null;
        }
        // todo 实时判断要输出的部分，不是全部要输出，如背景共识，同一时序对不同对象，输出结果不完全一样
        // 判断来自哪里的输出，如时序，底层，内外等，然后对已激活认知做判断，筛选，适用不同输出策略
        // 输出策略也是后天习得，先天无策略时，各种输出试错，形成时序策略经验。直接底层实现？
        // 输入也一样，主动视觉，信息探测策略
        if(args.length > 1) {
            message = args[1].toString()
                .replace("#", " ").replace("(", " ")
                .replace(")", " ").replace(",", " ");
        }else {
            if (args[0].toString().contains("^")){
                if (args[0] instanceof Operation) {
                    Operation op = (Operation)args[0];
                    Operator oper = op.getOperator();
                    List<Task> feedback = oper.execute(op, op.getArguments().term, (Memory) goalNs, nar);
                    if(feedback != null){
                        StringBuilder sb = new StringBuilder();
                        for (Task task : feedback) {
                            sb.append(task.getTerm().toString());
                        }
                        message = sb.toString();
                    }
                }
            }else {
                message = args[0].toString()
                        .replace("#", " ").replace("(", " ")
                        .replace(")", " ").replace(",", " ");
            }
        }

        System.out.println("Say---: " + message);

        return null;
    }
}