/* 
 * The MIT License
 *
 * Copyright 2018 The OpenNARS authors.
 *
 * Permission is hereby granted, free of charge, to any person obtaining a copy
 * of this software and associated documentation files (the "Software"), to deal
 * in the Software without restriction, including without limitation the rights
 * to use, copy, modify, merge, publish, distribute, sublicense, and/or sell
 * copies of the Software, and to permit persons to whom the Software is
 * furnished to do so, subject to the following conditions:
 *
 * The above copyright notice and this permission notice shall be included in
 * all copies or substantial portions of the Software.
 *
 * THE SOFTWARE IS PROVIDED "AS IS", WITHOUT WARRANTY OF ANY KIND, EXPRESS OR
 * IMPLIED, INCLUDING BUT NOT LIMITED TO THE WARRANTIES OF MERCHANTABILITY,
 * FITNESS FOR A PARTICULAR PURPOSE AND NONINFRINGEMENT. IN NO EVENT SHALL THE
 * AUTHORS OR COPYRIGHT HOLDERS BE LIABLE FOR ANY CLAIM, DAMAGES OR OTHER
 * LIABILITY, WHETHER IN AN ACTION OF CONTRACT, TORT OR OTHERWISE, ARISING FROM,
 * OUT OF OR IN CONNECTION WITH THE SOFTWARE OR THE USE OR OTHER DEALINGS IN
 * THE SOFTWARE.
 */
package org.opennars.language;

import edu.memphis.ccrg.linars.Term;
import org.opennars.io.Symbols.NativeOperator;

/**
 * A Statement about a Similarity relation as defined in the NARS-theory
 *
 * <AUTHOR> Wang
 * <AUTHOR> Hammer
 */
public class Similarity extends Statement {
    /**
     * Constructor with partial values, called by make
     * @param arg The component list of the term
     */
    public Similarity(final Term[] arg) {
        super(arg);
        init(arg);
    }

    public Similarity(final Term subj, final Term pred) {
        this(new Term[] { subj, pred} );
    }
    

    /**
     * Clone an object
     * @return A new object, to be casted into a Similarity
     */
    @Override
    public Similarity clone() {
        return new Similarity(term);
    }
    
    @Override public Similarity clone(final Term[] replaced) {
        if(replaced == null) {
            return null;
        }
        if (replaced.length!=2)
            return null;
        return make(replaced[0], replaced[1]);
    }

    /** alternate version of make that allows equivalent subject and predicate
     * to be reduced to the common term.      */
    public static Term makeTerm(final Term subject, final Term predicate) {
        if (subject.equals(predicate))
            return subject;                
        return make(subject, predicate);        
    }    
    
    /**
     * Try to make a new compound from two term. Called by the inference rules.
     * @param subject The first component
     * @param predicate The second component
     * @return A compound generated or null
     */
    public static Similarity make(final Term subject, final Term predicate) {

        if (invalidStatement(subject, predicate)) {
            return null;
        }
        // todo 是否需要排序？有时需要差异化双向关系，如相似度、相反性、相互性、相等性、相同性、相异性、相交性、相离性、
        //  相切性、相包含性、相包含于、相交于、相离于、相切于、相等于、相同于、相反于、相似于、相互
//        if (subject.compareTo(predicate) > 0) {
//            return make(predicate, subject);
//        }
        
        return new Similarity(subject, predicate);
    }

    /**
     * Get the operator of the term.
     * @return the operator of the term
     */
    @Override
    public NativeOperator operator() {
        return NativeOperator.SIMILARITY;
    }

    /**
     * Check if the compound is commutative.
     * @return true for commutative
     */
    @Override
    public boolean isCommutative() {
        return true;
    }
}
