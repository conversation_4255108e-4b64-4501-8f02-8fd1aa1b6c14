/*******************************************************************************
 * Copyright (c) 2009, 2011 The University of Memphis.  All rights reserved. 
 * This program and the accompanying materials are made available 
 * under the terms of the LIDA Software Framework Non-Commercial License v1.0 
 * which accompanies this distribution, and is available at
 * http://ccrg.cs.memphis.edu/assets/papers/2010/LIDA-framework-non-commercial-v1.0.pdf
 *******************************************************************************/
package edu.memphis.ccrg.lida.framework.shared;

import edu.memphis.ccrg.lida.framework.strategies.DecayStrategy;
import edu.memphis.ccrg.lida.framework.strategies.ExciteStrategy;
import edu.memphis.ccrg.lida.framework.tasks.TaskManager;
import edu.memphis.ccrg.lida.pam.PamLink;
import edu.memphis.ccrg.lida.pam.PamLinkImpl;
import edu.memphis.ccrg.lida.pam.PAMemory;
import edu.memphis.ccrg.lida.workspace.WorkspaceImpl;
import org.neo4j.kernel.impl.core.RelationshipEntity;
import org.opennars.io.Parser;
import org.opennars.io.Symbols;
import edu.memphis.ccrg.linars.CompoundTerm;
import org.opennars.language.Statement;
import edu.memphis.ccrg.linars.Term;
import java.text.DecimalFormat;
import java.util.Collections;
import java.util.HashMap;
import java.util.Map;
import java.util.logging.Level;
import java.util.logging.Logger;

import static edu.memphis.ccrg.lida.framework.initialization.AgentStarter.narsese;

/**
 * A {@link Link} that connects a {@link Node} to a {@link Linkable} (Node or Link).
 * 
 * <AUTHOR> J. McCall
 * <AUTHOR> Snaider
 * @see ElementFactory
 */
public class LinkImpl extends CompoundTerm implements Link {
	private static final Logger logger = Logger.getLogger(LinkImpl.class.getCanonicalName());

	/*
	 * Source of this link, always a node.
	 */
	public Node source;
	
	/*
	 * Sink of this link, a Linkable.
	 */
	public Linkable sink;

	public int nowOrder; //当前次序
	public int lastOrder; //上次次序
	public int kOrder; //框架内次序
	
	/*
	 * A custom id dependent on the source's and the sink's ids.
	 */
	private ExtendedId extendedId;
	
	/*
	 * Category of this Link.
	 */
	private LinkCategory category;
	/*
	 * Type of this link in the ElementFactory 
	 */
	private String factoryType;
	
	/**
	 * {@link PamLink} in a {@link PAMemory} that grounds this Link.
	 */
	protected PamLink groundingPamLink;

//	protected NeoLink groundingNeoLink;

	private static final ElementFactory factory = ElementFactory.getInstance();
//	private static final String DEFAULT_IS_DECAY = "noDecay";

	private ExciteStrategy exciteStrategy;
	private DecayStrategy decayStrategy;
	private DecayStrategy incentiveSalienceDecayStrategy;
	private double activation;
	public double removalThreshold0;
	private double incentiveSalience;

	private Map<String, ?> parameters;

	private RelationshipEntity relationshipProxy;
	// 最后来自，单值运行时属性，容易被覆盖，按优先级？
	// 来自不同周期、阶段、层次的动机，以最强最优先为准？
	// 多值运行时属性，提高复杂度。与点边本身同衰减=可叠加？
	// 欲求等传递期间，被加强或减弱，已传递则同增减，未传递按强弱过阈值与否
	private String lastFrom;

	private Map<String, Object> properties = new HashMap<>();

	private int id;

	public LinkImpl(Term[] arg) {
		super(arg);
		initactive();
		init(arg);
	}

	private void initactive() {
		activation = DEFAULT_ACTIVATION;
		removalThreshold0 = DEFAULT_ACTIVATIBLE_REMOVAL_THRESHOLD;
		incentiveSalience = DEFAULT_INCENTIVE_SALIENCE;
		decayStrategy = factory.getDefaultDecayStrategy();
		exciteStrategy = factory.getDefaultExciteStrategy();
		incentiveSalienceDecayStrategy = factory.getDefaultDecayStrategy();
	}

	@Override
	public void init(Map<String, ?> params) {
		parameters = params;
		init();
	}

	/**
	 * Default constructor	将继承的activationimpl方法移植过来，不用super了
	 */
	public LinkImpl() {
		initactive();
	}
	
	/**
	 * Constructs a new {@link Link} with specified parameters.
	 * @deprecated Use {@link ElementFactory getLink(Node, Linkable, LinkCategory)} instead.
	 * @param src source {@link Node}
	 * @param snk sink {@link Linkable}
	 * @param cat link's {@link LinkCategory}
	 */
	@Deprecated
	public LinkImpl(Node src, Linkable snk, LinkCategory cat) {
		super();
		if (src == null) {
			throw new IllegalArgumentException(
					"Cannot create a link with null source.");
		} else if (snk == null) {
			throw new IllegalArgumentException(
					"Cannot create a link with null sink.");
		} else if (cat == null) {
			throw new IllegalArgumentException(
					"Cannot create a link with null category.");
		} else if (src.equals(snk)) {
			throw new IllegalArgumentException(
					"Cannot create a link with the same source and sink.");
		} else if (snk.getExtendedId().isComplexLink()) {
			throw new IllegalArgumentException(
					"Sink cannot be a complex link. Must be a node or simple link.");
		} else {
			this.source = src;
			this.sink = snk;
			this.category = cat;
			updateExtendedId();
		}
	}

	/**
	 * Copy constructor
	 * @deprecated Use {@link ElementFactory getLink(String, Node, Linkable, LinkCategory, String, String, double, double)} instead.
	 * @param l source {@link LinkImpl}
	 */
	@Deprecated
	public LinkImpl(LinkImpl l) {
		super();
		if (l == null) {
			logger.log(Level.WARNING, "Cannot construct a Link from null.",
					TaskManager.getCurrentTick());
		} else {
			sink = l.getSink();
			source = l.getSource();
			category = l.getCategory();
			groundingPamLink = l.getGroundingPamLink();
			updateExtendedId();
		}
	}
	
	/*
	 * Refreshes this Link's ExtendedId based on its category, source, and sink.
	 */
	private void updateExtendedId() {
		if (category != null && source != null && sink != null) {
			if (logger.isLoggable(Level.FINEST)) {
				logger.log(Level.FINEST, "ExtendedID updated", TaskManager
						.getCurrentTick());
			}
			extendedId = new ExtendedId(source.getNodeId(), sink.getExtendedId(),
					category.getNodeId());
		}
	}

	@Override
	public String getTNname() {
		return category.getName();
	}

	@Override
	public ExtendedId getExtendedId() {
		if (extendedId == null) {
			updateExtendedId();
		}
		return extendedId;
	}

	@Override
	public Linkable getSink() {
		return sink;
	}

	@Override
	public Node getSource() {
		return source;
	}

	@Override
	public LinkCategory getCategory() {
		return category;
	}

	@Override
	public synchronized void setSink(Linkable snk) {	// todo 兼容子图，头尾节点为复合词项
//		if (snk == null) {
//			logger.log(Level.WARNING, "Cannot set sink to null", TaskManager
//					.getCurrentTick());
//		} else if (source != null && snk.equals(source)) {
//			logger.log(Level.WARNING,
//					"Cannot set sink to same Linkable as source", TaskManager
//							.getCurrentTick());
//		} else if (snk.getExtendedId().isComplexLink()) {
//			logger.log(Level.WARNING, "Cannot set sink to be a complex link.",
//					TaskManager.getCurrentTick());
//		} else {
			this.sink = snk;
//			term[2] = new Term(snk.getName());
			term[2] = (Term) snk;
			updateExtendedId();
//		}
	}

	@Override
	public synchronized void setSource(Node src) {
		if (src == null) {
			logger.log(Level.WARNING, "Cannot set source to null", TaskManager
					.getCurrentTick());
		} else if (sink != null && src.equals(sink)) {
			logger.log(Level.WARNING,
							"Cannot set link's source to the same Linkable as its sink",
							TaskManager.getCurrentTick());
		} else {
			source = src;
//			term[0] = new Term(src.getName());
			term[0] = (Term) source;
			updateExtendedId();
		}
	}

	@Override
	public synchronized void setCategory(LinkCategory c) {
		if (c == null) {
			logger.log(Level.WARNING, "Cannot set a Link's category to null",
					TaskManager.getCurrentTick());
		} else {
			category = c;
//			term[1] = new Term(category.getName());
			term[1] = (Term) category;
			updateExtendedId();
		}
	}

	@Override
	public PamLink getGroundingPamLink() {
		return groundingPamLink;
	}

	@Override
	public synchronized void setGroundingPamLink(PamLink l) {
		groundingPamLink = l;
	}
	
	/**
	 * This method compares this LinkImpl with any kind of Link.
	 * Two Links are equal if and only if they have the same id.
	 */
//	@Override
//	public boolean equals(Object obj) {
//		if (obj instanceof Link) {
//			Link other = (Link) obj;
//			return extendedId.equals(other.getExtendedId());
//		}
//		return false;
//	}
	
//	@Override
//	public int hashCode() {
//		if (extendedId == null) {
//			return 0;
//		}else {
//			return extendedId.hashCode();
//		}
//	}
	
	
//	@Override
	public String getLabel() {
//		return category.getName();
		return (String) super.name();
	}
	
//	@Override
	public String toString() {
		DecimalFormat df = new DecimalFormat("#.00");
		return source.toString() + "-" + category.getName()
//				+ " [" + category.getNodeId() + "] "
//				+ df.format(getActivation())
				+ "-" + sink.toString();
	}
	
	@Override
	public boolean isSimpleLink(){
		return extendedId.isSimpleLink();
	}

	/**
	 * This default implementation of {@link Link} has all of its attributes
	 * updated by {@link NodeStructureImpl} when links are updated. Therefore
	 * this class does not have to implement this method. Any subclass with
	 * specific class members (e.g. PamLinkImpl) should however override this
	 * method.
	 * 
	 * @see PamLinkImpl#updateLinkValues(Link)
	 * @see NodeStructureImpl#addLink(Link, String)
	 * @see NodeStructureImpl#getNewLink(Link, String, Node, Linkable, LinkCategory)
	 */
	@Override
	public void updateLinkValues(Link l) {
		if(l instanceof LinkImpl){
			setIncentiveSalience(l.getTotalIncentiveSalience());//TODO move to ElementFactory methods
		}
	}

	@Override
	public int getId() {
		return id;
	}

	@Override
	public void setId(int id) {
		this.id = id;
	}

//	@Override
//	public void setLinkProxy(RelationshipEntity LinkProxy) {
//		this.relationshipProxy = LinkProxy;
//	}
//
//	@Override
//	public RelationshipEntity getLinkProxy() {
//		return relationshipProxy;
//	}

	@Override
	public String getFactoryType() {
		return factoryType;
	}

	@Override
	public synchronized void setFactoryType(String t) {
		factoryType = t;
	}

	@Override
	public <T> T getParam(String name, T defaultValue) {
		if (defaultValue == null) {
			logger.log(Level.SEVERE,
					"Call to getParam(name,defaultValue) with null defaultValue for {1}.",
					new Object[]{TaskManager.getCurrentTick(),this});
			throw new IllegalArgumentException(
					"Second argument 'defaultValue' cannot be null");
		}

		T value = null;
		if (parameters != null) {
			if(parameters.containsKey(name)){
				Object paramValue = parameters.get(name);
				if(paramValue == null){
					logger.log(Level.WARNING, "Parameter with name {1} has value null\nUsing default parameter value",
							new Object[]{TaskManager.getCurrentTick(), name});
				}else{
					Class<?> classType = defaultValue.getClass();
					if(classType.isInstance(paramValue)){
						value=(T)paramValue;
					}else{
						logger.log(Level.WARNING,
								"Parameter with name {1} has type {2} but type {3} was expected. Returning default value",
								new Object[]{TaskManager.getCurrentTick(),name,
										paramValue.getClass().getCanonicalName(),
										classType});
					}
				}
			}else{
				logger.log(Level.WARNING, "Cannot find parameter with name: \"{1}\" for Initializable: \"{2}\". " +
								"\nUsing default parameter value. If this is an error check the parameter name in the configuration files",
						new Object[]{TaskManager.getCurrentTick(),name, toString()});
			}
		}else{
			logger.log(Level.WARNING, "Parameters for {1} have not been initialized",
					new Object[]{TaskManager.getCurrentTick(), toString()});
		}
		if (value == null) {
			value = defaultValue;
		}
		return value;
	}

	@Override
	public boolean containsParameter(String key){
		return parameters.containsKey(key);
	}

	@Override
	public Map<String, ?> getParameters() {
		return (parameters!=null)? Collections.unmodifiableMap(parameters):null;
	}

	@Override
	public Symbols.NativeOperator operator() {
		return Symbols.NativeOperator.INHERITANCE_neo;
	}

	@Override
	public Statement clone() {
		return null;
	}

	@Override
	public Term clone(Term[] replaced) {
		return null;
	}

	@Override
	public void init(){
		//TODO think about how I want to implement this. This is one option, another is adding to FactoriesDataXmlLoader
//		String name = getParam("activatible.incentiveSalienceDecayStrategy",DEFAULT_IS_DECAY);
//		incentiveSalienceDecayStrategy = factory.getDecayStrategy(name);
	}

	@Override
	public void decay(long ticks) {
		if (decayStrategy != null) {
			if (logger.isLoggable(Level.FINEST)) {
				logger.log(Level.FINEST,
						"Before decaying {1} has current activation: {2}",
						new Object[] { TaskManager.getCurrentTick(), this,
								getActivation() });
			}
			synchronized (this) {
				activation = decayStrategy.decay(getActivation(), ticks);
				incentiveSalience=incentiveSalienceDecayStrategy.decay(getIncentiveSalience(), ticks);
			}
			if (logger.isLoggable(Level.FINEST)) {
				logger.log(Level.FINEST,
						"After decaying {1} has current activation: {2}",
						new Object[] { TaskManager.getCurrentTick(), this,
								getActivation() });
			}
		}
	}

	@Deprecated
	@Override
	public void excite(double amount) {
		exciteActivation(amount);
	}

	@Override
	public void exciteActivation(double amount) {
		if (exciteStrategy != null) {
			if (logger.isLoggable(Level.FINEST)) {
				logger.log(Level.FINEST,
						"Before excitation {1} has current activation: {2}",
						new Object[] { TaskManager.getCurrentTick(), this,
								getActivation() });
			}
			synchronized (this) {
				activation = exciteStrategy.excite(getActivation(), amount);
			}
			if (logger.isLoggable(Level.FINEST)) {
				logger.log(Level.FINEST,
						"After excitation {1} has current activation: {2}",
						new Object[] { TaskManager.getCurrentTick(), this,
								getActivation() });
			}
		}
	}

	@Override
	public void exciteIncentiveSalience(double amount) {
		if (exciteStrategy != null) {
			if (logger.isLoggable(Level.FINEST)) {
				logger.log(Level.FINEST,
						"Before excitation {1} has current incentive salience: {2}",
						new Object[] { TaskManager.getCurrentTick(), this,
								getIncentiveSalience() });
			}
			synchronized (this) {
				incentiveSalience = exciteStrategy.excite(getIncentiveSalience(), amount);
			}
			if (logger.isLoggable(Level.FINEST)) {
				logger.log(Level.FINEST,
						"After excitation {1} has current incentive salience: {2}",
						new Object[] { TaskManager.getCurrentTick(), this,
								getIncentiveSalience()});
			}
		}
	}

	@Override
	public void setActivation(double a) {
		if(a > 1.0){
			synchronized (this) {
				activation = 1.0;
			}
		}else if (a < -1.0) {
			synchronized (this) {
				activation = -1.0;
			}
		}else{
			synchronized (this) {
				activation = a;
			}
		}
	}
	@Override
	public double getActivation() {
		return activation;
	}
	@Override
	public double getTotalActivation() {
		return getActivation();
	}
	@Override
	public double getActivatibleRemovalThreshold() {
		return removalThreshold0;
	}

	@Override
	public DecayStrategy getDecayStrategy() {
		return decayStrategy;
	}

	@Override
	public ExciteStrategy getExciteStrategy() {
		return exciteStrategy;
	}

	@Override
	public double getIncentiveSalience() {
		return incentiveSalience;
	}
	@Override
	public synchronized void setIncentiveSalience(double s) {
		if(s > 1.0){
			synchronized (this) {
				incentiveSalience = 1.0;
			}
		}else if (s < -1.0) {
			synchronized (this) {
				incentiveSalience = -1.0;
			}
		}else{
			synchronized (this) {
				incentiveSalience = s;
			}
		}
	}

	@Override
	public double getTotalIncentiveSalience() {
		return getIncentiveSalience();
	}

	@Override
	public void setActivatibleRemovalThreshold(double t) {
		removalThreshold0 = t;
	}

	@Override
	public void setDecayStrategy(DecayStrategy s) {
		decayStrategy = s;
	}

	@Override
	public void setExciteStrategy(ExciteStrategy s) {
		exciteStrategy = s;
	}

	@Override
	public boolean isRemovable() {
		return getActivation() <= removalThreshold0 &&
				Math.abs(getIncentiveSalience()) <= removalThreshold0;
	}

	@Override
	public void setIncentiveSalienceDecayStrategy(DecayStrategy s) {
		incentiveSalienceDecayStrategy=s;
	}

	@Override
	public DecayStrategy getIncentiveSalienceDecayStrategy() {
		return incentiveSalienceDecayStrategy;
	}

	@Override
	public void setProperties(Map<String, Object> properties) {
		this.properties = properties;
	}

	@Override
	public Map<String, Object> getProperties() {
		return this.properties;
	}

	@Override
	public Object getProperty(String key) {
		for (String k: properties.keySet()) {
			if (k.equals(key)) {
				return properties.get(k);
			}
		}
		return null;
	}
	@Override
	public Term toTerm(){
		Term tt = new Term();
		tt = WorkspaceImpl.getLinkTerm(this);
		if (term != null) {
			try {
				// todo 不能直接字符串转化，细分部分如pam点边需要保留原属性
				tt = narsese.parseTerm(tt.toString());
			} catch (Parser.InvalidInputException e) {
				throw new RuntimeException(e);
			}
		}
		return tt;
	}
}