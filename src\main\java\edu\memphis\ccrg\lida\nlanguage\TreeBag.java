/* 
 * The MIT License
 *
 * Copyright 2018 The OpenNARS authors.
 *
 * Permission is hereby granted, free of charge, to any person obtaining a copy
 * of this software and associated documentation files (the "Software"), to deal
 * in the Software without restriction, including without limitation the rights
 * to use, copy, modify, merge, publish, distribute, sublicense, and/or sell
 * copies of the Software, and to permit persons to whom the Software is
 * furnished to do so, subject to the following conditions:
 *
 * The above copyright notice and this permission notice shall be included in
 * all copies or substantial portions of the Software.
 *
 * THE SOFTWARE IS PROVIDED "AS IS", WITHOUT WARRANTY OF ANY KIND, EXPRESS OR
 * IMPLIED, INCLUDING BUT NOT LIMITED TO THE WARRANTIES OF MERCHANTABILITY,
 * FITNESS FOR A PARTICULAR PURPOSE AND NONINFRINGEMENT. IN NO EVENT SHALL THE
 * AUTHORS OR COPYRIGHT HOLDERS BE LIABLE FOR ANY CLAIM, DAMAGES OR OTHER
 * LIABILITY, WHETHER IN AN ACTION OF CONTRACT, TORT OR OTHERWISE, ARISING FROM,
 * OUT OF OR IN CONNECTION WITH THE SOFTWARE OR THE USE OR OTHER DEALINGS IN
 * THE SOFTWARE.
 */
package edu.memphis.ccrg.lida.nlanguage;

import org.opennars.inference.BudgetFunctions;
import org.opennars.main.Parameters;
import org.opennars.storage.Distributor;
import edu.memphis.ccrg.linars.Memory;

import java.io.Serializable;
import java.util.*;

/**
 * Original Bag implementation which distributes items into
 * discrete levels (queues) according to priority
 *  Original Bag实现，可根据优先级将项目分配为*离散级别（队列）
 */
public class TreeBag implements Serializable, Iterable<TreeChart> {

       /** priority levels 优先等级*/
    private final int TOTAL_LEVEL;
    /** firing threshold 开火门槛*/
    private final int THRESHOLD;
    /** shared DISTRIBUTOR that produce the probability distribution 产生概率分布的共享DISTRIBUTO*/
    private final Distributor DISTRIBUTOR;
    /** mapping from key to item 从键到项的映射*/
    public HashMap<String, TreeChart> nameTable;
    /** array of lists of items, for items on different level 项目列表数组，用于不同级别的项目*/
    public ArrayList<ArrayList<TreeChart>> itemTable;

    // 已完整匹配的构式
    public HashMap<String, TreeChart> completeTerms;

    /** defined in different bags 在不同的袋子里定义*/
    private final int capacity;
    /** current sum of occupied level 当前占用水平的总和*/
    private int mass;
    /** index to get next level, kept in individual objects 获取下一级的索引，保存在单个对象中*/
    private int levelIndex;
    /** current take out level */
    private int currentLevel;
    /** maximum number of items to be taken out at current level 当前级别要取出的最大项目数*/
    private int currentCounter;
    //包
    public TreeBag(final int levels, final int capacity, Parameters narParameters) {
        this(levels, capacity, (int) (narParameters.BAG_THRESHOLD * levels));
    }

    /** thresholdLevel = 0 disables "fire level completely" threshold effect */
	//阈值水平=0 禁用“火力全开”阈值效果
	public TreeBag(final int levels, final int capacity, final int thresholdLevel) {
        this.TOTAL_LEVEL = levels;
        DISTRIBUTOR = new Distributor(TOTAL_LEVEL); 
        this.THRESHOLD = thresholdLevel;
        this.capacity = capacity;
        clear();
    }
    
	//清理包
     public void clear() {
        itemTable = new ArrayList<ArrayList<TreeChart>>(TOTAL_LEVEL);
        for (int i = 0; i < TOTAL_LEVEL; i++) {
            itemTable.add(new ArrayList<TreeChart>());
        }
        nameTable = new LinkedHashMap<String, TreeChart>();

        completeTerms = new LinkedHashMap<String, TreeChart>();

        currentLevel = TOTAL_LEVEL - 1;
        // so that different bags start at different point //这样不同的包就从不同的点开始
        levelIndex = capacity % TOTAL_LEVEL;
        mass = 0;
        currentCounter = 0;
    }

    /**
     * Get the average priority of Items
     * @return The average priority of Items in the bag
     */
	//获取条目的平均优先级
    public float getAveragePriority() {
        if (nameTable.isEmpty()) {
            return 0.01f;
        }
        float f = (float) mass / (nameTable.size() * TOTAL_LEVEL);
        if (f > 1) {
            return 1.0f;
        }
        return f;
    }

    /**
     * Check if an item is in the bag
     * @param it An item
     * @return Whether the Item is in the Bag
     */
	//检查袋子中是否有项目
    public boolean contains(TreeChart it) {
        return nameTable.containsValue(it);
    }

    /**
     * Get an Item by key
     * @param key The key of the Item
     * @return The Item with the given key
     */
	//由键获取项目
    public TreeChart get(String key) {
        return nameTable.get(key);
    }

    /**
     * Add a new Item into the Bag
     * @param newItem The new Item
     * @return Whether the new Item is added into the Bag
     */
	//添加新项目.1获取项目、词项链接、任务、任务链接、概念的键.若老项目尚在，与新项目合并后剔除，并清除溢出。
    public TreeChart putIn(TreeChart newItem) {
        String newKey = (String) newItem.name();
        TreeChart oldItem = nameTable.put(newKey, newItem);
        if (oldItem != null) {
            outOfBase(oldItem);
            newItem.merge(oldItem); // merge duplications
        }
        // put the (new or merged) item into itemTable //将（新的或合并的）项目放入itemTable
        TreeChart overflowItem = intoBase(newItem);
        if (overflowItem != null) {             // remove overflow
            String overflowKey = (String) overflowItem.name();
            nameTable.remove(overflowKey);
            return overflowItem;
        } else {
            return null;
        }
    }
    
    /**
     * Put an item back into the itemTable
     * <p>
     * The only place where the forgetting rate is applied
     *
     * @param oldItem The Item to put back
     * @param m related memory
     * @return the item which was removed, or null if none removed
     */    
	//将一个项目放回项目表中。唯一适用遗忘率的地方。衰减？
    public TreeChart putBack(final TreeChart oldItem, final float forgetCycles, final Memory m) {
        final float relativeThreshold = m.narParameters.FORGET_QUALITY_RELATIVE;
        BudgetFunctions.applyForgetting(oldItem.budget, forgetCycles, relativeThreshold);
        return putIn(oldItem);
    }

    /**
     * Choose an Item according to priority distribution and take it out of the Bag
     * @return The selected Item
     */
	//根据优先级分配选择项目并将其从包中取出
    public TreeChart takeOut() {
        if (nameTable.isEmpty()) { // empty bag
            return null;
        }
        if (emptyLevel(currentLevel) || (currentCounter == 0)) { // done with the current level
            currentLevel = DISTRIBUTOR.pick(levelIndex);
            levelIndex = DISTRIBUTOR.next(levelIndex);
            int num = 0;
            while (emptyLevel(currentLevel) && num <= 1000) {          // look for a non-empty level
                currentLevel = DISTRIBUTOR.pick(levelIndex);
                levelIndex = DISTRIBUTOR.next(levelIndex);
                num++;
            }
            if (currentLevel < THRESHOLD) { // for dormant levels, take one item
                currentCounter = 1;
            } else {                  // for active levels, take all current items
                currentCounter = itemTable.get(currentLevel).size();
            }
        }
        TreeChart selected = takeOutFirst(currentLevel); // take out the first item in the level
        if (selected == null) {
            return null;
        }
        int belongingLevel = getLevel(selected);
        if(currentLevel != belongingLevel) {
            intoBase(selected);
            return takeOut();
        }
        currentCounter--;
        nameTable.remove(selected.name());
        return selected;
    }

    /**
     * Pick an item by key, then remove it from the bag
     * @param key The given key
     * @return The Item with the key
     */
	//从包中拿出一个项目
    public TreeChart pickOut(String key) {
        TreeChart picked = nameTable.get(key);
        if (picked != null) {
            outOfBase(picked);
            nameTable.remove(key);
        }
        return picked;
    }
    public TreeChart pickOut(TreeChart val) {
        return pickOut((String) val.name());
    }

    /**
     * Check whether a level is empty
     * @param n The level index
     * @return Whether that level is empty
     */
	//看级别是否为空
    protected boolean emptyLevel(int n) {
        if (itemTable.size() == n){
            return true;
        }
        return itemTable.get(n).isEmpty();
    }

    /**
     * Decide the put-in level according to priority
     * @param item The Item to put in
     * @return The put-in level
     */
	//根据优先级确定插入级别
    private int getLevel(TreeChart item) {
        float fl = item.getPriority() * TOTAL_LEVEL;
        int level = (int) Math.ceil(fl) - 1;
        if (level < 0){
            return 0;
        }
        return (level < 0) ? 0 : level;     // cannot be -1
    }

    /**
     * Insert an item into the itemTable, and return the overflow
     * @param newItem The Item to put in
     * @return The overflow Item
     */
	//根据优先级确定插入级别将项目插入项目表，然后将优先级低的老词项溢出
    private TreeChart intoBase(TreeChart newItem) {
        TreeChart oldItem = null;
        int inLevel = getLevel(newItem);
        if (nameTable.size() > capacity) {      // the bag is full
            int outLevel = 0;
            while (emptyLevel(outLevel) && outLevel < itemTable.size()) {
                outLevel++;
            }
            if (outLevel > inLevel) {           // ignore the item and exit
                return newItem;
            } else {                            // remove an old item in the lowest non-empty level
                oldItem = takeOutFirst(outLevel);
            }
        }
        if (inLevel < 0){
            inLevel = 0;
        }
        itemTable.get(inLevel).add(newItem);        // FIFO
        mass += (inLevel + 1);                  // increase total mass
        return oldItem;		// TODO return null is a bad smell
    }

    /**
     * Take out the first or last Type in a level from the itemTable
     * @param level The current level
     * @return The first Item
     */
	//从项目表中取出级别中的第一个或最后一个Type
    private TreeChart takeOutFirst(int level) {
        List items = itemTable.get(level);
        if(items.size() <= 0 ){
            itemTable.set(level,new ArrayList<>());
//            if(itemTable.size() > 0 && itemTable.size() > level){
//                items = itemTable.get(level);
//            }else if(itemTable.size() > 0 && itemTable.size() == level){
//                items = itemTable.get(level - 1);
//            }else {
                return null;
//            }
        }
        // 多线程惹祸？
        TreeChart selected = (TreeChart) items.get(0);
        if (itemTable.size() == 0 && level == 0 || level == -1){
            return null;
        }
        itemTable.get(level).remove(0);
        mass -= (level + 1);
        return selected;
    }

    /**
     * Remove an item from itemTable, then adjust mass
     * @param oldItem The Item to be removed
     */
	//从项目表中删除一个项目，然后调整质量
    protected void outOfBase(TreeChart oldItem) {
        int level = getLevel(oldItem);
        itemTable.get(level).remove(oldItem);
        mass -= (level + 1);
    }

    /**
     * Collect Bag content into a String for display
     */
	//将包中内容字符串化显示
    @Override
    public java.lang.String toString() {
//        StringBuffer buf = new StringBuffer(" ");
//	    for (int i = TOTAL_LEVEL; i >= 0 ; i--) {
//            if (!emptyLevel(i - 1)) {
//                buf = buf.append("\n --- Level " + i + ":\n ");
//                for (int j = 0; j < itemTable.get(i - 1).size(); j++) {
//                    buf = buf.append(itemTable.get(i - 1).get(j).toString() + "\n ");
//                }
//            }
//        }
//        return buf.toString();
        return "nametable_[" + nameTable.size() + "], itemtable_[" + itemTable.size() + "], currentlevel_[" + currentLevel + "]";
    }

    //字符串化
    /** TODO bad paste from preceding */
    public java.lang.String toStringLong() {
        StringBuffer buf = new StringBuffer(" BAG " + getClass().getSimpleName() );
        buf.append(" ").append( showSizes() );
		for (int i = TOTAL_LEVEL; i >= 0; i--) {
            if (!emptyLevel(i - 1)) {
                buf = buf.append("\n --- LEVEL " + i + ":\n ");
                for (int j = 0; j < itemTable.get(i - 1).size(); j++) {
                    buf = buf.append(itemTable.get(i - 1).get(j).toString() + "\n ");
                }
            }
        }
		buf.append(">>>> end of Bag").append( getClass().getSimpleName() );
        return buf.toString();
    }
    
	//显示尺寸
    java.lang.String showSizes() {
        StringBuilder buf = new StringBuilder(" ");
    	int levels = 0;
    	for ( ArrayList<TreeChart> items : itemTable) {
            if ((items != null) && ! items.isEmpty()) {
				levels++;
				buf.append( items.size() ).append( " " );
            }
		}
    	return "Levels: " + Integer.toString( levels ) + ", sizes: " + buf;
    }
    
    public int size() { 
        return nameTable.size();
    }

	//迭代器
    @Override
    public Iterator<TreeChart> iterator() {
        return nameTable.values().iterator();
    }
}
