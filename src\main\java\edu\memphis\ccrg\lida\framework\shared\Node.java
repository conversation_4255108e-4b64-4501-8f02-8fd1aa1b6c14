/*******************************************************************************
 * Copyright (c) 2009, 2011 The University of Memphis.  All rights reserved. 
 * This program and the accompanying materials are made available 
 * under the terms of the LIDA Software Framework Non-Commercial License v1.0 
 * which accompanies this distribution, and is available at
 * http://ccrg.cs.memphis.edu/assets/papers/2010/LIDA-framework-non-commercial-v1.0.pdf
 *******************************************************************************/
/**
 * Node.java
 */
package edu.memphis.ccrg.lida.framework.shared;

import edu.memphis.ccrg.lida.pam.PamNode;
import edu.memphis.ccrg.lida.proceduralmemory.Condition;
import edu.memphis.ccrg.linars.Term;
import org.neo4j.graphdb.Transaction;
import org.neo4j.kernel.impl.core.NodeEntity;

import java.util.List;

/**
 * A {@link Node} represents a Concept in LIDA. It could be implemented in
 * different ways for different parts of the system. For example could be
 * pamNodes in the PAM and WorkspaceNodes in the workspace. Nodes with the same
 * id represents the same concept so equals have to return true even if the
 * objects are of different classes.
 * 节点}代表LIDA中的概念。它可以针对系统的不同部分以不同的方式实现。
 * 例如，可以是PAM中的* pamNodes和工作区中的WorkspaceNodes。
 * 具有相同* id的节点表示相同的概念，因此即使*对象属于不同的类，equals也必须返回true
 * <AUTHOR> Snaider
 * <AUTHOR> J. McCall
 * 
 */
public interface Node extends Linkable, Condition {
	
	/**
	 * Returns the grounding PamNode.
	 * @return PamNode, in PAM, underlying this node.
	 */
	PamNode getGroundingPamNode();

    /**
     * Used by factory to set the underlying PamNode for this node
     * @param n PamNode
     */
	void setGroundingPamNode(PamNode n);

    /**
     * Returns Node's id
     * @return unique id
     */
	int getNodeId();
    
    /**
     * Sets Node's id
     * @param id unique id
     */
	void setNodeId(int id);

    
    @Override
	String getTNname();
    
    /**
     * Sets label
     * @param label readable label
     */
	void setLabel(String label);

	void setNodeName(String name);
	
	/** 
	 * Subclasses of Node should override this method to set all of their type-specific member data
	 * using the values of the specified Link.  
	 * Thus specified Node must be of the same subclass type.
	 * 
	 * @param n Node whose values are used to update with.
	 */
	void updateNodeValues(Node n);

	void setLocation(String location);
	String getLocation();

	void setBcastid(String bcastid);
	String getBcastid();

//	void setNodeProxy(NodeEntity nodeProxy);
//	NodeEntity getNodeProxy();

	NodeEntity getNodeProxy(Transaction tx, String p);

	void setTruth(int truth);
	int getTruth();

	String getLastAct();
	void setLastAct(String lastAct);

	int getFromsceneid();
	void setFromsceneid(int fromsceneid);

	int getFromnodeid();
	void setFromnodeid(int fromnodeid);

	String getFromLinkType();
	void setFromLinkType(String fromLinkType);

    int getDoneNum();

	void setDoneNum(int doneNum);

	public List<String> getLabels();

	public void setLabels(List<String> labels);

	Term toTerm();

	String getStrs();

//	void setProperties(Map<String, Object> properties);
//	Map<String, Object> getProperties();
}

