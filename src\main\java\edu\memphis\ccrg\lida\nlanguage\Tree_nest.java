package edu.memphis.ccrg.lida.nlanguage;

import java.util.ArrayList;
import java.util.List;
import java.util.Optional;

public class Tree_nest {
    public TreeNode root;// 根节点
    public int layer; // 层数
    public int allNodeSize;// 节点数量

    public Tree_nest() {
        this.root = null; // 初始时树为空
    }
    public Tree_nest(TreeNode treeNode) {
        this.root = treeNode;
        this.allNodeSize = 1;
    }
    public void addNode(Object value, TreeNode parent) {
        TreeNode newNode = new TreeNode(value);
        if (parent == null) {
            if (root == null) {
                root = newNode;
//            } else {
//                throw new IllegalArgumentException("Cannot add node as root is already set.");
            }
        } else {
            boolean isAdd = parent.addChild(newNode);
            if (isAdd) allNodeSize++;
        }
    }
    public void removeNode(Object value) {
        if (root == null) {
            return; // 树为空，无需删除
        }
        // 递归查找并删除节点
        removeNodeRecursive(root, value);
    }

    private boolean removeNodeRecursive(TreeNode node, Object value) {
        Optional<TreeNode> child = node.findChildByValue(value);
        if (child.isPresent()) {
            node.removeChild(child.get());
            return true;
        }
        for (TreeNode childNode : node.children) {
            if (removeNodeRecursive(childNode, value)) {
                return true;
            }
        }
        return false; // 如果没有找到节点，返回false
    }
    public Optional<TreeNode> findNode(Object value) {
        if (root == null) {
            return Optional.empty(); // 树为空，无法找到节点
        }
        return findNodeRecursive(root, value);
    }
    // 递归查找节点，optional包装，与普通方法相比，可以避免空指针异常，与直接返回treenode相比，可以避免重复查找
    private Optional<TreeNode> findNodeRecursive(TreeNode node, Object value){
        // 如果当前节点的值等于目标值
        if (node.value.equals(value)) {
            // 返回当前节点的Optional包装
            return Optional.of(node);
        }
        // 遍历当前节点的所有子节点
        for (TreeNode childNode : node.children) {
            // 递归调用findNodeRecursive方法，查找子节点中是否包含目标值
            Optional<TreeNode> found = findNodeRecursive(childNode, value);
            // 如果找到了目标节点，返回找到的节点的Optional包装
            if (found.isPresent()) {
                return found;
            }
        }
        // 如果没有找到节点，返回empty
        return Optional.empty();
    }

    // 更新节点值（示例方法，需要根据具体需求实现）
    public void updateNodeValue(Object oldValue, Object newValue) {
        Optional<TreeNode> node = findNode(oldValue);
        if (node.isPresent()) {
            node.get().value = newValue;
        } else {
            System.out.println("Node with value " + oldValue + " not found.");
        }
    }

    // 查找从根节点到目标节点的路径
    public Optional<List<TreeNode>> findPathToNode(Object targetValue) {
        List<TreeNode> path = new ArrayList<>();
        return findPathToNodeDFS(root, targetValue, path);
    }

    private Optional<List<TreeNode>> findPathToNodeDFS(TreeNode node, Object targetValue, List<TreeNode> path) {
        if (node == null) {
            return Optional.empty();
        }
        // 将当前节点添加到路径中
        path.add(node);
        // 如果找到目标节点，返回路径
        if (node.value.equals(targetValue)) {
            return Optional.of(path);
        }
        // 递归查找子节点
        for (TreeNode child : node.children) {
            Optional<List<TreeNode>> result = findPathToNodeDFS(child, targetValue, path);
            if (result.isPresent()) {
                return result;
            }
        }
        // 如果当前分支没有找到目标节点，则移除当前节点并回溯
        path.remove(path.size() - 1);
        return Optional.empty();
    }

    // 深度优先遍历树
    public void traverseTreeDFS() {
        List<TreeNode> visitedNodes = new ArrayList<>();
        dfs(root, visitedNodes);
    }

    private void dfs(TreeNode node, List<TreeNode> visitedNodes) {
        if (node == null) {
            return;
        }
        // 访问当前节点
        System.out.println(node.value);
        visitedNodes.add(node);
        // 递归访问子节点
        for (TreeNode child : node.children) {
            dfs(child, visitedNodes);
        }
    }

    // 为指定值的节点添加别称
    public void addAliasToNode(Object value, Object alias) {
        Optional<TreeNode> node = findNode(value);
        if (node.isPresent()) {
            node.get().addAlias(alias);
        } else {
            System.out.println("Node with value " + value + " not found.---- addAliasToNode");
        }
    }

    // 从指定值的节点中移除别称
    public void removeAliasFromNode(Object value, Object alias) {
        Optional<TreeNode> node = findNode(value);
        if (node.isPresent()) {
            node.get().removeAlias(alias);
        } else {
            System.out.println("Node with value " + value + " not found.---- removeAliasFromNode");
        }
    }

    // 检查指定值的节点是否包含某个别称
    public boolean hasAliasForNode(Object value, Object alias) {
        Optional<TreeNode> node = findNode(value);
        return node.map(treeNode -> treeNode.hasAlias(alias)).orElse(false);
    }

    // 获取指定值的节点的所有别称
    public List<Object> getAliasesForNode(Object value) {
        Optional<TreeNode> node = findNode(value);
        if (node.isPresent()) {
            return node.get().getAliases();
        }
        return new ArrayList<>();
    }

    // 为指定值的节点添加信息池数据
    public void addTopicDataToNode(Object value, Object data) {
        Optional<TreeNode> node = findNode(value);
        if (node.isPresent()) {
            node.get().addTopicData(data);
        } else {
            System.out.println("Node with value " + value + " not found.");
        }
    }

    // 从指定值的节点中移除信息池数据
    public void removeTopicDataFromNode(Object value, Object data) {
        Optional<TreeNode> node = findNode(value);
        if (node.isPresent()) {
            node.get().removeTopicData(data);
        } else {
            System.out.println("Node with value " + value + " not found.");
        }
    }

    // 检查指定值的节点是否包含某个信息池数据
    public boolean hasTopicDataForNode(Object value, Object data) {
        Optional<TreeNode> node = findNode(value);
        return node.map(treeNode -> treeNode.hasTopicData(data)).orElse(false);
    }

    // 获取指定值的节点的所有信息池数据
    public List<Object> getTopicDataForNode(Object value) {
        Optional<TreeNode> node = findNode(value);
        if (node.isPresent()) {
            return node.get().getTopicDataPool();
        }
        return new ArrayList<>();
    }
}


/*
class TreeNode0 {
    Object value;
    List<TreeNode> children;
    // 别称、可替代、同义词、命名等。类似相似或对等词项。可多个，父子关系共享
    List<Object> alias;
    // 主题信息池，包括相关的概念、属性、时序等，供动机执行时检索，区分可能混杂的数据，参考程序执行，可能要垃圾回收
    // 人类只有一个共用的工作记忆，可能信息混淆，且容量有限，还要衰减，但计算机可有多个不同的工作记忆，属于优化处理
    List<Object> topicDataPool;

    public TreeNode0(Object value) {
        this.value = value;
        this.children = new ArrayList<>();
        this.alias = new ArrayList<>();
        this.topicDataPool = new ArrayList<>();
    }

    public void addChild(TreeNode child) {
        children.add(child);
    }

    public Optional<TreeNode> findChildByValue(Object value) {
        return children.stream()
                .filter(node -> node.value.equals(value))
                .findFirst();
    }

    public void removeChild(TreeNode child) {
        children.remove(child);
    }

    // 添加别称
    public void addAlias(Object alias) {
        this.alias.add(alias);
    }

    // 移除别称
    public void removeAlias(Object alias) {
        this.alias.remove(alias);
    }

    // 检查是否包含特定的别称
    public boolean hasAlias(Object alias) {
        return this.alias.contains(alias);
    }

    // 获取别称列表
    public List<Object> getAliases() {
        return this.alias;
    }

    // 添加信息池数据
    public void addTopicData(Object data) {
        this.topicDataPool.add(data);
    }

    // 移除信息池数据
    public void removeTopicData(Object data) {
        this.topicDataPool.remove(data);
    }

    // 检查信息池是否包含特定数据
    public boolean hasTopicData(Object data) {
        return this.topicDataPool.contains(data);
    }

    // 获取信息池数据列表
    public List<Object> getTopicDataPool() {
        return this.topicDataPool;
    }

    // 修改节点值
    public void setValue(Object newValue) {
        this.value = newValue;
    }

    // 获取节点值
    public Object getValue() {
        return this.value;
    }
}
*/

