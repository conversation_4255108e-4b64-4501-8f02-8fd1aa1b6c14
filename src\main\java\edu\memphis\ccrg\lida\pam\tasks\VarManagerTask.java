/*******************************************************************************
 * Copyright (c) 2009, 2011 The University of Memphis.  All rights reserved. 
 * This program and the accompanying materials are made available 
 * under the terms of the LIDA Software Framework Non-Commercial License v1.0 
 * which accompanies this distribution, and is available at
 * http://ccrg.cs.memphis.edu/assets/papers/2010/LIDA-framework-non-commercial-v1.0.pdf
 *******************************************************************************/
package edu.memphis.ccrg.lida.pam.tasks;

import edu.memphis.ccrg.lida.framework.FrameworkModule;
import edu.memphis.ccrg.lida.framework.initialization.AgentStarter;
import edu.memphis.ccrg.lida.framework.shared.Link;
import edu.memphis.ccrg.lida.framework.shared.Node;
import edu.memphis.ccrg.lida.framework.shared.NodeStructure;
import edu.memphis.ccrg.lida.framework.tasks.FrameworkTaskImpl;
import edu.memphis.ccrg.lida.framework.tasks.TaskManager;
import edu.memphis.ccrg.lida.pam.PAMemory;

import java.util.logging.Level;
import java.util.logging.Logger;

/**
 */
public class VarManagerTask extends FrameworkTaskImpl {
	private static final Logger logger = Logger.getLogger(VarManagerTask.class.getCanonicalName());
//	private SensoryMemory sm;
	private PAMemory pam;
	private NodeStructure seqNs;
	@Override
	public void setAssociatedModule(FrameworkModule module, String moduleUsage) {
		if(module instanceof PAMemory){	// 用factorydata里的初始化，不是alifeagent里的
			pam = (PAMemory) module;
//		}else if (module instanceof SensoryMemory) {
//			sm = (SensoryMemory) module;
		}else{
			logger.log(Level.WARNING, "Cannot add module {1}",
					new Object[]{TaskManager.getCurrentTick(),module});
		}
	}

	/**
	 */
	@Override
	protected void runThisFrameworkTask() {
		Node isasource;
		Node isasink;
		seqNs = pam.getWorkspaceBuffer("seq").getBufferContent(null);
		Long tick = TaskManager.getCurrentTick() - AgentStarter.doStartick;
//		boolean isDovar = true;
//		if(tick > 20){
//			for(FrameworkTask task:pam.getAssistingTaskSpawner().getTasks()){
//			}
//		}

		if (AgentStarter.isDoVar && tick < 200){
			// 动机维持，目前在时序buffer，todo 放动机管理
			for(Link seqlink:seqNs.getLinks()){
				if(seqlink.getTNname().equals("nowisa")){
					seqlink.setActivation(seqlink.getActivation() + 0.3);
					isasource = seqlink.getSource();
					isasink = (Node) seqlink.getSink();
					isasource.setActivation(isasource.getActivation() + 0.2);
					isasink.setActivation(isasink.getActivation() + 0.2);
				}
			}
		}else {
		    AgentStarter.isDoVar = false;
		}
		// 不取消就是一直执行
//		cancel();
	}
}
