package edu.memphis.ccrg.lida.nlanguage;

import edu.memphis.ccrg.lida.framework.Agent;
import edu.memphis.ccrg.lida.framework.initialization.FullyInitializable;
import edu.memphis.ccrg.lida.framework.initialization.GlobalInitializer;
import edu.memphis.ccrg.lida.framework.initialization.Initializer;
import edu.memphis.ccrg.lida.motivation.workspace.MotivationWorkspace;

import java.util.Map;

/**
 * {@link Initializer} for the {@link MotivationWorkspace}.
 * <AUTHOR>
 *
 */
public class LanGenInitializer implements Initializer {

	@Override
	public void initModule(FullyInitializable m, Agent a, Map<String, ?> params) {
//		Workspace workspace = (Workspace)m;
		String label = (String) params.get("linkCategory");
		Object o = GlobalInitializer.getInstance().getAttribute(label.trim());
	}
}
