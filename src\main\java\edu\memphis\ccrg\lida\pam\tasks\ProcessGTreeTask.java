/*******************************************************************************
 * Copyright (c) 2009, 2011 The University of Memphis.  All rights reserved. 
 * This program and the accompanying materials are made available 
 * under the terms of the LIDA Software Framework Non-Commercial License v1.0 
 * which accompanies this distribution, and is available at
 * http://ccrg.cs.memphis.edu/assets/papers/2010/LIDA-framework-non-commercial-v1.0.pdf
 *******************************************************************************/
package edu.memphis.ccrg.lida.pam.tasks;

import edu.memphis.ccrg.lida.framework.shared.Link;
import edu.memphis.ccrg.lida.framework.tasks.FrameworkTaskImpl;
import edu.memphis.ccrg.lida.pam.PAMemory;
import edu.memphis.ccrg.lida.pam.PamLink;
import edu.memphis.ccrg.linars.Concept;
import edu.memphis.ccrg.linars.Memory;
import edu.memphis.ccrg.linars.ProcessGoal;
import org.opennars.control.DerivationContext;
import org.opennars.entity.Task;
import org.opennars.interfaces.Timable;
import org.opennars.language.Statement;
import org.opennars.main.Parameters;

import java.util.ArrayList;
import java.util.LinkedHashMap;
import java.util.List;

import static edu.memphis.ccrg.lida.framework.initialization.AgentStarter.nar;

/**
 * A task to add a {@link PamLink} and its sink to the percept.
 * 
 * <AUTHOR> J. McCall
 * @see ExcitationTask creates this task
 * @see PropagationTask creates this task
 */
public class ProcessGTreeTask extends FrameworkTaskImpl {
	private PAMemory pam;
	private Link link;
	private Task task;
	private Memory mem;
	private Timable time;
	private String target;

	public ProcessGTreeTask(Task task, Memory mem) {
		super(1, "tact");
//		this.pam = pam;
//		this.link = link;
		this.task = task;
		this.mem = mem;
//		this.time = time;
//		this.target = target;
	}
	/**
	 * Adds link's sink to the percept and tries to add the link as well then finishes.
	 */
	@Override
	protected void runThisFrameworkTask() {
		List<Task> execPreconditions = new ArrayList<>();
		// 顺承时序，加入当前已决定言行
		execPreconditions.add(task);
		DerivationContext nal = new DerivationContext(mem, new Parameters(), nar);
		Concept goalC = mem.concepts.get(((Statement)task.getTerm()).getPredicate().toString()).clone();

		Task task00 = mem.globalBuffer.getByTerm(goalC.term.toString());
		Task goalTask;
		if (task00 == null){
			goalTask = goalC.toTask('!');
		}else {
			goalTask = task00;
		}
		ProcessGoal.calcBestExecutablePrecondition(nal, goalC,
				goalTask.sentence, execPreconditions, new LinkedHashMap<>(), goalTask);

		cancel();
	}
	
}

