/* 
 * The MIT License
 *
 * Copyright 2018 The OpenNARS authors.
 *
 * Permission is hereby granted, free of charge, to any person obtaining a copy
 * of this software and associated documentation files (the "Software"), to deal
 * in the Software without restriction, including without limitation the rights
 * to use, copy, modify, merge, publish, distribute, sublicense, and/or sell
 * copies of the Software, and to permit persons to whom the Software is
 * furnished to do so, subject to the following conditions:
 *
 * The above copyright notice and this permission notice shall be included in
 * all copies or substantial portions of the Software.
 *
 * THE SOFTWARE IS PROVIDED "AS IS", WITHOUT WARRANTY OF ANY KIND, EXPRESS OR
 * IMPLIED, INCLUDING BUT NOT LIMITED TO THE WARRANTIES OF MERCHANTABILITY,
 * FITNESS FOR A PARTICULAR PURPOSE AND NONINFRINGEMENT. IN NO EVENT SHALL THE
 * AUTHORS OR COPYRIGHT HOLDERS BE LIABLE FOR ANY CLAIM, DAMAGES OR OTHER
 * LIABILITY, WHETHER IN AN ACTION OF CONTRACT, TORT OR OTHERWISE, ARISING FROM,
 * OUT OF OR IN CONNECTION WITH THE SOFTWARE OR THE USE OR OTHER DEALINGS IN
 * THE SOFTWARE.
 */
package org.opennars.language;

import edu.memphis.ccrg.lida.nlanguage.TreeChart;
import edu.memphis.ccrg.linars.CompoundTerm;
import edu.memphis.ccrg.linars.Term;
import org.opennars.io.Symbols.NativeOperator;

import java.util.Collection;
import java.util.List;

/**
 * A Product is a sequence of 1 or more terms as defined in the NARS-theory
 * 一个Product是NARS理论中定义的1个或多个术语的序列
 * <AUTHOR> Wang
 * <AUTHOR> Hammer
 */
public class Product extends TreeChart {
    /**
     * Constructor with partial values, called by make
     * @param arg The component list of the term
     */
    public Product(final Term... arg) {
        super(arg);
        init(arg);
    }

    public Product(Collection<Term> findingList, final Term... arg) {
        // 无关系元素集合，次序关系在元素属性里，可能有子节点
        // 至少两个参数，元素和匹配了多少，其他可以通过这两个参数推导出来，nars原生暂为null
        super(findingList,arg);
        init(arg);
    }

    public Product(final List<Term> x) {
        this(x.toArray(Term.EmptyTermArray));
    }
    
    public static Product make(final Term... arg) {
        return new Product(arg);
    }   
    
    /**
     * Clone a Product
     * @return A new object, to be casted into an ImageExt
     */
    @Override
    public Product clone() {
        return new Product(term);
    }

    @Override
    public CompoundTerm clone(final Term[] replaced) {
        if(replaced == null) {
            return null;
        }
        return new Product(replaced);
    }

    /**
     * Try to make a Product from an ImageExt/ImageInt and a component. Called by the inference rules.
     * 尝试从ImageExt / ImageInt和组件制作Product。由推理规则调用。
     * @param image The existing Image
     * @param component The component to be added into the component list
     * @param index The index of the place-holder in the new Image -- optional parameter
     * @return A compound generated or a term it reduced to
     */
    public static Term make(final CompoundTerm image, final Term component, final int index) {
        final Term[] argument = image.cloneTerms();
        argument[index] = component;
        return new Product(argument);
    }
    
    /**
     * Get the operator of the term.
     * @return the operator of the term
     */
    @Override
    public NativeOperator operator() {
        return NativeOperator.PRODUCT;
    }
}
