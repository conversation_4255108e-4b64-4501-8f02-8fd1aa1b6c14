/*
 * The MIT License
 *
 * Copyright 2019 OpenNARS.
 *
 * Permission is hereby granted, free of charge, to any person obtaining a copy
 * of this software and associated documentation files (the "Software"), to deal
 * in the Software without restriction, including without limitation the rights
 * to use, copy, modify, merge, publish, distribute, sublicense, and/or sell
 * copies of the Software, and to permit persons to whom the Software is
 * furnished to do so, subject to the following conditions:
 *
 * The above copyright notice and this permission notice shall be included in
 * all copies or substantial portions of the Software.
 *
 * THE SOFTWARE IS PROVIDED "AS IS", WITHOUT WARRANTY OF ANY KIND, EXPRESS OR
 * IMPLIED, INCLUDING BUT NOT LIMITED TO THE WARRANTIES OF MERCHANTABILITY,
 * FITNESS FOR A PARTICULAR PURPOSE AND NONINFRINGEMENT. IN NO EVENT SHALL THE
 * AUTHORS OR COPYRIGHT HOLDERS BE LIABLE FOR ANY CLAIM, DAMAGES OR OTHER
 * LIABILITY, WHETHER IN AN ACTION OF CONTRACT, TORT OR OTHERWISE, ARISING FROM,
 * OUT OF OR IN CONNECTION WITH THE SOFTWARE OR THE USE OR OTHER DEALINGS IN
 * THE SOFTWARE.
 */
package org.opennars.operator.misc;

import edu.memphis.ccrg.lida.data.NeoUtil;
import edu.memphis.ccrg.lida.framework.initialization.AgentStarter;
import edu.memphis.ccrg.lida.framework.shared.Node;
import edu.memphis.ccrg.linars.CompoundTerm;
import edu.memphis.ccrg.linars.Memory;
import edu.memphis.ccrg.linars.Term;
//import org.neo4j.graphdb.Node;
import org.neo4j.graphdb.Transaction;
import org.opennars.entity.Task;
import org.opennars.interfaces.Timable;
import org.opennars.io.Parser;
import org.opennars.operator.Operation;
import org.opennars.operator.Operator;

import java.util.*;

import static com.warmer.kgmaker.KgmakerApplication.graphDb;
import static edu.memphis.ccrg.lida.framework.initialization.AgentStarter.narsese;
import static edu.memphis.ccrg.lida.framework.initialization.AgentStarter.pam;
//搜索单条，输出是否，bool
//案例：苹果是不是水果，是水果吗
public class SearchSB extends Operator {
    public SearchSB() {
        super("^searchSB");
    }
    public SearchSB(final String name) {
        super(name);
    }

    // 目前是数据中的查询操作，也可能是代码里的查询机制，无论在哪，只是触发机制和入口，具体查询还要另外实现
    // 代码查询=先明确是问题和具体方向，输入内容=本身要理解的，自主激活扩散，无主题则散乱无序，实时环境为主题
    // 语句理解后=问句作为搜索主题，自动触发受控定向搜索，推理得出答案，对陈述=解析语句本身+分析背后原意

    // 回应只是将答案输出，适应环境=问答场景环路，短程复杂场景=多场景嵌套，整合动机=竞争合作，图程构建执行
    // 复杂问题=时间跨度大，场景多，不是一条搜索推理即可，有问题解决方法论=需执行图程，先做计划

    // todo 与pam扩散整合，结合实时反馈，做更智能灵活的搜索
    @Override
    public List<Task> execute(Operation operation, Term[] args, Memory memory, Timable time) {
        List<Task> tasks = new ArrayList<>();
        if (args[0] instanceof CompoundTerm){
            CompoundTerm ctt = (CompoundTerm) args[0];
            Term[] terms = ctt.term;
            // 可能是语句数或词数
            int len = terms.length;
            System.out.println("Search:--------- " + Arrays.toString(terms));

//            getByCypher(len, terms, tasks);

            String sname = "";

            sname = getAnswer();

            try {
                Task task = narsese.parseTask(sname + ".");
                tasks.add(task);
            } catch (Parser.InvalidInputException e) {
                throw new RuntimeException(e);
            }

            // 如果没有结果，可能是没有经验记忆，可能是距离太长，可能判断错误、查找错误，
            // 只要不是需要凭空归纳，都是可达的，三段论都是一种搜索。其他错误都是经验问题，试错即可。
            // todo 从以上找若干中间点，再约搜若干次，找不到就从中间高置信点推理，
            //  根据点类型、缺失类型、目标类型，适用不同推理方式。后天图程？
            // 推理搜索明确分开，搜索=结构推理。反过来，各种推理都是搜索，只是搜索的方案不同。
            // 集中推理，针对一类、一组，进行连续推理。nars类型驱动，pam也是类型驱动。
        }

        // todo 可能是各种模态，如果是纯文本，可直接触发脑内语音或语言。感知数据，则要转语言
        return tasks;
    }

    private static String getAnswer() {
        String sname = "";
        // 苹果+去哪了，输出场景：(*,苹果,$状态)
        // (^搜索匹配,(*,(*,苹果,$状态),<$状态<->去哪了>)）
        // 搜索匹配条件集，直接用cypher语句，查包含苹果和一个其他元素a的场景，并且元素a与【去哪了】相似
        // todo 拓展为与内容无关，适配各种搜索场景和条件集，组装查询语句
        String cypher = "MATCH (c{name:'苹果'})-[r:arg0]->(a)<-[r1:arg1]-(b)-[r2:相似]->(d{name:'去哪了'}) return a";

        try (Transaction tx = graphDb.beginTx()) {
            List<Map<String, Object>> result = NeoUtil.getByCypher(cypher,tx);
            // 遍历结果，找到a的节点，然后获取场景，直接输出
            for (Map<String, Object> map : result) {
                org.neo4j.graphdb.Node a = (org.neo4j.graphdb.Node) map.get("a");
                sname = (String) a.getProperty("name");
            }
            tx.commit();
        }
        return sname;
    }

    // 搜索最短路径方案，两两组合，统计场景
    private static void getByCypher(int len, Term[] terms, List<Task> tasks){
        // 统计节点出现次数
        Map<Node, Integer> nodeMap = new HashMap<>();
        // 看数量，四个以下，则两两组合
        if (len < 4) {
            for (int i = 0; i < len; i++) {
                for (int j = i + 1; j < len; j++) {
                    getNS(terms, i, j, nodeMap);
                }
            }
        }else {
            // 四个及以上，只组合相邻的，一和二、三和四分别组合，一三、二四不要，剩下同理
            for (int i = 0; i < len; i++) {
                // 间隔组合，i-1的就不要了，因为i-1和i已经组合过了
                if (i % 2 == 0) {
                    getNS(terms, i, i + 1, nodeMap);
                }
            }
        }
        // 对map进行排序，然后遍历，如果次数大于1并且有场景标签，则触发场景，以场景点为中心，找到整个场景元素

        List<Map.Entry<Node, Integer>> list = new ArrayList<>(nodeMap.entrySet());
        list.sort((o1, o2) -> o2.getValue() - o1.getValue());
        List<Node> snodes ;
        for (Map.Entry<Node, Integer> entry : list) {
            Node key = entry.getKey();
            String tnname = key.getTNname();
            // todo 目前默认自传体场景，后续要根据场景类型来触发
            if (entry.getValue() > 1 && AgentStarter.selfscenemap.containsKey(tnname)) {
                snodes = NeoUtil.getSceneSon(tnname);
                StringBuilder sb = new StringBuilder();
                // 拼接场景，中间要加逗号，前后不用
                for (int i = 0; i < snodes.size(); i++) {
                    sb.append(snodes.get(i).getTNname());
                    if (i < snodes.size() - 1) {
                        sb.append(",");
                    }
                }
                try {
                    Task task = narsese.parseTask("(*," + sb + ").");
                    tasks.add(task);
                } catch (Parser.InvalidInputException e) {
                    throw new RuntimeException(e);
                }
                break;
            }
        }
    }

    private static void getNS(Term[] terms, int i, int j, Map<Node, Integer> nodeMap) {
        Set<Node> nodes1 = NeoUtil.getNsN2N(terms[i].toString(), terms[j].toString(), 6);
        for (Node node : nodes1) {
            if (nodeMap.containsKey(node)) {
                nodeMap.put(node, nodeMap.get(node) + 1);
            } else {
                pam.putMap(node, node.getTNname());
                nodeMap.put(node, 1);
            }
        }
    }

}