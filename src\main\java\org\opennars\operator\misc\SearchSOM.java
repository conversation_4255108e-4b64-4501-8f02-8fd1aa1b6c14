/*
 * The MIT License
 *
 * Copyright 2019 OpenNARS.
 *
 * Permission is hereby granted, free of charge, to any person obtaining a copy
 * of this software and associated documentation files (the "Software"), to deal
 * in the Software without restriction, including without limitation the rights
 * to use, copy, modify, merge, publish, distribute, sublicense, and/or sell
 * copies of the Software, and to permit persons to whom the Software is
 * furnished to do so, subject to the following conditions:
 *
 * The above copyright notice and this permission notice shall be included in
 * all copies or substantial portions of the Software.
 *
 * THE SOFTWARE IS PROVIDED "AS IS", WITHOUT WARRANTY OF ANY KIND, EXPRESS OR
 * IMPLIED, INCLUDING BUT NOT LIMITED TO THE WARRANTIES OF MERCHANTABILITY,
 * FITNESS FOR A PARTICULAR PURPOSE AND NONINFRINGEMENT. IN NO EVENT SHALL THE
 * AUTHORS OR COPYRIGHT HOLDERS BE LIABLE FOR ANY CLAIM, DAMAGES OR OTHER
 * LIABILITY, WHETHER IN AN ACTION OF CONTRACT, TORT OR OTHERWISE, ARISING FROM,
 * OUT OF OR IN CONNECTION WITH THE SOFTWARE OR THE USE OR OTHER DEALINGS IN
 * THE SOFTWARE.
 */
package org.opennars.operator.misc;

import edu.memphis.ccrg.lida.data.NeoUtil;
import edu.memphis.ccrg.linars.CompoundTerm;
import edu.memphis.ccrg.linars.Memory;
import edu.memphis.ccrg.linars.Term;
import org.neo4j.graphdb.Transaction;
import org.opennars.entity.Task;
import org.opennars.interfaces.Timable;
import org.opennars.io.Parser;
import org.opennars.language.Product;
import org.opennars.language.Similarity;
import org.opennars.language.Variable;
import org.opennars.operator.Operation;
import org.opennars.operator.Operator;

import java.util.*;

import static com.warmer.kgmaker.KgmakerApplication.graphDb;
import static edu.memphis.ccrg.lida.framework.initialization.AgentStarter.narsese;

//搜索单条，输出具体内容，基于多个条件综合
//案例：苹果+去哪了，输出场景：(*,苹果,$状态)
public class SearchSOM extends Operator {
    public SearchSOM() {
        super("^SearchSOM");
    }
    public SearchSOM(final String name) {
        super(name);
    }

    // 目前是数据中的查询操作，也可能是代码里的查询机制，无论在哪，只是触发机制和入口，具体查询还要另外实现
    // 代码查询=先明确是问题和具体方向，输入内容=本身要理解的，自主激活扩散，无主题则散乱无序，实时环境为主题
    // 语句理解后=问句作为搜索主题，自动触发受控定向搜索，推理得出答案，对陈述=解析语句本身+分析背后原意

    // 回应只是将答案输出，适应环境=问答场景环路，短程复杂场景=多场景嵌套，整合动机=竞争合作，图程构建执行
    // 复杂问题=时间跨度大，场景多，不是一条搜索推理即可，有问题解决方法论=需执行图程，先做计划

    // todo 与pam扩散整合，结合实时反馈，做更智能灵活的搜索
    @Override
    public List<Task> execute(Operation operation, Term[] args, Memory memory, Timable time) {
        List<Task> tasks = new ArrayList<>();
        if (args[0] instanceof CompoundTerm){
            CompoundTerm ctt = (CompoundTerm) args[0];
            Term[] terms = ctt.term;
            // 可能是语句数或词数
            System.out.println("Search:--------- " + Arrays.toString(terms));
            String sname = "";
            sname = getAnswer(terms);
            try {
                Task task = narsese.parseTask(sname + ".");
                tasks.add(task);
            } catch (Parser.InvalidInputException e) {
                throw new RuntimeException(e);
            }

            // 如果没有结果，可能是没有经验记忆，可能是距离太长，可能判断错误、查找错误，
            // 只要不是需要凭空归纳，都是可达的，三段论都是一种搜索。其他错误都是经验问题，试错即可。
            // todo 从以上找若干中间点，再约搜若干次，找不到就从中间高置信点推理，
            //  根据点类型、缺失类型、目标类型，适用不同推理方式。后天图程？
            // 推理搜索明确分开，搜索=结构推理。反过来，各种推理都是搜索，只是搜索的方案不同。
            // 集中推理，针对一类、一组，进行连续推理。nars类型驱动，pam也是类型驱动。
        }

        // todo 可能是各种模态，如果是纯文本，可直接触发脑内语音或语言。感知数据，则要转语言
        return tasks;
    }

    private static String getAnswer(Term[] terms) {
        String sname = "";
        String cypher = "";

        // 苹果+去哪了，输出场景：(*,苹果,$状态)
        // (^搜索匹配,(*,(*,苹果,$状态),<$状态<->去哪了>)）
        // 搜索匹配条件集，直接用cypher语句，查包含苹果和一个其他元素a的场景，并且元素a与【去哪了】相似

//        cypher = "MATCH (c{name:'苹果'})-[r:arg0]->(a)<-[r1:arg1]-(b)-[r2:相似]->(d{name:'去哪了'}) return a";

        cypher = assembleCypherQuery3(terms);

        // todo 拓展为与内容无关，适配各种搜索场景和条件集，组装查询语句
        //      考虑pam的扩散，顺序扩散，逆向扩散，双向扩散。非传递性的也扩散了？
        // 案例，输入两条语句：[(*,苹果,$状态), <$状态 <-> 去哪了>]
        // 如果term为Product等【格式如（*，x，y）】，则每个单词项转为一条语句，即类似(c)-[r:arg0]->(a)，arg的标号按顺序
        // 如果term为Similarity等【格式如：除圆括号外的括号，如<>】，则括号内内容作为一个整体，类似(b)-[r2:相似]->(d)，中间的双箭头为“相似”，其他同理
        // 如果term的某组件为Variable【格式有$符号】，则代表其关联的是查询目标，以此为输出。非$的词项，作为查询条件，作为name属性
        // 可能有多个变量，还包括自变量$和因变量#，需要用变量名来区分，可直接用变量名做节点
        // 语句term类型有多种可能，如Product和Similarity，先判断类型，
        cypher = "MATCH (苹果)-[r:arg0]->(a), (a)<-[r1:arg1]-(状态), (状态)-[r2:相似]->(d)\n" +
                 "WHERE 苹果.name = '苹果' AND d.name = '去哪了'\n" +
                 "RETURN a";


        cypher = "MATCH (苹果)-[r0:arg0]->(a0), (状态)-[r1:arg1]->(a0), (状态)-[r_sim:相似]->(去哪了)\n" +
                 "WHERE 苹果.name = '苹果' AND 去哪了.name = '去哪了'\n" +
                 "RETURN a0";

        try (Transaction tx = graphDb.beginTx()) {
            System.out.println("Cypher组装-------------: " + cypher);
            List<Map<String, Object>> result = NeoUtil.getByCypher(cypher,tx);
            // 遍历结果，找到a的节点，然后获取场景，直接输出
            for (Map<String, Object> map : result) {
                org.neo4j.graphdb.Node a = (org.neo4j.graphdb.Node) map.get("a0");
                sname = (String) a.getProperty("name");
                break;
            }
            tx.commit();
        }
        return sname;
    }


    private static String assembleCypherQuery3(Term[] terms) {
        StringBuilder cypher = new StringBuilder("MATCH ");
        List<String> whereClauses = new ArrayList<>();
        int index = 0;
        // 假设所有变量名都是"a"开头，后面跟数字，可以根据实际情况调整这部分逻辑
        Set<String> vars = new HashSet<>();
        for (Term term : terms) {
            CompoundTerm term0 = (CompoundTerm) term;
            Term[] subTerms = term0.term;
            // 格式如（*，x，y）
            if (term0 instanceof Product) {
                // 查询目标，如a0, a1, 用于输出，如场景框架，表示内部结构，不会字符化到语句中
                // 变量与查询目标可能不一样，暂输出目标，变量不输出
                String varName = "a" + index;
                vars.add(varName);
                for (int i = 0; i < subTerms.length; i++) {
                    // 添加节点，并添加到WHERE子句，除非它们是查询目标
                    // 需去除$符号，cypher不支持，但有无符号有区别，去掉后会混淆，如（*，场景，$场景），需判断是否去掉后有相同的
                    String tName = getCleanVarName(subTerms[i].toString());
                    cypher.append("(").append(tName).append(")-[r").append(i).append(":arg").append(i).append("]->(").append(varName).append(")");
                    // 每个场景要素都是一条查询边
                    cypher.append(", ");
                    if (subTerms[i] instanceof Variable) {
                        // 是查询目标，不添加到WHERE子句
                        continue;
                    }
                    whereClauses.add(tName + ".name = '" + subTerms[i] + "'");
                }
            }else if (term0 instanceof Similarity) {
                // 格式如：除圆括号外的括号，如<>
                String varName1 = getCleanVarName(subTerms[0].toString());
                String varName2 = getCleanVarName(subTerms[1].toString());
                cypher.append("(").append(varName1).append(")-[r_sim:相似]->(").append(varName2).append("), ");
                // 添加到WHERE子句，除非它们是查询目标
                if (!(subTerms[0] instanceof Variable)) {
                    whereClauses.add(varName1 + ".name = '" + subTerms[0] + "'");
                }
                if (!(subTerms[1] instanceof Variable)) {
                    whereClauses.add(varName2 + ".name = '" + subTerms[1] + "'");
                }
            }
            index++;
        }
        // 去除最后的逗号和空格
        if (cypher.length() > 0) {
            cypher.setLength(cypher.length() - 2);
        }
        // 添加WHERE子句
        if (!whereClauses.isEmpty()) {
            cypher.append("\nWHERE ");
            cypher.append(String.join(" AND ", whereClauses));
        }
        // 添加RETURN子句，这里简单返回所有变量节点，可以根据需要调整
        cypher.append("\nRETURN ");

//        for (String clause : whereClauses) {
//            if (clause.contains(".name = ")) {
//                String varName = clause.substring(0, clause.indexOf(".name = "));
//                if (varName.startsWith("a")) { // 是我们生成的变量名
//                    vars.add(varName);
//                }
//            }
//        }
//        // 假设 vars 是已经填充的变量名集合
//        boolean isFirst = true; // 标记是否为第一个元素
//        for (String var : vars) {
//            if (!isFirst) {
//                cypher.append(", "); // 如果不是第一个元素，则添加逗号和空格
//            }
//            cypher.append(var); // 添加变量名
//            isFirst = false; // 设置标记为 false，表示下一个元素不是第一个了
//        }

        // 假设 vars 是已经填充的变量名集合
        StringJoiner sj = new StringJoiner(", "); // 创建一个使用逗号和空格作为分隔符的StringJoiner
        for (String var : vars) {
            sj.add(var); // 添加变量名到StringJoiner
        }
        cypher.append(sj); // 将StringJoiner转换为字符串并添加到cypher中

        if (cypher.length() > 0 && cypher.charAt(cypher.length() - 1) == ',') {
            cypher.deleteCharAt(cypher.length() - 1); // 去除最后的逗号
            cypher.append(" "); // 添加空格以保持格式整洁
        }

        return cypher.toString();
    }
    // 辅助方法，用于清理变量名，去除$符号等
    private static String getCleanVarName(String varName) {
        // 仅保留字母和数字，可以根据需要调整规则
//        return varName.replaceAll("[^a-zA-Z0-9]", "");
        // 去掉$符号
        return varName.replaceAll("\\$", "");
    }


    // todo 根据词项类型，处理不同格式的词项。不是用字符串处理
    private static String assembleCypherQuery2(Term[] terms) {
        StringBuilder cypher = new StringBuilder("MATCH ");
        StringBuilder whereClause = new StringBuilder("WHERE ");
        List<String> returnClauses = new ArrayList<>();

        for (int i = 0; i < terms.length; i++) {
            CompoundTerm term = (CompoundTerm) terms[i];
            Term[] subTerms = term.term;
            String termString = term.toString();

            // 检查是否为查询目标
            if (termString.contains("$")) {
                // 假设查询目标的格式是(*, x, $y)，我们需要提取出y作为节点名
                String targetNodeName = termString.replaceAll(".*\\$(\\w+).*", "$1");
                returnClauses.add("(n" + i + "{name: '" + targetNodeName + "'})");
                whereClause.append("a").append(i).append(" = n").append(i).append(" AND ");
            } else if (termString.startsWith("<") && termString.endsWith(">")) {
                // 处理<>格式的关系
                String relationContent = termString.substring(1, termString.length() - 1);
                String[] relationParts = relationContent.split("<->");
                String leftNode = "n" + (i - 1); // 假设与上一个节点有关联
                String rightNode = "n" + (i + 1); // 假设与下一个节点有关联
                cypher.append("(").append(leftNode).append(")-[:").append(relationParts[0].trim()).append("]->(").append(rightNode).append(")");
                i++; // 跳过下一个Term，因为它已经在这个<>关系中处理了
            } else {
                // 处理普通关系，格式为(*, x, y)
                String leftNode = "n" + (i - 1);
                String rightNode = "n" + i;
                String relationName = "arg" + (i - 1); // 假设关系名按顺序编号
                String nodeName = termString.replaceAll(".*,(.*),\\w+.*", "$1"); // 提取节点名
                cypher.append("(").append(leftNode).append(")-[:").append(relationName).append("]->(").append(rightNode).append(":Node {name: '").append(nodeName).append("'})");
            }
        }

        // 去除WHERE子句的最后一个AND
        if (whereClause.length() > 0) {
            whereClause.setLength(whereClause.length() - 5); // 去掉最后的" AND "
        }

        cypher.append(" ").append(whereClause).append(" RETURN ");

        // 拼接RETURN子句
        String returnClause = String.join(", ", returnClauses);
        cypher.append(returnClause);

        return cypher.toString();
    }

    private static String assembleCypherQuery1(Term[] terms) {
        StringBuilder cypher = new StringBuilder("MATCH ");

        // 假设第一个元素是起始节点和关系
        cypher.append("(c{name:'").append(terms[0].toString()).append("'})-[r:arg0]->(a)");

        // 遍历剩余元素以构建中间部分和结束部分
        for (int i = 1; i < terms.length - 1; i++) {
            cypher.append("<-[").append("r" + i).append(":arg1]-(").append("node" + i).append(")");
        }

        // 最后一个元素是目标节点和关系
        cypher.append("<-[").append("r" + (terms.length - 1)).append(":相似]->(d{name:'")
                .append(terms[terms.length - 1].toCompoundTerm()).append("'})");

        // 添加返回语句
        cypher.append(" RETURN a");

        return cypher.toString();
    }
    private static String assembleCypherQuery0(Term[] terms) {
        StringBuilder cypher = new StringBuilder("MATCH ");
        List<String> conditions = new ArrayList<>();

        for (Term term : terms) {
            // 假设每个Term都有一个可以转换为cypher条件的方法
            // 这将取决于Term类的具体实现和你想要查询的具体属性
            String condition = convertTermToCypherCondition(term);
            conditions.add(condition);
        }

        // 使用AND连接所有条件
        String joinedConditions = String.join(" AND ", conditions);
        cypher.append(joinedConditions);
        cypher.append(" RETURN n");

        return cypher.toString();
    }

    private static String assembleCypherQuery(Term[] terms) {
        StringBuilder cypher = new StringBuilder("MATCH (n) WHERE ");
        List<String> conditions = new ArrayList<>();

        for (Term term : terms) {
            // 假设每个Term都有一个可以转换为cypher条件的方法
            // 这将取决于Term类的具体实现和你想要查询的具体属性
            String condition = convertTermToCypherCondition(term);
            conditions.add(condition);
        }

        // 使用AND连接所有条件
        String joinedConditions = String.join(" AND ", conditions);
        cypher.append(joinedConditions);
        cypher.append(" RETURN n");

        return cypher.toString();
    }

    private static String convertTermToCypherCondition(Term term) {
        // 这里需要根据Term的具体实现来解析和转换
        // 假设Term有一个getName()方法和getValue()方法
//        String name = term.getName(); // 假设这是属性的名字
//        String value = term.getValue(); // 假设这是我们要匹配的值
//        return String.format("n.%s = '%s'", name, value);
        return "";
    }

}