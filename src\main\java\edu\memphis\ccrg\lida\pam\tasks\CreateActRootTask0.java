/*******************************************************************************
 * Copyright (c) 2009, 2011 The University of Memphis.  All rights reserved. 
 * This program and the accompanying materials are made available 
 * under the terms of the LIDA Software Framework Non-Commercial License v1.0 
 * which accompanies this distribution, and is available at
 * http://ccrg.cs.memphis.edu/assets/papers/2010/LIDA-framework-non-commercial-v1.0.pdf
 *******************************************************************************/
package edu.memphis.ccrg.lida.pam.tasks;

import edu.memphis.ccrg.lida.data.NeoUtil;
import edu.memphis.ccrg.lida.framework.initialization.AgentStarter;
import edu.memphis.ccrg.lida.framework.shared.Link;
import edu.memphis.ccrg.lida.framework.shared.Node;
import edu.memphis.ccrg.lida.framework.tasks.FrameworkTaskImpl;
import edu.memphis.ccrg.lida.pam.PAMemory;
import edu.memphis.ccrg.linars.CompoundTerm;
import edu.memphis.ccrg.linars.Memory;
import edu.memphis.ccrg.linars.Term;
import groovy.transform.builder.InitializerStrategy;
import org.opennars.control.DerivationContext;
import org.opennars.entity.Task;
import org.opennars.io.Parser;

import java.util.ArrayList;
import java.util.List;
import java.util.Set;

import static edu.memphis.ccrg.lida.framework.FrameworkModuleImpl.taskSpawner;
import static edu.memphis.ccrg.lida.framework.initialization.AgentStarter.nar;
import static edu.memphis.ccrg.lida.framework.initialization.AgentStarter.narsese;
import static edu.memphis.ccrg.lida.pam.PAMemoryImpl.seqNs;

/**
 *
 */
public class CreateActRootTask0 extends FrameworkTaskImpl {
	private PAMemory pam;
	private Link link;
	private Task task;
	private Memory mem;
	private DerivationContext nal;
	private Term[] terms;

	private boolean isDaemon = false;// 是否是守护线程，守护线程不会阻止程序的结束
	private boolean isRoot = false;// 是否是时序根节点
	private String rootNode = null;// 时序根节点
	private String source = null;// 时序节点来源，前一位节点
	private String linkType = null;// 时序边来源类型
	private int index;
	private String preAttentStr;
	private String actStamp;

	public CreateActRootTask0(String rootNode, int index, String preAttentStr, boolean isRoot, String actStamp) {
		super(1, "tact");
//		this.terms = terms;
		this.rootNode = rootNode;
//		this.source = source;
//		this.linkType = linkType;
		this.index = index; // 输入队列中的位置
		this.preAttentStr = preAttentStr;
		this.isRoot = isRoot;// 是否是时序根节点
		this.actStamp = actStamp;
	}

	public CreateActRootTask0() {
		super(1, "tact");
	}

	@Override
	protected void runThisFrameworkTask(){
		// 多标志词限制，如首先+逗号分句+然后，中间的算第一大步，里面可能有多层小步骤，分句1+逗号+分句2，数量不定
		// 逗号后是其他分句或“然后”，分两种情况，需否定语句，标明分句n非然后？每句理解结果分别打包？矩阵式打包？
		// 原子步骤按类型驱动，什么类型就怎么构建。无法直接统筹构建=嵌套由高到低，需分步构建=一句一句来，因为逐句输入

		// 不能搞混先天时序构建后后天计划方法论，基础和应用区别。依赖变量句，变量句是零散的，不构成体系化的计划图程
		// 只有一分句也构建时序首？流式构建，来多少构建多少，而不是等待所有分句都到齐再构建。分句数量不定，不可预知
		// 大变量句也能一句表示整个大时序，左边原句，右边为要构建成的时序结构，左边推出右边，也就是执行时的结构，如nars1<=>心算时序
		// 变量句只是一条认知，原始nl能对应转化成什么narsese，不是操作，虽然里面可能包含操作。类似代码转ast，编译过程
		String mark = "";
		String mark2 = "";
		// 无根是构建方法名阶段，标志词可能为012个，零个是主动模仿学习，否则是被动学习，第二个可能是逗号等
		// 有根无源，则是构建时序首阶段，适用变量句如：($方法名,$次序标志1,$分句1)，多种形式，可无次序标志，甚至无方法名
		// 两者都有，则是构建时序节点阶段，变量句形式：($次序标志1,$分句1,$次序标志2,$分句2)，多种形式，分句数量不定
		// 变量句为过渡桥梁，依赖后天+理解。类似执行机制，数据驱动。逐句理解信息不足时，激活多个变量句，需等待理解结果
		// 次序标志词，一般是首位标志开始，否则会引起认知控制混乱，如第一个是“然后”，但也可自动纠正
//		if (source != null) {
//			mark = source;
//		}else {
//			// 没有第二标志词
//		}
		String inputQueueStr = AgentStarter.inputQueueStr;
		String nodeName;

		// todo 逐步构建语句结构，无完整案例情况下用构式，有则直接激活

		for(Node node : nar.memory.getNodes()){
			nodeName = node.getTNname();
			// todo 同类标志词搭配，解决多种不同类型标志词组合的问题，如首先+第一步等，实际语句中个别标志词会混乱，可纠正转为同类
			// 同类之间分句，再整体处理，非同类标志在当前处理中，当做普通分句，下一构建时序首阶段，再考虑标志词

			// 方案1=变量句枚举各种标志词组合和分句情况，难应对各种情况，且不易扩展，不可取，分段短变量句也麻烦
			// 方案2=同类标志词搭配，也就是共同指向上层概念，不同类标志词分开，还是得整体构建，分段麻烦
			// 方案3=流式构建，遇到标志词都特别处理，关键在如何触发构建，nars长整句转换=没法流式，执行同理

			// 就是普通嵌套构建，只是标记了图程，可调用，与“为什么”等同理。维护一个次序标志表，map，key是次序标志，value是位置
			// 每个分句对应一个变量句，（#，首先，其他）=/>（&/，其他）。&/=前向构建。
			// 无标志时，识别分句类型，按分句顺序即可，平铺无嵌套
			// 动词对应动作时序，但也可能是无关插入语。明确要构建的，明确不要构建的，不明确的再说

			String attentStr0 = node.getStrs();
			Link link = null;
			int index0 = inputQueueStr.indexOf(attentStr0);
			// 如果attenStr在inputQueueStr中的位置等于index，则表示该节点是当前构建的节点，已构建到index
			if (index0 == index + 1){
				boolean isSameType = false;
				boolean isHaveTypeThis = false;	// 本句有类别
				boolean isHaveTypePre = false;	// 前句有类别
				// 看两句的类别分别是什么，看头结点为nodename和preAttentStr的，有无isa语句
				// 并判断类别，普通分句没有类别，无类别和有类别，相当于不同类别。
				// 需判断本句和前句类别，不能遍历记忆中的links，因为有延迟，记忆中还没有构建出来，需查数据库
				// 需分别拿到nodename和preAttentStr作为头结点的link，关系类型限制为isa
				String query1 = "match (n)-[r:isa]->(m) where n.name = \'" + nodeName + "\' return r";
				Set<Link> links1 = NeoUtil.getLinksCypher(query1);
				String query2 = "match (n)-[r:isa]->(m) where n.name = \'" + preAttentStr + "\' return r";
				if (!links1.isEmpty()){
					isHaveTypeThis = true;
					Set<Link> links2 = NeoUtil.getLinksCypher(query2);
					if (!links2.isEmpty()){
						isHaveTypePre = true;
						// 如果两个link的尾节点相同，且link类别为isa，则两个link的头结点为同类
						for (Link link1 : links1){
							for (Link link2 : links2){
								if (link1.getSink().getTNname().equals(link2.getSink().getTNname())){
									isSameType = true;
								}
							}
						}
					}
				}

//				for (Link link0 : nar.memory.getLinks()){
//					// nodename作为头结点的link
//					if (link0.getSink().getTNname().equals(nodeName)){
//						if (link0.getTNname().equals("isa")){
//							isHaveTypeThis = true;
//							// preAttentStr作为头结点的link
//							for (Link link1 : nar.memory.getLinks()){
//								if (link1.getSink().getTNname().equals(preAttentStr)){
//									if (link1.getTNname().equals("isa")){
//										isHaveTypePre = true;
//										// 如果两个link的尾节点相同，且link类别为isa，则两个link的头结点为同类
//										if (link0.getSink().getTNname().equals(link1.getSink().getTNname())){
//											isSameType = true;
//										}
//									}
//								}
//							}
//						}
//					}
//				}
				if (!isHaveTypeThis){
					// 本句无类别，上面的前句循环也不会执行，所以这里要判断前句是否有类别
					Set<Link> links2 = NeoUtil.getLinksCypher(query2);
					if (!links2.isEmpty()){
						isHaveTypePre = true;
					}

//					for (Link link1 : nar.memory.getLinks()){
//						if (link1.getSink().getTNname().equals(preAttentStr)){
//							if (link1.getTNname().equals("isa")){
//								isHaveTypePre = true;
//							}
//						}
//					}

					if (!isHaveTypePre){
						// 前句也无类别，两句都是普通分句，同类
						isSameType = true;
					}
				}
				// 如果当前句与前一句类别不同，就构建首尾（回溯到最近的同类级别），分首尾两种情况，后句可不考虑。无需标志表，
				// 中间是普通时序，顺承即可。与前面有同类则同层，不同类则新建一层，两类或两组类别不能交叉，只需跟主路线各点比较
				// 是否有无同类，是运行时机制，一个标志表，原本应后天，也是一种认知，可表征和推理，有路径，（a,b）->不同类
				if(isRoot || !isSameType){
					// 来自时序根节点的时序链接，无论如何都会构建，但分时序首与普通时序，构建方式不同。
					// 不是同类=连续分句，且不是时序根节点，也构建时序首
					// todo 各步也可能有方法名。另外可能要保留次序词作为单独分句，而不仅是时序首头结点。不过已理解沉淀的时序可按自主意图输出
					link = NeoUtil.mergeLink(null, "时序首", rootNode, nodeName, "Verb", "Verb");
					// 可能是次序词，只构建一条边即可，也可能不是，是普通分句，需把分句也构建出来
					if(isHaveTypeThis){
						// 如果有可能的后续嵌套时序构建，则将上位时序存入主路线，以便回溯执行。与执行分开
						seqNs.getCreateMainPath().add(link);
						CreateActRootTask0 createActRootTask = new CreateActRootTask0(mark, index + attentStr0.length() + 1, nodeName,false, actStamp);
						taskSpawner.addTask(createActRootTask);
					}else {
						System.out.println("普通分句");
						// 没有变量的，最简单的时序，直接构建
						CreateSimpleSceneTask createSimpleSceneTask = new CreateSimpleSceneTask(link, node, actStamp);
						taskSpawner.addTask(createSimpleSceneTask);
					}
//				}else if(!isSameType){
//					link = NeoUtil.mergeLink(null, "时序首", rootNode, nodeName, "Verb", "Verb");
//					seqNs.getCreateMainPath().add(link);
//					CreateActRootTask0 createActRootTask = new CreateActRootTask0(mark, index + attentStr0.length() + 1, nodeName,false);
//					taskSpawner.addTask(createActRootTask);
				}else {
					// 同类，则看分句特征，触发对应构建线程。只有分句间才会同类
					// 只构建时序首边，如多个分句中的中间分句，只是时序链接
					System.out.println("同类");
					link = NeoUtil.mergeLink(null, "时序", rootNode, nodeName, "Verb", "Verb");
					CreateSimpleSceneTask createSimpleSceneTask = new CreateSimpleSceneTask(link, node, actStamp);
					taskSpawner.addTask(createSimpleSceneTask);
				}
				cancel();
				break;
			}
		}
	}
}

