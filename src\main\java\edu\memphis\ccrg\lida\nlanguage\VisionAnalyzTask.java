package edu.memphis.ccrg.lida.nlanguage;

import com.warmer.kgmaker.KgmakerApplication;
import edu.memphis.ccrg.lida.data.NeoUtil;
import edu.memphis.ccrg.lida.framework.ModuleName;
import edu.memphis.ccrg.lida.framework.shared.Link;
import edu.memphis.ccrg.lida.framework.shared.LinkImpl;
import edu.memphis.ccrg.lida.framework.shared.Node;
import edu.memphis.ccrg.lida.framework.shared.NodeImpl;
import edu.memphis.ccrg.lida.framework.tasks.FrameworkTaskImpl;
import edu.memphis.ccrg.lida.pam.PAMemory;
import edu.memphis.ccrg.lida.pam.PamImpl0;
import edu.memphis.ccrg.lida.pam.PamLink;
import edu.memphis.ccrg.lida.workspace.WorkspaceContent;
import edu.memphis.ccrg.linars.Term;
import org.opennars.inference.TemporalRules;
import org.opennars.language.Conjunction;

import java.util.*;
import java.util.concurrent.atomic.AtomicBoolean;

import static edu.memphis.ccrg.lida.framework.initialization.AgentStarter.nar;

/**
 * 二维结构分析，如视觉，分析结构为三维，即立体树状
 * 可能兼顾三维结构分析
 */
public class VisionAnalyzTask extends FrameworkTaskImpl {
	private PAMemory pam;
	private Link link;
	private WorkspaceContent listenNs;
//	private WorkspaceContent yufaNs;
	private WorkspaceContent yuyiNs;
	private String message;
	private List<Node> words = new ArrayList<>();
	int lsize;    // 对应场景总边数量属性
	int actlsize;// 场景已激活的边的数量
	Set<Link> links; //当前词所在的所有构式边
	int linksSize; //当前词所在的所有构式边的数量
	Node pos;
	TreeBag yiTreeBag;
	private AtomicBoolean started = new AtomicBoolean(false);
	Set<String> mmcache0 = PamImpl0.smcache;

	/**
	 * Default constructor
	 * @param link {@link PamLink}
	 * @param pam {@link PAMemory}
	 */
	public VisionAnalyzTask(Link link, PAMemory pam , int linksSize) {
		super(1);
		this.pam = pam;
		this.link = link;
		this.linksSize = linksSize;
		// 内听觉，外听觉，内视觉，外视觉，内文本，外文本，至少6个语句分析来源，来源汇总区分，统一到符号语义
		// 语言高频处理，需要常驻？分别尝试下
		listenNs = pam.getWorkspaceBuffer("listen").getBufferContent(null);
//		yufaNs = pam.getWorkspaceBuffer("yufa").getBufferContent(null);
		yuyiNs = pam.getWorkspaceBuffer("yuyi").getBufferContent(null);

		yiTreeBag = ((ChartTreeSet)yuyiNs).chartSet;
	}

	@Override
	protected synchronized void runThisFrameworkTask() {
//		System.out.println("语义分析任务开始");

		// todo 优先在预测集内扩散激活，甚至调起注意力动机
		actsence(link,null);

		cancel();
	}

	private void actsence(Link parent, TreeChart insertChart) {
		Node sink;
		String sname;

		sink = (Node) parent.getSink();
		sname = sink.getTNname();

		boolean isAct = false;
		TreeChart matchTreeChart = null;

		// 遍历treebag里nameTable的keyset中，左括号前字符是否匹配sname，也就是是否已经有该sink，有则跳过，没有则加入
		for (String key : yiTreeBag.nameTable.keySet()) {
			if (key.substring(0, key.indexOf("(")).equals(sname)) {
				matchTreeChart = yiTreeBag.nameTable.get(key);
				boolean isFind = false;
				// 判断当前key的value里，findingList是否包含当前parent链接，有则从findingList里移除，在foundList里添加
				// 无论是否嵌套，是否有匹配完整的嵌套，一旦符合根节点和有find边，都移除。是嵌套构式，要在parent替换整体子构式前移除
				if (matchTreeChart.findingList.contains(parent)) {
					matchTreeChart.findingList.remove(parent);
					isFind = true;
				}
				boolean isFound = false;

				// insertChart不为空，说明是中间层构式，来自下层激活，在未匹配完整时就要开始嵌套构建
				if (insertChart != null) {
					String sourceName = parent.getSource().getTNname();
					// 如果是正在find的构式，不是已有匹配完整嵌套构式，直接加入构建新嵌套
					if (isFind){
						// 用构式2整体替换foundList里的parent的source节点
						parent.setSource(insertChart);
						((LinkImpl)parent).init(((LinkImpl)parent).term);
						// 如果是已有匹配完整嵌套构式，先移除foundlist的对应部分，再加入构建新嵌套，保留已有的
						matchTreeChart.foundList.add((Term) parent);
						// 完成匹配链接总数
						matchTreeChart.cpsize = matchTreeChart.cpsize + insertChart.cpsize - 1;
					}
					Collection<Term> foundcopy = new ArrayList<>();
					foundcopy.addAll(matchTreeChart.foundList);

					// 跟上面if不能合并，因为found变了，addall也不能放里面
					if (!isFind) {
						int ssize;
						for (Term link : foundcopy) {
							Node ss = ((Link)link).getSource();
							String rr ;
							if(ss instanceof TreeChart){
								rr = ((TreeChart) ss).sceneRoot.getTNname();
								ssize = ((TreeChart) ss).cpsize;
							}else {
								rr = ss.getTNname();
								ssize = 1;
							}

							// 无论是否已嵌套，只要找到匹配的，就替换
							if (rr.equals(sourceName)) {
								isFound = true;
								((Link) link).setSource(insertChart);// 在原链接改
								((LinkImpl)link).init(((LinkImpl)link).term);

								matchTreeChart.cpsize = matchTreeChart.cpsize + insertChart.cpsize - ssize;
								break;
							}
						}
					}
					// 将foundList和findingList里的链接整合为新list
					List<Link> links2 = new ArrayList<>();
					for (Term link : matchTreeChart.foundList) {
						links2.add((Link) link);
					}
					for (Term link : matchTreeChart.findingList) {
						links2.add((Link) link);
					}

					NodeImpl root = (NodeImpl) matchTreeChart.sceneRoot;
					// 然后重新组装并更新matchTreeChart的sceneTerm
					matchTreeChart = bulidTree(root, root.getTNname(), foundcopy, matchTreeChart.foundList.size(), links2);
					matchTreeChart.init(matchTreeChart.term);
				}

				// 上面加了，这里就不用加了。如已有完整嵌套构式，也要加入foundList，把已匹配的对应部分替换成parent
				if (!matchTreeChart.foundList.contains(parent) && !isFound) {
					matchTreeChart.foundList.add((Term) parent);
				}

				// 如果findingList为空，说明该构式已经完整匹配，加入treebag的完整构式列表
				if (matchTreeChart.findingList.size() == 0) {
					// 不能无限嵌套，复杂度不能大于句子长度
					if (matchTreeChart.complexity < KgmakerApplication.message.size() * 6){
						// 部分完整也可输出部分理解，通达意识。但既不是听觉也不是视觉，只是个语义框架
						// 判断是否完全解析理解，已纳入所有输入词语，并无多余成分等
						// 完全理解，则往下激活顺承，激活动机，不完全也可激活，但需要竞争，不能激活就执行

						// 递归给语义树子节点编号和提交
						matchTreeChart = numberTree(matchTreeChart,0);

						yiTreeBag.completeTerms.put(matchTreeChart.toString(), matchTreeChart);
						// 继续往下激活，中间层构式，直接用成分而不是词性，虽然词性与成分一一对应
						Node node = new NodeImpl(sname);
						// todo 直接搜，而不是pam扩散，有针对性，但丢了很多其他信息，如内涵等，节律也不够好
						// 多种结构和多种内涵时，需要判别筛选，如苹果。语法辅助（结构搭配）、内涵辅助（语义搭配）
						// 禁止性搭配，短路抑制机制，不能XX。先天规则层抑制、后天义法层抑制
						Set<Link> links01 = NeoUtil.getLinks(node);
						if (links01 != null && !links01.isEmpty()) {
							for (Link link : links01) {
								// 递归激活下层构式，暂时限制边类型arg
								if (link.getTNname().length() >= 3 && link.getTNname().substring(0,3).equals("arg")) {
									actsence(link, matchTreeChart);
								}
							}
						}
					}
				}
				isAct = true;
				break; // 构式根节点都唯一，找到一个就跳出
			}
		}

		Object size = sink.getProperty("size");
		if (size != null) {
			// 分别按字符串类型或long类型取int值
			if(size instanceof String){
				lsize = Integer.parseInt((String) size);
			}else {
				lsize = ((Long) size).intValue();
			}
		}else {
			lsize = 100;	// 无size属性，说明是叶子节点或暂没设置，暂设为100，匹配率肯定低于0.5
		}

		// 根据根节点sink判断buffer里是否有两条边或以上构式，有则尝试构建语yi树
		// 第一个词通常无完整构式，也有可能首词非句首=语句被截断，乱序输入等，越靠近句首=构式角色越靠前
		if (!isAct) {
			// 未全激活，加入新链接后，看是否完整激活
			Collection<Term> scenels = ((ChartTreeSet)yuyiNs).getLinksOfSinkT(sname);
			actlsize = scenels.size();
			double sss = (double) actlsize / lsize ;
			// 一个词也能匹配单构式？只有单个词如感叹+拟声等，连词=但和与？np=n等也是单构式
			if (( sss == 0.5) && linksSize < 500 || sss > 0.5) {
				// 如果构式总边数与已激活边数达到一半，则直接激活整个框架为完整构式，优先级最高，放进确定集合里
				// 未匹配的可当预测，也可先不管，不完整构式不好嵌套，完整的嵌套能一次成型，无需每次更新总构式，再有新嵌套直接拼接
				Set<Link> links0 = NeoUtil.getSomeLinks(sink,null, "<", null, null);
				List<Link> links00 = new ArrayList<>(links0);

				bulidTree((Term) sink, sname, scenels, lsize, links00);
			}
		}
	}
	private TreeChart numberTree(TreeChart treeChart, int i) {
		// 遍历chart里的foundlist链接，找到order为0的节点，即最左边节点，然后递归给子节点编号
		// foundList是Collection，不能直接用get，需要转成list
		List<Term> foundList0 = new ArrayList<>(treeChart.foundList);
		int num = i; // 从i开始编号，num是累积编号，也是当前编号
		for (int j = 0; j < foundList0.size(); j++) {
			// 找到order与j相等的节点
			// todo foundlist一开始就按order排序，不用遍历。另外有容错纠错需求，词序不对不一定错误
			for (Term term : foundList0) {
				Link link = (Link) term;
				if (Integer.valueOf((String) (link.getProperty("order"))) == j){
					// 如果链接的source节点为chart，则递归给chart的子节点编号
					if (link.getSource() instanceof TreeChart) {
						TreeChart treeChart1 = (TreeChart) link.getSource();
						treeChart1 = numberTree(treeChart1, num);
						num = treeChart1.cpsize + num;
					}else {
						((LinkImpl)link).nowOrder = num;
						// 除了递归编号，还递归提交到语义图理解模块，无论是否完全匹配整句，相当于渐进式逐步理解
						// link是arg结构，提交到边集，但不会提交到nars。点可提交到点集
						// todo 整合到pam
//						pam.getListener().receivePercept(link, ModuleName.UnderstandGraph);

						pam.getListener().receivePercept(link, ModuleName.CurrentSM);

						nar.memory.addDefaultNode(link.getSource());
						nar.memory.addDefaultLink(link);

						System.out.println("提交语义图理解模块：---------"+ link);
						num++;
					}
				}
			}
		}
		return treeChart;
	}

	private TreeChart bulidTree(Term sceneRoot, String rootName, Collection<Term> foundList,
								int lsize , List<Link> links00) {
		List<Term> findingList = new ArrayList<>();
		Term[] components = new Term[lsize];// 无关系元素集合，次序关系在元素属性里，可能有子节点
		Link ll;
		Map<Integer,String> orderMap = new HashMap();
		String subSceneStr = "";
		Object orderObj;
		// 图搜索得到的都是链接，暂不提取转term数组的方法，term数组还要遍历，这里现场实时遍历更便捷
		for (int i = 0; i < lsize; i++) {
			ll = links00.get(i);
			if (!ll.getTNname().substring(0,2).equals("ar")) {
				continue;
			}
			components[i] = (Term) ll;    // 只是遍历次序，实际次序在元素属性里

//			pam.getListener().receivePercept(ll, ModuleName.GrammarGraph);

			pam.getListener().receivePercept(link, ModuleName.CurrentSM);

			nar.memory.addDefaultNode(link.getSource());
			nar.memory.addDefaultLink(link);

			if (!foundList.contains(ll)) {
				findingList.add((Term) ll); // 未激活的边，都放预测边集里
			}
		}
		StringBuffer buf = new StringBuffer();
		Term conjunction = Conjunction.make(findingList, components, TemporalRules.ORDER_NONE, false);

		yiTreeBag.putBack((Conjunction) conjunction, 10f, nar.memory);

		return (TreeChart) conjunction;
	}
}

