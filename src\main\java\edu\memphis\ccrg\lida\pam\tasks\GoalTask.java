/*******************************************************************************
 * Copyright (c) 2009, 2011 The University of Memphis.  All rights reserved. 
 * This program and the accompanying materials are made available 
 * under the terms of the LIDA Software Framework Non-Commercial License v1.0 
 * which accompanies this distribution, and is available at
 * http://ccrg.cs.memphis.edu/assets/papers/2010/LIDA-framework-non-commercial-v1.0.pdf
 *******************************************************************************/
package edu.memphis.ccrg.lida.pam.tasks;

import edu.memphis.ccrg.lida.data.NeoUtil;
import edu.memphis.ccrg.lida.framework.ModuleName;
import edu.memphis.ccrg.lida.framework.shared.Link;
import edu.memphis.ccrg.lida.framework.shared.Node;
import edu.memphis.ccrg.lida.framework.shared.NodeStructure;
import edu.memphis.ccrg.lida.framework.tasks.FrameworkTaskImpl;
import edu.memphis.ccrg.lida.pam.PAMemory;
import edu.memphis.ccrg.lida.pam.PamLink;

import java.util.Set;

/**
 * A task to add a {@link PamLink} and its sink to the percept.
 * 
 * <AUTHOR> J. McCall
 * @see ExcitationTask creates this task
 * @see PropagationTask creates this task
 */
public class GoalTask extends FrameworkTaskImpl {

	private PAMemory pam;
//	private Link link;
	private NodeStructure seqNs;
	private NodeStructure nonNs;
	private NodeStructure goalNs;
	private Node sink;
	private boolean iswant;

	/**
	 * Default constructor
	 * @param sink {@link PamLink}
	 * @param pam {@link PAMemory}
	 */
	public GoalTask(Node sink, PAMemory pam, NodeStructure seqNs, NodeStructure nonNs,
					NodeStructure goalNs, boolean iswant, String type) {
		super(1, type);
		this.pam = pam;
		this.sink = sink;
		this.seqNs = seqNs;
		this.nonNs = nonNs;
		this.goalNs = goalNs;
		this.iswant = iswant;
	}

	/**
	 * Adds link's sink to the percept and tries to add the link as well then finishes.
	 */
	@Override
	protected void runThisFrameworkTask() {
		cancel();
	}
	protected void runThisFrameworkTask0() {
		String sname = sink.getTNname();
		Set<Link> Links = NeoUtil.getSomeLinks(sink, null, null, null, "变量");
		for (Link link : Links) {
			for (Link l : seqNs.getLinksOfSource(link.getSource().getTNname())) {
				if (l.getTNname().equals("nowisa")) {
					// 目前扩散激活不包括neo以外的新连接，头尾节点会各自扩散激活，节点激活值
					l.setActivation(l.getActivation() + 0.3);
					pam.putMap((Node)l.getSink(), l.getSink().getTNname());

					// 就只加入无意识？暂无时序、自我、情感、动机等buffer需求
					pam.getListener().receivePercept((Node)l.getSink(), ModuleName.NonGraph);
					pam.getListener().receivePercept(l.getSource(), ModuleName.NonGraph);
					pam.getListener().receivePercept(l, ModuleName.NonGraph);

//					String from0 = "varisa";
//					if (iswant) {
						// 欲求的变量， 欲求的动作，分别匹配汇合，深度2 = 只执行一次？
//						propagateActivationToParents(l.getSource(), 1, "varwant_var");
						System.out.println(l.toString() + "---varwant_var---" + " --------- deep " + 1);

						l.getSource().setActivation(l.getSource().getActivation() + 0.4);

						// todo 没有欲求，只有时序转动机，转=需要先天激励或动机经验？
						// 来自欲求变量的激活，且激活的是场景，则判断是否有欲求动作激活
//						if (
//							from.equals("varwant_var") &&
//							AgentStarter.scenemap.containsKey(sname)) {

//							Set<Link> sLinks = NeoUtil.getSomeLinks(l.getSource(), null, null, null, "场景");
					// todo 这里少了一层遍历 pam的 for (Link parent : parentLinkSet)
						int nodeid;
						for (Link slink : nonNs.getLinksOfSink(sname)) {
							nodeid = slink.getSource().getNodeId();
							if (slink.getCategory().getName().equals("动作") && goalNs.containsNode(nodeid)) {
								// 根据欲求的动作，答说 [1.00] ---动作 [.80]--->ft111 [1.00]
								for (Link link0 : goalNs.getLinksOfSource(nodeid)) {
//									if (sname.equals(link0.getSink().getName())) continue;
									for (Link link2 : nonNs.getLinksOfSink(link0.getSink().getTNname())) {
										// 找到欲求节点，给实例化欲求赋值激励，ft10 [1.00] ---欲求 [.90]--->ft111 [1.00]
										if (link2.getCategory().getName().equals("欲求")) {
											for (Link link1 : nonNs.getLinksOfSource(sname)) {
												if (link1.getCategory().getName().equals("心理计划")) {
													double invent = link2.getSink().getIncentiveSalience();

													if (invent == 0.0) continue;
													if (goalNs.containsLink(slink)) continue;    // 已经执行过一次

													// sink实例，ft151，ft150 = 不会进入这里，link的sink就是parent的sink
													sink.setIncentiveSalience(invent);
													// 动作与实例的链接，加入目标buffer完成实例化，sink是动作link的尾节点
													pam.getListener().receivePercept(sink, ModuleName.GoalGraph);
													pam.getListener().receivePercept(slink, ModuleName.GoalGraph);

													link1.getSink().setIncentiveSalience(invent);
													// 加入计划，以备执行，头尾节点都要，当前parent的sink是头节点，如ft151
													pam.getListener().receivePercept(sink, ModuleName.SeqGraph);
													pam.getListener().receivePercept((Node) link1.getSink(), ModuleName.SeqGraph);
													pam.getListener().receivePercept(link1, ModuleName.SeqGraph);
													// 实例化动机计划建模，不扩散激活，以免摊开太大，且要约束很多
													pam.getActRoot(link1, false, false, null);
												}
											}
										}
									}
								}
							}
						}
//					}

//					} else {
						// 从变量开始激活，长传递链也能传递，变量--nowisa--》实例
//					propagateActivationToParents(l.getSource(), deep, from0);
//					System.out.println(l.toString() + "---varisa_var---"+" --------- deep " + deep);
//					}
					Set<Link> linkSet = NeoUtil.getSomeLinks(sink, "动作", null, null, null);
					for (Link link0 : linkSet) {
						// 直接激活、加入等一串pam常规操作
						pam.putMap((Node)link0.getSink(), link0.getSink().getTNname());
						// 就只加入无意识？
						pam.getListener().receivePercept((Node)link0.getSink(), ModuleName.NonGraph);
						pam.getListener().receivePercept(link0.getSource(), ModuleName.NonGraph);
						pam.getListener().receivePercept(link0, ModuleName.NonGraph);

						if (iswant) {
//							propagateActivationToParents(link0.getSource(), 1, "varwant_verb");
//							Set<Link> linkSet = NeoUtil.getSomeLinks(link0.getSource(), "动作", null, null, null);

							System.out.println(link0.toString() + "---varwant_verb---" + " ------ deep " + 1);

							link0.getSource().setActivation(link0.getSource().getActivation() + 0.4);

//						} else {
//							propagateActivationToParents(link0.getSource(), deep, from0);
//							System.out.println(link0.toString() + "---varisa_verb---"+" ---- deep " + deep);
						}
					}
				}
			}
		}

		cancel();
	}
	
}

