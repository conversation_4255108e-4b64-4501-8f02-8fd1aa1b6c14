/*******************************************************************************
 * Copyright (c) 2009, 2011 The University of Memphis.  All rights reserved. 
 * This program and the accompanying materials are made available 
 * under the terms of the LIDA Software Framework Non-Commercial License v1.0 
 * which accompanies this distribution, and is available at
 * http://ccrg.cs.memphis.edu/assets/papers/2010/LIDA-framework-non-commercial-v1.0.pdf
 *******************************************************************************/
package edu.memphis.ccrg.lida.framework;

import edu.memphis.ccrg.lida.attentioncodelets.AttentionCodeletModule;
import edu.memphis.ccrg.lida.episodicmemory.EpisodicMemory;
import edu.memphis.ccrg.lida.pam.PAMemory;
import edu.memphis.ccrg.lida.sensorymemory.SensoryMemory;
import edu.memphis.ccrg.lida.workspace.workspacebuffers.WorkspaceBuffer;

import java.util.Collection;
import java.util.Collections;
import java.util.HashMap;
import java.util.Map;

/**
 * Encapsulation of the name of a {@link FrameworkModule}.  Provides several public-static instances by default. 
 * <AUTHOR> Snaider
 * <AUTHOR> J. McCall
 */
public class ModuleName {
    /**
	 * String representation of {@link ModuleName}
	 */
	public final String name;
	private static Map<String, ModuleName> moduleNames = new HashMap<String, ModuleName>();

	/**
	 * Returns ModuleName of specified name. 
	 * @param name String
	 * @return ModuleName
	 */
	public static ModuleName getModuleName(String name) {
		return moduleNames.get(name);
	}

	/**
	 * Creates and adds a new module name if name is not already defined.
	 * Returns new ModuleName or existing {@link ModuleName} associated with the name.
	 * @param name String
	 * @return ModuleName
	 */
	public static ModuleName addModuleName(String name) {
		if (!moduleNames.containsKey(name)) {
			new ModuleName(name);
		}
		return moduleNames.get(name);
	}

	private ModuleName(String name) {
		this.name = name;
		moduleNames.put(name, this);
	}
	
	@Override
	public String toString(){
		return name;
	}

	/**
	 * Returns a {@link Collection} of all {@link ModuleName}s
	 * @return all module names
	 */
	public static Collection<ModuleName> values() {
		return Collections.unmodifiableCollection(moduleNames.values());
	}

	/**
	 * Name of an {@link edu.memphis.ccrg.lida.environment.Environment} module
	 */
	public final static ModuleName Environment = new ModuleName("Environment");
	/**
	 * Name of a {@link SensoryMemory} module
	 */
	public final static ModuleName SensoryMemory = new ModuleName(
			"SensoryMemory");
	/**
	 * Name of a {@link PAMemory} module
	 */
	public final static ModuleName PerceptualAssociativeMemory = new ModuleName(
			"PAMemory");
	/**
	 * Name of an {@link EpisodicMemory} module
	 */
	public final static ModuleName TransientEpisodicMemory = new ModuleName(
			"TransientEpisodicMemory");	// todo 瞬时情景记忆和陈述性记忆
	/**
	 * Name of an {@link EpisodicMemory} module
	 */
	public final static ModuleName DeclarativeMemory = new ModuleName(
			"DeclarativeMemory");
	/**
	 * Name of a {@link edu.memphis.ccrg.lida.workspace.Workspace} module
	 */
	public final static ModuleName Workspace = new ModuleName("Workspace");
	/**
	 * Name of a {@link WorkspaceBuffer} module
	 */
	public final static ModuleName PerceptualBuffer = new ModuleName(
			"PerceptualBuffer");
	/**
	 * Name of a {@link WorkspaceBuffer} module
	 */
	public final static ModuleName EpisodicBuffer = new ModuleName(
			"EpisodicBuffer");
	/**
	 * Name of a {@link edu.memphis.ccrg.lida.workspace.workspacebuffers.BroadcastQueue} module
	 */
	public final static ModuleName BroadcastQueue = new ModuleName("BroadcastQueue");

	// 各个缓存独立存取=建表分类统计，自由交互，全局通用=全脑可达，可有重叠，可同步可不同步，可增删改查属性
	// 内容重叠，可能是概念的分模态属性，如猴子=视听文各模态的猴子，每个模态都可直达概念本体，然后激活其他模态属性
	// 目前没有视听等模态，需要概念本体（词项表示）+模态标记，来表征具体的模态属性，
	// 缓存交互=有点被动，缓存A新内容不是直接触发缓存B线程，而只能由线程遍历？内容交互与线程交互不一样
	// 内容融合、轮询、转码，动机、情感、模态等混合缓存？

	/* Name of a {@link WorkspaceBuffer} module	*/
	public final static ModuleName CurrentSM = new ModuleName("CurrentSituationalModel");

	// 无意识buffer，受情感和注意调控
	public final static ModuleName NonGraph = new ModuleName("NonGraph");
	public final static ModuleName narmemory = new ModuleName("narmemory");
	// 情感buffer，具体的情绪情感，调控其他的基础
	public final static ModuleName FeelGraph = new ModuleName("FeelGraph");
	// 动机-目标buffer，想要的
	public final static ModuleName GoalGraph = new ModuleName("GoalGraph");
	// 时序buffer，想做的计划，可单个动作，与想要的和内容区分
	public final static ModuleName SeqGraph = new ModuleName("SeqGraph");

	// 内容buffer，听到看到的内容，包括理解语义网？
	public final static ModuleName ConcentGraph = new ModuleName("ConcentGraph");

	// 各模态注意前内容包，视听，文=外界输入文本，想象的视听，想象的文=非视听+内部产生的文本
	public final static ModuleName VisionGraph = new ModuleName("VisionGraph");
	public final static ModuleName ListenGraph = new ModuleName("ListenGraph");
	public final static ModuleName TextGraph = new ModuleName("TextGraph");
	public final static ModuleName VVGraph = new ModuleName("VVGraph");
	public final static ModuleName VListenGraph = new ModuleName("VListenGraph");

//	public final static ModuleName VTextGraph = new ModuleName("VTextGraph");

	// 想表达的内容，与想做的计划区分
	public final static ModuleName WordGraph = new ModuleName("WordGraph");
	// 场景buffer，可能做海马buffer，用于行为构建、场景回忆、新建数据
	public final static ModuleName SceneGraph = new ModuleName("SceneGraph");
	// 语法buffer，与场景分离后整合成语句
	public final static ModuleName GrammarGraph = new ModuleName("GrammarGraph");
	// todo【】？后期视觉规则=类似语法+与场景整合成连续帧
	// 理解蕴含语义网，包含isa和蕴含。与内容网冲突，先用内容网
	public final static ModuleName UnderstandGraph = new ModuleName("UnderstandGraph");

	/**
	 * Name of an {@link AttentionCodeletModule} 
	 */
	public final static ModuleName AttentionModule = new ModuleName(
			"AttentionModule");
	/**
	 * Name of a {link edu.memphis.ccrg.lida.workspace.structurebuildingcodelets.StructureBuildingCodeletModule}
	 */
	public final static ModuleName StructureBuildingCodeletModule = new ModuleName(
			"StructureBuildingCodeletModule");
	/**
	 * Name of a {@link edu.memphis.ccrg.lida.globalworkspace.GlobalWorkspace} module
	 */
	public final static ModuleName GlobalWorkspace = new ModuleName(
			"GlobalWorkspace");
	/**
	 * Name of a {@link edu.memphis.ccrg.lida.proceduralmemory.ProceduralMemory} module
	 */
	public final static ModuleName ProceduralMemory = new ModuleName(
			"ProceduralMemory");
	/**
	 * Name of an {@link edu.memphis.ccrg.lida.actionselection.ActionSelection} module
	 */
	public final static ModuleName ActionSelection = new ModuleName(
			"ActionSelection");
	/**
	 * Name of a {@link edu.memphis.ccrg.lida.sensorymotormemory.SensoryMotorMemory} module
	 */
	public final static ModuleName SensoryMotorMemory = new ModuleName(
			"SensoryMotorMemory");
	/**
	 * Name of an {@link Agent} module
	 */
	public final static ModuleName Agent = new ModuleName("Agent");
	/**
	 * Name of an unnamed module
	 */
	public final static ModuleName UnnamedModule = new ModuleName("UnnamedModule");
}
