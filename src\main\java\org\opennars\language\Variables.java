/* 
 * The MIT License
 *
 * Copyright 2018 The OpenNARS authors.
 *
 * Permission is hereby granted, free of charge, to any person obtaining a copy
 * of this software and associated documentation files (the "Software"), to deal
 * in the Software without restriction, including without limitation the rights
 * to use, copy, modify, merge, publish, distribute, sublicense, and/or sell
 * copies of the Software, and to permit persons to whom the Software is
 * furnished to do so, subject to the following conditions:
 *
 * The above copyright notice and this permission notice shall be included in
 * all copies or substantial portions of the Software.
 *
 * THE SOFTWARE IS PROVIDED "AS IS", WITHOUT WARRANTY OF ANY KIND, EXPRESS OR
 * IMPLIED, INCLUDING BUT NOT LIMITED TO THE WARRANTIES OF MERCHANTABILITY,
 * FITNESS FOR A PARTICULAR PURPOSE AND NONINFRINGEMENT. IN NO EVENT SHALL THE
 * AUTHORS OR COPYRIGHT HOLDERS BE LIABLE FOR ANY CLAIM, DAMAGES OR OTHER
 * LIABILITY, WHETHER IN AN ACTION OF CONTRACT, TORT OR OTHERWISE, ARISING FROM,
 * OUT OF OR IN CONNECTION WITH THE SOFTWARE OR THE USE OR OTHER DEALINGS IN
 * THE SOFTWARE.
 */
package org.opennars.language;

import edu.memphis.ccrg.linars.CompoundTerm;
import edu.memphis.ccrg.linars.Term;
import org.opennars.inference.TemporalRules;
import org.opennars.io.Symbols;

import java.util.LinkedHashMap;
import java.util.LinkedHashSet;
import java.util.Map;
import java.util.Random;
import java.util.Set;

/**
 * Static utility class for static methods related to Variables
 * 静态实用程序类，用于与变量相关的静态方法
 * <AUTHOR> Hammer
 */
public class Variables {
    public static boolean findSubstitute(Random rnd, final char type, final Term term1, final Term term2, final Map<Term, Term> map1, final Map<Term, Term> map2) {
        return findSubstitute(rnd, type, term1, term2, new Map[] { map1, map2 });
    }
    public static boolean allowUnification(final char type, final char uniType)
    {   // it is valid to allow dependent var unification in case that a independent var unification is happening,
        // 如果发生独立变量统一，允许依赖变量统一是有效的
        //as shown in the 
        // <(&&,<$1 --> [ENGLISH]>, <$2 --> [CHINESE]>, <(*, $1, #3) --> REPRESENT>, <(*, $2, #3) --> REPRESENT>) ==> <(*, $1, $2) --> TRANSLATE>>.
        //example by Kai Liu.
        //1.7.0 and 2.0.1 also already allowed this, so this is for v1.6.x now.
        
        if(uniType == type) { //the usual case
            return true;
        }
        if(uniType == Symbols.VAR_INDEPENDENT) { //the now allowed case
            if(type == Symbols.VAR_DEPENDENT ||
               type == Symbols.VAR_QUERY) {
                return true;
            }
        }
        if(uniType == Symbols.VAR_DEPENDENT) { //the now allowed case
            return type == Symbols.VAR_QUERY;
        }
        return false;
    }
    
    /** map is a 2-element array of Map<Term,Term>. it may be null, in which case
     * the maps will be instantiated as necessary.  
     * this is to delay the instantiation of the 2 Map until necessary to avoid
     * wasting them if they are not used.
     * map是一个Map<Term,Term>的2元素数组。它可以为null，在这种情况下，map映射将根据需要被实例化。
     * 这是为了延迟2个Map的实例化，直到必要的时候才避免浪费它们，如果它们没有被使用。
     * findSubstitute意思是找到替代，可做判断词项匹配用，词项匹配，则有替代，否则没有替代
     */
    public static boolean findSubstitute(Random rnd, final char type, final Term term1, final Term term2, final Map<Term, Term>[] map) {
        return findSubstitute(rnd, type, term1, term2, map, false);
    }
    public static boolean findSubstitute(Random rnd, final char type, final Term term1,
                                         final Term term2, final Map<Term, Term>[] map, final boolean allowPartial) {
        boolean term1HasVar = term1.hasVar(type);
        if(type == Symbols.VAR_INDEPENDENT) {
            term1HasVar |= term1.hasVarDep();
            term1HasVar |= term1.hasVarQuery();
        }
        if(type == Symbols.VAR_DEPENDENT) {
            term1HasVar |= term1.hasVarQuery();
        }
        final boolean term2HasVar = term2.hasVar(type);

        final boolean term1Var = term1 instanceof Variable;
        final boolean term2Var = term2 instanceof Variable;
        
        if(allowPartial && term1 instanceof Conjunction && term2 instanceof Conjunction) {
            final Conjunction c1 = (Conjunction) term1;
            final Conjunction c2 = (Conjunction) term2;
            //more effective matching for NLP
            if(c1.getTemporalOrder() == TemporalRules.ORDER_FORWARD &&
                    c2.getTemporalOrder() == TemporalRules.ORDER_FORWARD) {
                final int size_smaller = c1.size();
                if(c1.size() < c2.size()) {
                    //find an offset that works
                    for(int k = 0;k < (c2.term.length - c1.term.length);k++) {

                        if(map[0] == null) {
                            map[0] = new LinkedHashMap<>();
                        }
                        if(map[1] == null) {
                            map[1] = new LinkedHashMap<>();
                        }

                        final Map<Term, Term>[] mapk = copyMapFrom(map);
                        boolean succeeded = true;
                        for(int j = k;j < k + size_smaller;j++) {
                            final int i = j-k;
                            final Map<Term, Term>[] mapNew = copyMapFrom(map);
                            //attempt unification:
                            if(findSubstitute(rnd, type,c1.term[i],c2.term[j],mapNew)) {
                                appendToMap(mapNew[0], mapk[0]);
                                appendToMap(mapNew[1], mapk[1]);
                            } else { //another shift k is needed
                                succeeded = false;
                                break;
                            }
                        }
                        if(succeeded) {
                            appendToMap(mapk[0], map[0]);
                            appendToMap(mapk[1], map[1]);
                            return true;
                        }
                    }
                }
            }
        }
        
        final boolean termsEqual = term1.equals(term2);
        if (!term1Var && !term2Var && termsEqual)  {
            return true;
        }
        // variable "renaming" to variable of same type is always valid
        // 变量“重命名”为相同类型的变量始终有效
        if(term1 instanceof Variable && term2 instanceof Variable) {
            final Variable v1 = (Variable) term1;
            final Variable v2 = (Variable) term2;
            if(v1.getType() == v2.getType()) {
                final Variable CommonVar = makeCommonVariable(term1, term2);
                if (map[0] == null) {  map[0] = new LinkedHashMap<>(); map[1] = new LinkedHashMap<>(); }
                map[0].put(v1, CommonVar);
                map[1].put(v2, CommonVar);
                return true;
            }
        }
        final boolean term1VarUnifyAllowed = term1Var && allowUnification(((Variable) term1).getType(), type);
        final boolean term2VarUnifyAllowed = term2Var && allowUnification(((Variable) term2).getType(), type);

        if (term1VarUnifyAllowed || term2VarUnifyAllowed) {
            Term termA = term1VarUnifyAllowed ? term1 : term2;
            Term termB = term1VarUnifyAllowed ? term2 : term1;
            Variable termAAsVariable = (Variable)termA;
            //https://github.com/opennars/opennars/issues/482:
            int mapIdx = term1VarUnifyAllowed ? 0 : 1;
            final Term t = map[mapIdx]!=null ? map[mapIdx].get(termAAsVariable) : null;
            if (t != null) {
                return findSubstitute(rnd, type, t, termB, map);
            }

            if (map[0] == null) {  map[0] = new LinkedHashMap<>(); map[1] = new LinkedHashMap<>(); }

            if (term1VarUnifyAllowed) {
                if ((termB instanceof Variable) && allowUnification(((Variable) termB).getType(), type)) {
                    final Variable CommonVar = makeCommonVariable(termA, termB);
                    map[0].put(termAAsVariable, CommonVar);
                    map[1].put(termB, CommonVar);
                } else {
                    if(termB instanceof Variable && ((((Variable)termB).getType()==Symbols.VAR_QUERY && ((Variable)termA).getType()!=Symbols.VAR_QUERY) ||
                        (((Variable)termB).getType()!=Symbols.VAR_QUERY && ((Variable)termA).getType()==Symbols.VAR_QUERY))) {
                        return false;
                    }
                    map[0].put(termAAsVariable, termB);
                    if (termAAsVariable.isCommon()) {
                        map[1].put(termAAsVariable, termB);
                    }
                }
            } else {
                map[1].put(termAAsVariable, termB);
                if (termAAsVariable.isCommon()) {
                    map[0].put(termAAsVariable, termB);
                }
            }
            return true;
        } else {
            final boolean hasAnyTermVars = term1HasVar || term2HasVar;
            final boolean termsHaveSameClass = term1.getClass().equals(term2.getClass());
            
            if (!(hasAnyTermVars && termsHaveSameClass && term1 instanceof CompoundTerm)) {
                return termsEqual;
            }
            final CompoundTerm cTerm1 = (CompoundTerm) term1;
            final CompoundTerm cTerm2 = (CompoundTerm) term2;

            //consider temporal order on term matching 考虑term匹配的时间顺序
            final boolean isSameOrder = term1.getTemporalOrder() == term2.getTemporalOrder();
            final boolean isSameSpatial = term1.getIsSpatial() == term2.getIsSpatial();
            final boolean isSameOrderAndSameSpatial = isSameOrder && isSameSpatial;

            final boolean areBothConjunctions = term1 instanceof Conjunction && term2 instanceof Conjunction;
            final boolean areBothImplication = term1 instanceof Implication && term2 instanceof Implication;
            final boolean areBothEquivalence = term1 instanceof Equivalence && term2 instanceof Equivalence;

            if((areBothConjunctions && !isSameOrderAndSameSpatial) ||
                ((areBothEquivalence || areBothImplication) && !isSameOrder)) {
                return false;
            }
            // 只是简单判断长度，但不考虑具体内容。可能是两个及以上分句
            if (cTerm1.size() != cTerm2.size()) {
                return false;
            }
            if ((cTerm1 instanceof ImageExt) && (((ImageExt) cTerm1).relationIndex != ((ImageExt) cTerm2).relationIndex) || (cTerm1 instanceof ImageInt) && (((ImageInt) cTerm1).relationIndex != ((ImageInt) cTerm2).relationIndex)) {
                return false;
            }
            final Term[] list = cTerm1.cloneTerms();
            if (cTerm1.isCommutative()) {
                CompoundTerm.shuffle(list, rnd);
                //ok attempt unification with all permutations。
                // 尝试与所有排列统一。
                if(list == null || cTerm2.term == null || list.length != cTerm2.term.length) {
                    return false;
                }
                // todo 三个满足两个，也返回false。是否改为返回true
                // 案例：<(&&,<$1 --> [chirping]>,<$1 --> flyer>,<$1 --> ani>) ==> <$1 --> bird>>.
                // 输入<fff --> flyer>。得到<(&&,<fff --> [chirping]>,<fff --> flyer>,<fff --> ani>) ==> <fff --> bird>>.
                // 不是直接输出<fff --> bird>，因为推出的还是条件，假如满足，才最终输出<fff --> bird>。
                final Set<Integer> matchedJ = new LinkedHashSet<>(list.length * 2);
                for(int i = 0; i < list.length; i++) {
                    boolean succeeded = false;
                    for(int j = 0; j < list.length; j++) {
                        if(matchedJ.contains(j)) {
                            // this one already was used to match one of the i's
                            // 这个已经被用来匹配一个i
                            continue;
                        }
                        //clone map also:
                        final Term ti = list[i].clone();
                        if(map[0] == null) {
                            map[0] = new LinkedHashMap<>();
                        }
                        if(map[1] == null) {
                            map[1] = new LinkedHashMap<>();
                        }
                        final Map<Term, Term>[] mapNew = copyMapFrom(map);
                        //attempt unification: 尝试统一
                        if(findSubstitute(rnd, type,ti,cTerm2.term[j],mapNew)) {
                            appendToMap(mapNew[0], map[0]);
                            appendToMap(mapNew[1], map[1]);
                            succeeded = true;
                            matchedJ.add(j);
                            break;
                        }
                    }
                    if(!succeeded) {return false;}
                }
                return true;
            }
            for (int i = 0; i < cTerm1.size(); i++) {
                final Term t1 = list[i];
                final Term t2 = cTerm2.term[i];
                if (!findSubstitute(rnd, type, t1, t2, map)) {
                    return false;
                }
            }
            return true;
        }
    }

    /**
     * copies two maps from source into two new maps
     * 复制source 中的两个映射到两个新映射中，复制避免引用修改原始值
     * @param source source maps (two)
     * @return copied maps
     */
    private static Map<Term, Term>[] copyMapFrom(Map<Term, Term>[] source) {
        final Map<Term, Term>[] destination = (Map<Term, Term>[]) new LinkedHashMap<?,?>[2];

        destination[0] = new LinkedHashMap<>();
        destination[1] = new LinkedHashMap<>();

        appendToMap(source[0], destination[0]);
        appendToMap(source[1], destination[1]);
        return destination;
    }

    public static void appendToMap(Map<Term, Term> source, Map<Term, Term> target) {
        for(final Term c : source.keySet()) {
            target.put(c, source.get(c));
        }
    }

    /**
     * Check whether a string represent a name of a term that contains a variable
     * 检查字符串是否表示包含变量的术语的名称
     * @param n The string name to be checked
     * @return Whether the name contains a variable
     */
    public static boolean containVar(final CharSequence n) {
        if (n == null) return false;
        final int l = n.length();
        for (int i = 0; i < l; i++) {
            switch (n.charAt(i)) {                
                case Symbols.VAR_INDEPENDENT:
                case Symbols.VAR_DEPENDENT:
                case Symbols.VAR_QUERY:
                    // 如果前一个字符不是逗号或左括号、左大括号、左中括号，则不是变量。如双引号或下划线等
                    if (i > 0 && n.charAt(i - 1) != Symbols.ARGUMENT_SEPARATOR
                            && n.charAt(i - 1) != Symbols.STAMP_OPENER
                            && n.charAt(i - 1) != Symbols.COMPOUND_OPENER
                            && n.charAt(i - 1) != Symbols.SET_INT_OPENER) {
                        return false;
                    }
                    return true;
            }
        }        
        return false;
    }
    
    public static final boolean containVar(final Term[] t) {
        for (final Term x : t)
            if (x instanceof Variable)
                return true;
        return false;
    }

    /**
     * To unify two terms
     *
     * @param type The type of variable that can be substituted
     * @param t The first and second term as an array, which will have been modified upon returning true
     *          第一个和第二个术语作为一个数组，这将在返回true时被修改
     * @return Whether the unification is possible.  't' will refer to the unified terms
     */
    public static boolean unify(Random rnd, final char type, final Term[] t) {
        return unify(rnd, type, t[0], t[1], t);
    }

    /**
     * To unify two terms
     * 统一两个术语。t1和t2是单句，compound里可以是复合句，也就是t1的母句
     * todo nars没有两个及以上前提同时满足的统一
     * @param type The type of variable that can be substituted
     * @param t1 The compound containing the first term, possibly modified
     * @param t2 The compound containing the second term, possibly modified
     * @param compound The first and second term as an array, which will have been modified upon returning true
     * @return Whether the unification is possible.  't' will refer to the unified terms
     */
    public static boolean unify(Random rnd, final char type, final Term t1, final Term t2, final Term[] compound) { 
        return unify(rnd, type, t1, t2, compound, false);
    }
    public static boolean unify(Random rnd, final char type, final Term t1, final Term t2, final Term[] compound, final boolean allowPartial) {
        final Map<Term, Term> map[] = new Map[2]; //begins empty: null,null
        // 匹配并有可替代
        final boolean hasSubs = findSubstitute(rnd, type, t1, t2, map, allowPartial);
        if (hasSubs) {
            final Term a = (compound[0] instanceof Variable && map[0].containsKey(compound[0])) ? 
                            map[0].get(compound[0]) : 
                            applySubstituteAndRenameVariables(((CompoundTerm)compound[0]), map[0]);
            if (a == null) return false;
            final Term b = (compound[1] instanceof Variable && map[1].containsKey(compound[1])) ? 
                            map[1].get(compound[1]) :
                            applySubstituteAndRenameVariables(((CompoundTerm)compound[1]), map[1]);
            if (b == null) return false;
            //only set the values if it will return true, otherwise if it returns false the callee can expect its original values untouched
            //只有在返回true时才设置值，否则如果返回false，调用者可以期望其原始值不变
            if(compound[0] instanceof Variable && compound[0].hasVarQuery() && (a.hasVarIndep() || a.hasVarIndep()) ) {
                return false;
            }
            if(compound[1] instanceof Variable && compound[1].hasVarQuery() && (b.hasVarIndep() || b.hasVarIndep()) ) {
                return false;
            }
            compound[0] = a;
            compound[1] = b;
            return true;
        }
        return false;
    }

    /** appliesSubstitute and renameVariables, resulting in a cloned object, 
     *  will not change this instance
     *  应用替换和重命名变量，导致克隆对象，不会更改此实例
     *  */
    public static Term applySubstituteAndRenameVariables(final CompoundTerm t, final Map<Term, Term> subs) {
        if ((subs == null) || (subs.isEmpty())) {
            //no change needed
            return t;
        }
        final Term r = t.applySubstitute(subs);
        if (r == null) return null;
        if (r.equals(t)) return t;
        return r;
    }

    public static Variable makeCommonVariable(final Term v1, final Term v2) {
        // v2 first since when type does not match
        // but it is an allowed rename like $1 -> #1 then the second type should be used
        // v2首先，因为当类型不匹配时，但它是一个允许的重命名，如$1 -> #1，那么第二个类型应该被使用
        //TODO use more efficient string construction
        return new Variable(v2.toString() + v1.toString() + '$');
    }
    
    /**
     * examines whether a term is using an independent variable in an invalid way
     * 检查术语是否以无效的方式使用独立变量
     * @param T term to be examined
     * @return Whether the term contains an independent variable
     */
    public static boolean indepVarUsedInvalid(final Term T) {
        //  if its a conjunction/disjunction, this is invalid: (&&,<$1 --> test>,<$1 --> test2>),
        //  while this isnt: (&&,<$1 --> test ==> <$1 --> test2>,others)
        //  this means we have to go through the conjunction, and check if the component is a indepVarUsedInvalid instance, if yes, return true
        //  如果它是一个合取/析取，这是无效的：（&&，<$1 --> test>，<$1 --> test2>），
        //  而这不是：（&&，<$1 --> test ==> <$1 --> test2>，others）
        //  这意味着我们必须通过合取，并检查组件是否是一个indepVarUsedInvalid实例，如果是，返回true
        if(T instanceof Conjunction || T instanceof Disjunction) {
            final Term[] part=((CompoundTerm)T).term;
            for(final Term t : part) {
                if(indepVarUsedInvalid(t)) {
                    return true;
                }
            }
        }
        if(!(T instanceof Inheritance) && !(T instanceof Similarity)) {
            return false;
        }
        return T.hasVarIndep();
    }

    /**
     * Check if two terms can be unified
     * 检查两个术语是否可以统一
     * @param type The type of variable that can be substituted
     * @param term1 The first term to be unified
     * @param term2 The second term to be unified
     * @return Whether there is a substitution
     */
    public static boolean hasSubstitute(Random rnd, final char type, final Term term1, final Term term2) {
        return findSubstitute(rnd, type, term1, term2, new LinkedHashMap<>(), new LinkedHashMap<>());
    }
    
}
