/*******************************************************************************
 * Copyright (c) 2009, 2011 The University of Memphis.  All rights reserved. 
 * This program and the accompanying materials are made available 
 * under the terms of the LIDA Software Framework Non-Commercial License v1.0 
 * which accompanies this distribution, and is available at
 * http://ccrg.cs.memphis.edu/assets/papers/2010/LIDA-framework-non-commercial-v1.0.pdf
 *******************************************************************************/
package edu.memphis.ccrg.lida.proceduralmemory;

import edu.memphis.ccrg.lida.actionselection.Action;
import edu.memphis.ccrg.lida.actionselection.Behavior;
import edu.memphis.ccrg.lida.framework.FrameworkModule;
import edu.memphis.ccrg.lida.framework.FrameworkModuleImpl;
import edu.memphis.ccrg.lida.framework.ModuleListener;
import edu.memphis.ccrg.lida.framework.ModuleName;
import edu.memphis.ccrg.lida.framework.initialization.AgentStarter;
import edu.memphis.ccrg.lida.framework.initialization.Initializable;
import edu.memphis.ccrg.lida.framework.shared.*;
import edu.memphis.ccrg.lida.framework.strategies.DecayStrategy;
import edu.memphis.ccrg.lida.framework.tasks.TaskManager;
import edu.memphis.ccrg.lida.globalworkspace.BroadcastListener;
import edu.memphis.ccrg.lida.globalworkspace.Coalition;
import edu.memphis.ccrg.lida.workspace.Workspace;
import edu.memphis.ccrg.lida.workspace.workspacebuffers.WorkspaceBuffer;
import edu.memphis.ccrg.linars.Term;
import org.opennars.entity.*;
import org.opennars.language.Inheritance;
import org.opennars.language.Product;
import org.opennars.language.SetExt;
import org.opennars.language.Tense;
import org.opennars.main.Nar;
import org.opennars.operator.Operation;

import java.util.*;
import java.util.concurrent.ConcurrentHashMap;
import java.util.logging.Level;
import java.util.logging.Logger;

/**
 * Default implementation of {@link ProceduralMemory}. Indexes scheme by context
 * elements for quick access. Assumes that the {@link Condition} of {@link Scheme} are {@link Node} only.
 * 默认实现。通过上下文*元素建立索引方案，以便快速访问。假设{@link Scheme}的{@link Condition}仅是{@link Node}
 * <AUTHOR> J. McCall
 * <AUTHOR> Snaider
 */
public class ProceduralMemoryImpl extends FrameworkModuleImpl implements
		ProceduralMemory, BroadcastListener {
	private static final Logger logger = Logger.getLogger(ProceduralMemoryImpl.class.getCanonicalName());
	private static final ElementFactory factory = ElementFactory.getInstance();// 元素工厂，用于创建元素

	/**
	 * The possible type of usage for a condition inside a {@link Scheme}
	 *{@link Scheme}中某个条件可能的用法类型
	 */
	public enum ConditionType{
		/**
		 * A {@link Condition} that is part of a scheme's context.
		 */
		CONTEXT, 
		/**
		 * A {@link Condition} that is part of a scheme's adding list.
		 */
		ADDINGLIST,
		/**
		 * A {@link Condition} that is part of a scheme's deleting list.
		 * Not yet supported.
		 */
		DELETINGLIST,
		/**
		 * A {@link Condition} that is part of a scheme's negated context.
		 * Not yet supported.
		 */
		NEGATEDCONTEXT
	};
	/*	节点在其上下文中索引的方案
	 * Schemes indexed by Nodes in their context.
	 */
	protected Map<Object, Set<Scheme>> contextSchemeMap = new ConcurrentHashMap<Object, Set<Scheme>>();

	/*
	 * Schemes indexed by Nodes in their adding list.
	 * 节点在其添加列表中索引的方案
	 */
	protected Map<Object, Set<Scheme>> addingSchemeMap = new ConcurrentHashMap<Object, Set<Scheme>>();

	/*
	 * Set of all schemes current in the module. Convenient for decaying the schemes' base-level activation.
	 * 模块中当前所有方案的集合。方便衰减方案的基础级别激活
	 */
	private Set<Scheme> schemeSet = Collections.newSetFromMap(new ConcurrentHashMap<Scheme,Boolean>());

	/**
	 * A pool of all conditions (context and adding) in all schemes in the procedural memory
	 * 程序存储器中所有方案中所有条件（上下文和相加）的池
	 */
	protected Map<Object, Condition> conditionPool = new HashMap<Object, Condition>();

	/**
	 * Recent contents of consciousness that have not yet decayed away.
	 * 意识的最新内容尚未消失
	 */
	protected InternalNodeStructure broadcastBuffer = new InternalNodeStructure("PamNodeImpl","PamLinkImpl");

//	protected InternalNodeStructure goalBuffer = new InternalNodeStructure("PamNodeImpl","PamLinkImpl");
//
//	protected InternalNodeStructure actBuffer = new InternalNodeStructure("PamNodeImpl","PamLinkImpl");
//
//	protected InternalNodeStructure planBuffer = new InternalNodeStructure("PamNodeImpl","PamLinkImpl");

	/**
	 * Allows Nodes to be added without copying. 
	 * Warning: doing so allows the same java object of Node to exist in multiple places.
	 * 允许添加节点而不进行复制。 *警告：这样做允许Node的相同java对象在多个位置存在
	 * <AUTHOR> J. McCall
	 * @see NodeStructureImpl
	 */
	protected class InternalNodeStructure extends NodeStructureImpl {

		public InternalNodeStructure(String nodeType, String linkType) {
			super(nodeType, linkType);
		}

		@Override
		public Node addNode(Node n, boolean copy) {
			return super.addNode(n, copy);
		}
	}

	/*
	 * Listeners of this Procedural Memory
	 */
	private List<ProceduralMemoryListener> proceduralMemoryListeners = new ArrayList<ProceduralMemoryListener>();

	private static final double DEFAULT_SCHEME_SELECTION_THRESHOLD = 0.1;	
	/*
	 * Determines how much activation a scheme should have to be instantiated
	 * 确定方案必须实例化多少激活
	 */
	private double schemeSelectionThreshold = DEFAULT_SCHEME_SELECTION_THRESHOLD;
	
	private static final double DEFAULT_CONDITION_WEIGHT = 1.0;//for Javier
	
	private static final String DEFAULT_SCHEME_CLASS = "edu.memphis.ccrg.lida.proceduralmemory.SchemeImpl";
	/**
	 * Qualified name of the {@link Scheme} class used by this module 
	 */
	public static String schemeClass = DEFAULT_SCHEME_CLASS;
	/**
	 * DecayStrategy used by all conditions in the condition pool (and broadcast buffer).
	 * 条件池（和广播缓冲区）中所有条件使用的DecayStrategy
	 */
	private DecayStrategy conditionDecay;
	
	/**
	 * This module can initialize the following parameters:<br><br/>
	 * 
	 * <b>proceduralMemory.schemeSelectionThreshold type=double</b> amount of activation schemes must have to be instantiated, default is 0.0<br/>
	 * <b>proceduralMemory.contextWeight type=double</b> The weight of context conditions for the calculation of scheme activation. Should be positive<br/>
	 * <b>proceduralMemory.addingListWeight type=double</b> The weight of adding list conditions for the calculation of scheme activation. Should be positive<br/>
	 * <b>proceduralMemory.conditionDecayStrategy type=string</b> The DecayStrategy used by all conditions in the condition pool (and broadcast buffer).<br/> 
	 * <b>proceduralMemory.schemeClass type=string</b> qualified name of the {@link Scheme} class used by this module <br/>
	 *
	 * 该模块可以初始化以下参数：proceduralMemory.schemeSelectionThreshold type = double
	 * 必须实例化激活方案的数量，默认值为0.0
	 * proceduralMemory.contextWeight type = double
	 * 用于方案激活计算的上下文条件权重。应该为正数<br/>
	 * proceduralMemory.addingListWeight type = double
	 * 为计划激活计算添加列表条件的权重。应该为正数。
	 * proceduralMemory.conditionDecayStrategy type = string
	 * 条件池（和广播缓冲区）中所有条件使用的DecayStrategy。
	 * proceduralMemory.schemeClass type =字符串
	 * 此模块使用的{@link Scheme}类的限定名称<br/>
	 * 
	 * @see Initializable
	 */
	@Override
	public void init() {
		schemeSelectionThreshold = getParam(
				"proceduralMemory.schemeSelectionThreshold",
				DEFAULT_SCHEME_SELECTION_THRESHOLD);
		SchemeImpl.setContextWeight(getParam("proceduralMemory.contextWeight",
				DEFAULT_CONDITION_WEIGHT));
		SchemeImpl.setAddingListWeight(getParam(
				"proceduralMemory.addingListWeight", DEFAULT_CONDITION_WEIGHT));
		String decayName = getParam("proceduralMemory.conditionDecayStrategy",
				factory.getDefaultDecayType());
		conditionDecay = factory.getDecayStrategy(decayName);
		schemeClass = getParam("proceduralMemory.schemeClass",
				DEFAULT_SCHEME_CLASS);
	}

	public WorkspaceBuffer sceneGraph;
	public WorkspaceBuffer seqGraph;
	public WorkspaceBuffer goalGraph;
	public WorkspaceBuffer wordGraph;

	public WorkspaceBuffer nonGraph;


	@Override
	public void setAssociatedModule(FrameworkModule m, String usage) {
		if(m instanceof Workspace){
			// 初始化工作记忆，即原nars的memory，全局统一
//			AgentStarter.nar.memory = (Memory) ((WorkspaceBuffer) m.getSubmodule(ModuleName.narmemory)).getBufferContent(null);

			nonGraph = (WorkspaceBuffer) m.getSubmodule(ModuleName.NonGraph);
			seqGraph = (WorkspaceBuffer) m.getSubmodule(ModuleName.SeqGraph);
			goalGraph = (WorkspaceBuffer) m.getSubmodule(ModuleName.GoalGraph);
			wordGraph = (WorkspaceBuffer) m.getSubmodule(ModuleName.WordGraph);

		}else{
			logger.log(Level.WARNING, "Cannot add module {1}",
					new Object[]{TaskManager.getCurrentTick(),m});
		}
	}

	@Override
	public void addListener(ModuleListener l) {
		if (l instanceof ProceduralMemoryListener) {
			proceduralMemoryListeners.add((ProceduralMemoryListener) l);
		}else{
			logger.log(Level.WARNING, "Requires ProceduralMemoryListener but received {1}", 
					new Object[]{TaskManager.getCurrentTick(), l});
		}
	}
	
	@Override
	public Scheme getNewScheme(Action a){
		if(a == null){
			logger.log(Level.WARNING, "Action is null, cannot create scheme.",
					TaskManager.getCurrentTick());
			return null;
		}
		SchemeImpl s = DoGetScheme();

		s.setAction(a);

		schemeSet.add(s);
		return s;
	}

	private SchemeImpl DoGetScheme() {
		SchemeImpl s = null;
		try {
			s = (SchemeImpl) Class.forName(schemeClass).newInstance();
			s.setProceduralMemory(this);
		} catch (InstantiationException e) {
			logger.log(Level.WARNING, "Error creating Scheme.", TaskManager
					.getCurrentTick());
		} catch (IllegalAccessException e) {
			logger.log(Level.WARNING, "Error creating Scheme.", TaskManager
					.getCurrentTick());
		} catch (ClassNotFoundException e) {
			logger.log(Level.WARNING, "Error creating Scheme.", TaskManager
					.getCurrentTick());
		}
		return s;
	}

	public Scheme getNewScheme(){
		SchemeImpl s = DoGetScheme();
		return s;
	}

	/**
	 * Add {@link Condition} c to the condition pool if it is not already stored. </br>
	 * Returns the stored condition with same id as c.</br>
	 * This method is intended to be used only by {@link SchemeImpl} to ensure that all schemes 
	 * in the {@link ProceduralMemory} share the same condition instances.
	 * @param c the condition to add to the condition pool.
	 * @return c or the stored condition with same id as c
	 * 将{@link Condition} c添加到条件池（如果尚未存储）。
	 * 返回具有与c相同的ID的存储条件。此方法仅由{@link SchemeImpl}使用，以确保{@link ProceduralMemory}中的所有方案*共享相同的条件实例。
	 * param c将条件添加到条件池。 * @return c或与c相同ID的存储条件
	 */
	public Condition addCondition(Condition c){
		if(c == null){
			logger.log(Level.WARNING, "Cannot add null condition", TaskManager.getCurrentTick());
			return null;
		}
		Condition stored = conditionPool.get(c.getConditionId());
		if(stored == null){
			logger.log(Level.FINEST,"New Condition {1} added to condition pool.",
					new Object[]{TaskManager.getCurrentTick(), c});
			c.setDecayStrategy(conditionDecay);
			conditionPool.put(c.getConditionId(),c);
			stored = c;
		}
		return stored;
	}
	
	/**
	 * Add a reference to specified Scheme from specified condition. Thus the presence of Condition c
	 * in the broadcast buffer will tend to activate s. 
	 * The specified scheme should be one that will have c in its conditions (e.g. in the Context or adding list)
	 * Indexes Scheme s by Condition c of ConditionType type.
	 * @param s the {@link Scheme} to index
	 * @param c the condition 
	 * @param type the type of the condition. This select the indexing map
	 */
	void indexScheme(Scheme s, Condition c, ConditionType type) {
		Map<Object, Set<Scheme>> map = null;
		switch(type){
			case CONTEXT:
				map = contextSchemeMap;
				break;
			case ADDINGLIST:
				map = addingSchemeMap;
				break;
			case DELETINGLIST:
				break;
			case NEGATEDCONTEXT:
				break;
		}
		if (map !=null){
			synchronized (c) {
				Object id = c.getConditionId();
				Set<Scheme> values = map.get(id);
				if (values == null) {
					values = Collections.newSetFromMap(new ConcurrentHashMap<Scheme,Boolean>());
					map.put(id, values);
				}
				values.add(s);
			}
		}
	}
	
	/*
	 * Assumes Conditions are Nodes only 
	 */
	@Override
	public void receiveBroadcast(Coalition coalition) throws Exception {
		NodeStructure ns = (NodeStructure) coalition.getContent();
		if (ns != null) {
			return;
		}
		for (Node broadcastNode: ns.getNodes()) {
			// For each broadcast node, check if it is in the condition pool,
			// i.e., there is at least 1 scheme that has context or result
			// condition equal to the node.
			// todo 情景数据--事理--行为，广播后整合现实场景（直接来自pam？），预测动作
//			Node condition = (Node) conditionPool.get(broadcastNode.getConditionId());

//			SchemeImpl sch = (SchemeImpl) Class.forName(schemeClass).newInstance();
			Set<Scheme> schemes = ns.getActions(broadcastNode, this);

//			Thread t = Thread.currentThread();
//			System.out.println("receiveBroadcast 进入线程-------》" + t.getName());
//			System.out.println("schemes.size :" + schemes.size());

			if (schemes != null && schemes.size() != 0) {

				contextSchemeMap.put(broadcastNode.getNodeId(),schemes);

				//Add node to broadcast buffer only if already in the condition pool
				if (!broadcastBuffer.containsNode(broadcastNode)) {
					// Add a reference to the condition pool Node to the
					// broadcast buffer without copying
					broadcastBuffer.addNode(broadcastNode, false);
					broadcastBuffer.setBroadSceneCount(ns.getBroadSceneCount());
				}
				// Update the activation of the condition-pool/broadcast-buffer node if needed
				if (broadcastNode.getActivation() > broadcastNode.getActivation()) {
					broadcastNode.setActivation(broadcastNode.getActivation());
				}
				// Update the desirability of the condition-pool/broadcast-buffer node if needed
				if(broadcastNode.getIncentiveSalience() > broadcastNode.getIncentiveSalience()){
					broadcastNode.setIncentiveSalience(broadcastNode.getIncentiveSalience());
				}
			}
		}
		// 注意 coalition 与 condition
		learn(coalition);

		// Spawn a new task to activate and instantiate relevant schemes.
		// This task runs only once in the next tick
		// todo 将节点与相关信息拆分的好处：先判断节点重要性？但判断需要背后的信息
		// 不判断buffer 是确信不为空？
//		taskSpawner.addTask(new FrameworkTaskImpl() {
//			@Override
//			protected void runThisFrameworkTask() {
//				activateSchemes();
//				cancel();
//			}
//		});

		// 尝试直接执行，不用新task
		activateSchemes();
	}
	
	@Override
	public void learn(Coalition coalition) {
		//TODO implement learning 学习程序性记忆
		// make sure to use the correct way of adding new schemes see addScheme
	}

	public static int k = 0;

	private Map actionMap;

	@Override
	public void activateSchemes() {
		// To prevent a scheme from being instantiated multiple times all
		// schemes over threshold are stored in a set
		Set<Scheme> relevantSchemes = new HashSet<Scheme>();

//		Thread t = Thread.currentThread();
//		System.out.println("activateSchemes 进入线程-------》" + t.getName());
//		System.out.println("broadcastBuffer.size :" + broadcastBuffer.getNodes().size());


		actionMap = new HashMap();
		for (Node n: broadcastBuffer.getNodes()) {
			// Get all schemes that contain Node n in their context and add them to relevantSchemes
			// todo 行为图数据，整合buffertask？不需要将pool和buffer分开？
			Set<Scheme> schemes = contextSchemeMap.get(n.getNodeId());
			if (schemes != null) {
				relevantSchemes.addAll(schemes);

				actionMap.put(n.getTNname() + "-" + n.getLocation(),schemes);

//				for(Scheme scheme:schemes){
//					actionMap.put(n.getName(),scheme.getAction().getName());
//				}
			}
			// If Node n has positive desirability,
			// get the schemes that have n in their adding list and add them to relevantSchemes
			if (n.getIncentiveSalience() > 0.0) {	// 激励显著性 goodhealth、foodOrigin
				schemes = addingSchemeMap.get(n.getConditionId());
				if (schemes != null) {
					relevantSchemes.addAll(schemes);
				}
			}//TODO Case is incentive salience is negative?
		}

		// 是否有猴子或食物，排他
		for (Object node : actionMap.keySet()){
			String[] strings = node.toString().split("-");

			if (!strings[0].equals("emptyFront") && !strings[0].equals("goodHealth")
					&& !strings[0].equals("fairHealth") && !strings[0].equals("badHealth") /*&& k % 2 == 0*/) {

				docycles(strings[1], new Term(strings[0]));
				doaction(new Term(strings[0]), (Set<Scheme>) actionMap.get(node));

			}else if(k % 2 == 0){
				docycles(strings[1], new Term(strings[0]));
				doaction(new Term(strings[0]), (Set<Scheme>) actionMap.get(node));
			}else {
				// 空格
			}

//			if (strings[0].equals("foodOrigin")) {
//				docycles(n.getLocation(), (Term) n);
//				doaction((Term) n, schemes);
//			}else if (strings[0].equals("foodOrigin")) {
//			}else {
//			}

		}

		k ++ ;
		if (k % 2 == 0) {
			if (k % 4 == 0) {
//				nar.addInput("<{SELF} --> [cango]>! :|:");
				nar.addInput("<{SELF} --> [morehealthy]>! :|:");
			}else {
				nar.addInput("<{SELF} --> [satisfied]>! :|:");
			}
		}
		
		nar.cycles(10);

		// nars那边还没做出决策
		// || s.getAction().getName().equals("eat") 间隔十几
//		if (AgentStarter.actionId == 0 && k % 15 == 0 || k < 30) {
//			//TODO consider links too
//			// For each relevant scheme, if it should be instantiated, then instantiate
			// 对于每个相关的方案，如果应该实例化，则实例化
//			for (Scheme s: relevantSchemes) {
//
//				if (shouldInstantiate(s, broadcastBuffer)) {
//					createInstantiation(s);
//				}
//			}
////			doAct(s);
//		}
		
		AgentStarter.actionId = 0;

		// 无论有没有行为，都执行周期。同一个nar

	}

	Nar nar = AgentStarter.nar;
	
	public static LinkedList tasklist = new LinkedList();

	private void docycles(String site, Term linkable) {

		String name = (String) linkable.name();

		Term term = new Product(new Term[]{new SetExt(linkable), new Term(site)});
		Term term0;
		if (!name.equals("goodHealth") && !name.equals("fairHealth") && !name.equals("badHealth") && !name.equals("emptyFront")) {
			term0 = new Inheritance(new Term[] {term, new Term("at")});
		}else if (name.equals("badHealth") && k % 19 == 0) {
			term0 = new Inheritance(new Term[] {linkable, new Term("on")});
		}else if(k % 5 == 0){
			if (name.equals("emptyFront")) {
				term0 = new Inheritance(new Term[] {term, new Term("at")});
			}else {
				term0 = new Inheritance(new Term[] {linkable, new Term("on")});
			}
		}else {
			return;
		}
		
		dotask(linkable, term0);
	}

	private void dotask(Term linkable, Term term0) {
		final Stamp stamp = new Stamp(-1 , Tense.Present, nar.memory.newStampSerial(),
				nar.memory.narParameters.DURATION);

		final Sentence sentence = new Sentence(
				term0,
				'.',
				new TruthValue((float) 1.0, 0.9, nar.memory.narParameters),
				stamp);

		final BudgetValue budget = new BudgetValue(0.8f, 0.5f, 1, nar.memory.narParameters);


		// 暂时只是输入任务，在动作选择时再执行？多线程问题
		// 广播之后。内部操作周期内完成，需要广播的阶段性操作广播后执行
		nar.memory.inputTask(AgentStarter.nar, new Task(sentence, budget, Task.EnumType.INPUT));

//		nar.cycles(5);
		
	}

	private void doaction(Term linkable, Set<Scheme> schemes) {
		String nal;
		String budget;
		String action;
		switch ((String)linkable.name()){
			case "rockFront":
//				nar.addInput("<{SELF} --> [cango]>. :|: %0%");
				nal = "<{SELF} --> [satisfied]>";
				budget = "$0.80;0.79;0.79$";
				multiAction(linkable, schemes, nal, budget);
				break;
			case "treeOrigin":
//				nar.addInput("<{SELF} --> [cango]>. :|: %0%");
				nal = "<{SELF} --> [satisfied]>";
				budget = "$0.80;0.79;0.79$";
				multiAction(linkable, schemes, nal, budget);
				break;
			case "predatorOrigin":
				nar.addInput("<{SELF} --> [morehealthy]>. :|: %0%");
				nar.addInput("<{SELF} --> [satisfied]>. :|: %0%");
				nal = "<{SELF} --> [morehealthy]>";
				budget = "$1.0;0.99;0.99$";
				multiAction(linkable, schemes, nal, budget);
				break;
			case "predatorFront":
				nar.addInput("<{SELF} --> [satisfied]>. :|: %0%");
				nal = "<{SELF} --> [satisfied]>";
//				multiAction(linkable, schemes, nal);
				budget = "$0.91;0.95;0.95$";

				Random r = new Random();
				List<Scheme> list = new ArrayList<>(schemes);
				int lastAction = r.nextInt(schemes.size());
				for (int i = 0; i < schemes.size(); i++) {
					if (i == lastAction) {
						action = list.get(i).getAction().getName();
						nar.addInput(budget + "<(&/,<(*,{predatorFront},#2) --> at>,+12,(^" + action + ",{SELF}),+12,(^moveAgent,{SELF}),+12) =/> " + nal + ">.");
//						nar.addInput(budget + "<(&/,<(*,{predatorFront},#2) --> at>,+12,(^" + action + ",{SELF}),+100) =/> " + nal + ">.");
					}
				}
				break;
			case "outOfBounds":
//				nar.addInput("<{SELF} --> [cango]>. :|: %0%");
				nal = "<{SELF} --> [satisfied]>";
				budget = "$0.80;0.79;0.79$";
				multiAction(linkable, schemes, nal, budget);
				break;
			case "treeFront":
//				nar.addInput("<{SELF} --> [cango]>. :|: %0%");
				nal = "<{SELF} --> [satisfied]>";
				budget = "$0.80;0.79;0.79$";
				multiAction(linkable, schemes, nal, budget);
				break;
			case "foodFront":
				nar.addInput("<{SELF} --> [morehealthy]>! :|:");
				budget = "$0.95;0.95;0.95$";
				nal = "<{SELF} --> [morehealthy]>";
//				nal = "<{SELF} --> [satisfied]>";
//				nar.addInput(budget + "<(&/,<(*,{foodFront},#2) --> at>,+12,(^moveAgent,{SELF}),+100) =/> <(*,{foodOrigin},#1) --> at>>.");
//				multiAction(linkable, schemes, nal, budget);
				for (Scheme scheme: schemes){
					action = scheme.getAction().getName();
					nar.addInput(budget + "<(&/,<(*,{foodFront},$1) --> at>,+12,(^moveAgent,{SELF}),+12," +
							"<(*,{foodOrigin},$2) --> at>,+12,(^eat,{SELF}),+100) =/> " + nal + ">.");
				}
				break;
//			case "foodOrigin":
////				nar.addInput("<{SELF} --> [morehealthy]>. :|:");
//				nal = "<{SELF} --> [morehealthy]>";
//				budget = "$1.0;0.99;0.99$";
//				multiAction(linkable, schemes, nal, budget);
//				break;
			default:
				if (k % 3 == 0 && !linkable.name().equals("foodOrigin")) {
					nal = "<{SELF} --> [satisfied]>";
					budget = "$0.70;0.69;0.79$";
					multiAction(linkable, schemes, nal, budget);
				}
//					nar.addInput("<{SELF} --> [safety]>! :|:");
				// emptyfront就先别管
				break;
		}

//		nar.cycles(5);
	}

	private void multiAction(Term linkable, Set<Scheme> schemes, String nal, String budget) {
		String action;
		String name = "emptyFront";
		Random r = new Random();
		List<Scheme> list = new ArrayList<>(schemes);
		int lastAction = r.nextInt(schemes.size());
		for (int i = 0; i < schemes.size(); i++) {
			if (i == lastAction || name.equals("foodFront") || name.equals("foodOrigin")) {
				action = list.get(i).getAction().getName();
				name = (String) linkable.name();
				nar.addInput(budget + "<(&/,<(*,{" + name + "},#2) --> at>,+12,(^" + action + ",{SELF}),+100) =/> " + nal + ">.");
			}
		}
//		for (Scheme scheme: schemes){
//			action = scheme.getAction().getName();
//			name = (String) linkable.name();
//			nar.addInput("<(&/,<(*,{" + name + "},#2) --> at>,+12,(^" + action + ",{SELF}),+100) =/> " + nal + ">.");
//		}
	}

	private void doAct(Scheme s) {
		final Stamp stamp = new Stamp(-1 , Tense.Present, nar.memory.newStampSerial(),
				nar.memory.narParameters.DURATION);

		// 纯目标版本
		Term term0 = new Operation(new Term[] {new Product(new SetExt(new Term("SELF"))),
				nar.memory.getOperator("^" + s.getAction().getName())});
		char punc = '!';

		// 陈述版本
//			Term term = new Product(nar.memory.getOperator("^" + s.getAction().getName()),new SetExt(new Term("SELF")));
//			Term term1 =new Inheritance(new Term[]{new Product(new SetExt(new Term("SELF"))),new SetInt(new Term("healthy"))});
//			char punc = '.';
//			Term term0 = new Implication(term,term1,1);

		final Sentence sentence = new Sentence(
				term0, punc,
				new TruthValue((float) 1.0, 0.9, nar.memory.narParameters),
				stamp);

		final BudgetValue budget = new BudgetValue(0.8f, 0.5f, 1,
				nar.memory.narParameters);

		nar.memory.inputTask(AgentStarter.nar,
				new Task(sentence, budget, Task.EnumType.INPUT));
	}

	/**
	 * Returns true if the specified scheme's total activation is greater than
	 * the scheme selection threshold. </br>The threshold can be set in the
	 * {@link #init()} method.
	 * @see SchemeImpl#getActivation()
	 */
	@Override
	public boolean shouldInstantiate(Scheme s, NodeStructure broadcastBuffer){
		return s.getTotalActivation() >= schemeSelectionThreshold;
	}

	@Override
	public Behavior createInstantiation(Scheme s) {
		logger.log(Level.FINE, "Instantiating scheme: {1} in ProceduralMemory",
				new Object[]{TaskManager.getCurrentTick(),s});
		Behavior b = factory.getBehavior(s);
		b.setBroadcostId(broadcastBuffer.getBroadSceneCount());
		for (ProceduralMemoryListener listener : proceduralMemoryListeners) {
			listener.receiveBehavior(b);
		}
		return b;
	}

	@Override
	public void decayModule(long ticks){
		broadcastBuffer.decayNodeStructure(ticks);
	}
	
	@Override
	public void removeScheme(Scheme s) {
		schemeSet.remove(s);
		removeFromMap(s, s.getContextConditions(), contextSchemeMap);
		removeFromMap(s, s.getAddingList(), addingSchemeMap);
	}

	private static void removeFromMap(Scheme s,
			Collection<Condition> conditions, Map<Object, Set<Scheme>> map) {
		for (Condition c : conditions) {
			Set<Scheme> schemes = map.get(c.getConditionId());
			if(schemes != null){
				schemes.remove(s);
			}
		}
	}

	@Override
	public boolean containsScheme(Scheme s) {
		return schemeSet.contains(s);
	}

	@Override
	public int getSchemeCount() {
		return schemeSet.size();
	}

	@Override
	public Collection<Scheme> getSchemes() {
		return Collections.unmodifiableCollection(schemeSet);
	}	

	@Override
	public Object getModuleContent(Object... params) {
		if("schemes".equals(params[0])){
			return Collections.unmodifiableCollection(schemeSet);
		}
		return null;
	}
	
	/**
	 * Gets the condition pool. Method intended for testing only.
	 * @return an {link UnmodifiableCollection} of the condition in the pool
	 */
	public Collection<Condition> getConditionPool(){
		return Collections.unmodifiableCollection(conditionPool.values());
	}
	
	/**
	 * Gets the broadcast buffer. Method intended for testing only.
	 * @return an {@link NodeStructure} containing recent broadcasts
	 */
	public NodeStructure getBroadcastBuffer(){
		return new UnmodifiableNodeStructureImpl(broadcastBuffer);
	}
}