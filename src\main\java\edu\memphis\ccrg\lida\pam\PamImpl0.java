package edu.memphis.ccrg.lida.pam;

import edu.memphis.ccrg.lida.data.NeoUtil;
import edu.memphis.ccrg.lida.framework.ModuleName;
import edu.memphis.ccrg.lida.framework.initialization.AgentStarter;
import edu.memphis.ccrg.lida.framework.shared.Link;
import edu.memphis.ccrg.lida.framework.shared.LinkCategory;
import edu.memphis.ccrg.lida.framework.shared.Linkable;
import edu.memphis.ccrg.lida.framework.shared.Node;
import edu.memphis.ccrg.lida.framework.tasks.TaskManager;
import edu.memphis.ccrg.lida.nlanguage.GrammarTask;
import edu.memphis.ccrg.lida.pam.tasks.*;
import edu.memphis.ccrg.linars.CompoundTerm;
import edu.memphis.ccrg.linars.Concept;
import edu.memphis.ccrg.linars.Memory;
import edu.memphis.ccrg.linars.Term;
import org.neo4j.graphdb.Relationship;
import org.neo4j.graphdb.Result;
import org.neo4j.graphdb.Transaction;
import org.opennars.entity.Task;
import org.opennars.entity.TermLink;
import org.opennars.io.Parser;
import org.opennars.io.Symbols;
import org.opennars.language.*;
import org.opennars.operator.Operation;
import org.opennars.operator.Operator;

import java.util.*;

import static com.warmer.kgmaker.KgmakerApplication.graphDb;
import static edu.memphis.ccrg.lida.framework.initialization.AgentStarter.narsese;
import static org.opennars.language.Variables.appendToMap;

public class PamImpl0 extends PAMemoryImpl{
    @Override
    public void propagateActivationToParents( Node pn, int deep, String from) {
        String pname = pn.getTNname();
        String sname = "";
        Node sink;
        Node source;
        Set<Link> parentLinkSet;
        deep++;
        int deepThreshold = 6;
        putMap(pn, pname);
        Tense tense = null;
        // 扩散两大关键：模态区分+点边类型
        switch (from) {
            case "listen":
//				deepThreshold = 8;
            case "see":
            case "touch":
            case "smell":
            case "taste":
            case "feel":
            case "get":
                tense = Tense.Present;
                nar.addInput(pname + ". :|:");
                nar.cycles(1);
                break;
            case "pam":
            case "nar":
            case "think":
            default:
                break;
        }

        if (deep > deepThreshold) {
            return;
        }

        parentLinkSet = pamNodeStructure.getConnectedSinks(pn);
        // todo 先加入任务队列，按模态分组，模态内可再按线程、阶段、内容等分类，可并行和注意调控
        for (Link parent : parentLinkSet) {
            sink = (Node) parent.getSink();
            source = parent.getSource();
            sname = sink.getTNname();
            // 避免循环激活，todo 非循环环状激活避免
            if (pn.getFromnodeid() == sink.getNodeId()) {continue;}
            putMap(sink, sname);
            String linkType = parent.getCategory().getName();

            // Calculate the amount to propagate // 计算传播量=传递值=权重*激活来源
            propagateParams.put("upscale", upscaleFactor);
            propagateParams.put("totalActivation", pn.getTotalActivation());
            double amountToPropagate = propagationStrategy.getActivationToPropagate(propagateParams);

            // 重设fromnodeid，避免循环激活
            parent.getSource().setFromnodeid(pn.getFromnodeid());

			if ((sname.contains("回应") || sname.contains("听到") || sname.contains("听到1")) && sname.contains("happy")) {
				System.out.println("----听到----命令 or 问题---" + parent.getCategory().getName() + " -- " + sname);
			}

            // 加入csm看是否报告和意象，目前可加入做可视化参考，动机子图由narsese输入？
            pamListeners.get(0).receivePercept(pn, ModuleName.CurrentSM);
            pamListeners.get(0).receivePercept(sink, ModuleName.CurrentSM);
            pamListeners.get(0).receivePercept(parent, ModuleName.CurrentSM);

            // 任何点边都加入nar的mem=全局无意识，利于wm和pam、nars推理
            nar.memory.addDefaultNode(pn);
            nar.memory.addDefaultNode(sink);
            nar.memory.addDefaultLink(parent);

            boolean ismatch = false;

            switch (linkType){
                case "相似":
                case "对等":
                case "顺承":
                    ismatch = true;
                    // 动机子图并行，目前仍然加入nar大图，参与推理，但不参与目标处理
                    pamListeners.get(0).receivePercept(pn, ModuleName.GoalGraph);
                    pamListeners.get(0).receivePercept(sink, ModuleName.GoalGraph);
                    pamListeners.get(0).receivePercept(parent, ModuleName.GoalGraph);
                    // 直接处理，构建备选树，以便构建动机链
                    break;
                case "语序":
                case "顺接":
                    ismatch = true;
                    pamListeners.get(0).receivePercept(pn, ModuleName.GrammarGraph);
                    pamListeners.get(0).receivePercept(sink, ModuleName.GrammarGraph);
                    pamListeners.get(0).receivePercept(parent, ModuleName.GrammarGraph);
                    break;
                case "否定":
                    ismatch = true;
                    break;
                case "arg":
                case "arg0":
                case "arg1":
                case "arg2":
                case "arg3":
                case "arg4":
                case "arg5":
                    ismatch = true;
                    // todo 不调用线程，直接整合义法构建过程到pam内？
//                    SemanticAnalyzTask0 semanticAnalyzTask = new SemanticAnalyzTask0(parent,this , parentLinkSet.size());
//                    taskSpawner.addTask(semanticAnalyzTask);
                    break;
                case "feel":
                    ismatch = true;
                    feelAndDo(pn, (PamLinkImpl) parent, sname);
                    break;
                default:
                    break;
            }
            // 如果是不规则类型，如批次—文件夹id-文件id-构式结构等
            if(!ismatch){
                if (sname.contains("_")){
                    String[] strs = sname.split("_");
                    String last = strs[strs.length - 1];
                    String last0 = strs[strs.length - 2];// 倒数第二个，1_2_0_NP_2
                    // 最后一个如果是np、ip、vp等，认为是语法结构，激活到语法图，否则不激活
                    if (last.equals("NP") || last.equals("IP") || last.equals("VP") || last.equals("PP")
                            || last0.equals("NP") || last0.equals("IP") || last0.equals("VP") || last0.equals("PP")){
                        ismatch = true;
                        pamListeners.get(0).receivePercept(pn, ModuleName.GrammarGraph);
                        pamListeners.get(0).receivePercept(sink, ModuleName.GrammarGraph);
                        pamListeners.get(0).receivePercept(parent, ModuleName.GrammarGraph);
                    }
                }
            }

//            if(deep <= deepThreshold - 2 && (linkType.equals("相似") || linkType.equals("对等"))){
//                propagateActivation(sink, (PamLink) parent, amountToPropagate, deepThreshold - 2, "pam");
//                // 相似度边，不往上激活，只是概念内部激活
//                continue;
//            }

            // 设置来源id，来源场景id为pn自带
            sink.setFromsceneid(pn.getFromsceneid());
            sink.setFromnodeid(pn.getNodeId());
            sink.setFromLinkType(parent.getCategory().getName());

            // 如果sink是个复合点，有cpstr属性，则检查cpstr里每个点的name是否都在mem里，都在则继续往下激活
//			String cpstr = (String) sink.getProperties().get("cpstr");
//			String name = (String) sink.getProperties().get("name");
            // todo 复合项所有子项都分别激活了，也不一定是整体激活，可能是各个其他词项分别激活部分，整合激活还要判断
            //      完整性判断，加上最近衰减的内容，是否完整
            boolean allInMem = true;
            Term term = null;
            try {
                term = narsese.parseTerm(sname);
            } catch (Parser.InvalidInputException e) {
                throw new RuntimeException(e);
            }
            // 遍历term的每个组成部分，如果有一个不在mem里，则控制激活一层深度。不用考虑层级和位置，只要有就行
            // 时间间隔内，整体激活（各部分激活）则往下，各部分间隔超过阈值，不算，神经激活有不应期
            // 各链接间隔不同，根据强度衰减等，越强间隔越大。另建虚实属性？不经过nars时间戳？
            // 满足度，与激活度不同，虚实度？各满足或虚实求平均？非语句输入=可能只是概念一部分，要语句完整才输入推理
            // 这里通过根节点找子节点，与子节点激活父节点不同，
            // todo 另这是在已激活的里面找，还可在未激活里面找，也就是普通的框架搜索
            if(term instanceof CompoundTerm) {
//                    allInMem = isAllInMemVar((CompoundTerm) term);
                allInMem = isAllInMem((CompoundTerm) term, allInMem, new ArrayList<>());
            }else {
                if (nar.memory.getNode(term.toString()) == null) {
                    allInMem = false;
                }
            }

//				String[] cps = cpstr.split(",");
//				for (String cp : cps) {
//					// 特殊符号忽略，如星号、井号、交并差等
//					if (cp.equals("*") || cp.equals("#") || cp.equals("&") || cp.equals("|") || cp.equals("--")
//							|| cp.equals("&&") || cp.equals("||") || cp.equals("&/") || cp.equals("&\\")){
//						continue;
//					}
//					if (nar.memory.getNode(cp) == null) {
//						allInMem = false;
//						break;
//					}
//				}
            if (allInMem) { //|| tense != null
                propagateActivation(sink, (PamLink) parent, amountToPropagate, deep, "pam");//"pam"
            }else
            if(deep != deepThreshold)
            {
                // 再往下激活一次
                propagateActivation(sink, (PamLink) parent, amountToPropagate, deepThreshold - 2, "pam");
            }

//			propagateActivation(sink, (PamLink) parent, amountToPropagate, deep, from);

        }
    }

    private static void feelAndDo(Node pn, PamLinkImpl parent, String sname) {
        // 某些强烈、典型、底层操作，特别是心理操作，直接体感驱动，不经过动机
        // 喜欢、死、教你、榴莲、美女、鬼、跑
        if (sname.contains("happy")){
            Set<Link> linkSet1 = NeoUtil.getSomeLinks(pn, "相似", ">", null, null);
            for (Link link: linkSet1){
                Node node = (Node) link.getSink();
                Term term = node.toTerm();
                if (term instanceof Operation) {
                    Operation op = (Operation)term;
                    Operator oper = op.getOperator();
                    // 将信息加入注意力队列，由动机协同处理
//                                AgentStarter.attentStr = parent.getSource().getTNname().split(",")[2].replace(")", "");
                    Term[] terms = new Term[2];
                    terms[0] = parent;// 教你，happy.toTerm()
                    terms[1] = op.getArguments();// 方法论
                    List<Task> feedback = oper.execute(op, terms, (Memory) goalNs, nar);
                    if(feedback != null){
                        term = feedback.get(0).getTerm();
                        System.out.println("-----执行------无动机操作-----结果------- " + term);
                    }
                }
            }
        }
    }

    public static boolean isAllInMemVar(CompoundTerm ct) {
        boolean allInMem = true;
        List<String> termNameList = new ArrayList<>();

        allInMem = isAllInMem(ct, allInMem, termNameList);

        if(ct.toString().contains("开心")){
            allInMem = true;
        }

        // 信念变量实例化，维护信念变量表；nars有实例化，但需要推理，有滞后性和随机性，这里直接实例化
        // 即使直接调用nars推理，也是先加入nar，如需立马使用，需在nars加入pam等，或者直接处理，不经nars
        // 多个地方可以嵌入实例化，reason实例化=闲时备用，pam激活就实例化=直接但可能与动机无关，断言实例化=需放开顺承限制？
        // 缓存提交过程实例化。无论哪里实例化，一般都是从变量句找实例句，变量句一般是satement，纯复合结构如合取另说。
        // 目标有自己的实例化，一是变量动机实例化，一是推送到时序参数列表，用于时序执行，精确复杂。动机驱动

        // 关键：反推激活变量的原实例，绑定实例化的源头
        // 如果超过两个$符号，则认为要实例化。没法一个个处理。单个不需要，而且深度大的、分句多的，难找到实例
        // 单变量单副本无需实例化，实例化就是某语句本身。单变量多副本语句、多变量单和多副本都要，也就是整合多语句
        // todo 要求非变量项都已激活，未全激活，实例化不了，也做了无用功？实例化的有错误？但缺失的正是要关注的？
        String termsN = ct.toString();
        if(allInMem && termsN.indexOf("$") != termsN.lastIndexOf("$")){
            // 统计ct最外层里至少两个复合项
            int num = 0;
            if (ct instanceof Statement) {
                Term ctSub =  ((Statement) ct).getSubject();
                if (ctSub instanceof CompoundTerm) {
                    Term[] terms = ((CompoundTerm)ctSub).term;
                    for (Term term1 : terms) {
                        if (term1 instanceof CompoundTerm) {
                            num++;
                        }
                    }
                    // ctSub = (&/,(&&,(*,听到,$命令),<$命令 <-> 命令>),$命令)
                    if (num >= 1 && terms.length >= 2) {
                        List<List<Term>> matchlist = new ArrayList<>();
                        Map<Term,Term> subVar = new LinkedHashMap<>();
                        List<Term> reMatchList = new ArrayList<>();

                        String match = varSub0(termNameList, (CompoundTerm) ctSub, matchlist, subVar);
                        System.out.println("matchlist = " + matchlist + ", subVar = " + subVar);

                        matchVar(subVar, matchlist, (CompoundTerm) ctSub, reMatchList);

                        if (!reMatchList.isEmpty()){
                            for (Term term1: reMatchList){
                                if (term1 instanceof CompoundTerm){
                                    CompoundTerm term2 = (CompoundTerm)term1;
                                    // 长串词项，中间部分也加入mem，方便动机匹配前提，默认为现在时态
                                    // todo 时态处理要更严格严谨
                                    if(term2.term.length > 2){
                                        int index = term2.term.length - 1;
                                        Term[] newprec = new Term[index];
                                        System.arraycopy(term2.term, 0, newprec, 0, index);
                                        // todo 要根据原来类型构建
                                        Term precondition = Conjunction.make(newprec);
                                        nar.addInputTo(precondition + ". :|:", (Memory) goalNs);
                                        nar.addInput(precondition + ". :|:");
                                        System.out.println("addInputTo precondition = " + precondition);
                                    }
                                }

//                                nar.addInputTo(term1.toString() + ".", (Memory) goalNs);

//                                Task term10 = ((CompoundTerm)term1).toTask('!');
//                                ProcessGTreeTask task0 = new ProcessGTreeTask(term10, nar.memory);
//                                taskSpawner.addTask(task0);

                                // 把关系形式复合项也加入，用于动机绑定前提
                                ((Statement) ct).setSubject(term1);
//                                nar.addInputTo(ct + ".", (Memory) goalNs);

                                Task ctt = ct.toTask('!');
                                ProcessGTreeTask task = new ProcessGTreeTask(ctt, nar.memory);
                                taskSpawner.addTask(task);
                            }
                        }
                    }
                }
            }else {
                // todo 非关系形式的复合前提
                // ct = (&/,(&&,(*,听到,$命令),<$命令 <-> 命令>),$命令)
            }
        }
        return allInMem;
    }

    private static boolean isAllInMem(CompoundTerm ct, boolean allInMem, List<String> termNameList) {
        Set<String> termNames = new HashSet<>();
        // 递归遍历ct.term，todo 还要判断位置等
        termNames = ct.getTermNames(termNames);
        for (String termName : termNames) {
            if(!termName.startsWith("$") && !termName.startsWith("#")
                    && !termName.equals("SELF") && !termName.equals("happy")){
                // 变量跳过，以剁了符号开头，而不是只包含，因为拿到了复合词项的每个原子词项。不检查。
                if (nar.memory.getNode(termName) == null && !termName.equals("听到") && !termName.contains("^")
                        && !termName.equals("听到1") && !termName.equals("回应")
                        && !termName.equals("不开心") && !termName.equals("不说")
                ) {
                    allInMem = false;
                    break;
                }else {
                    // 多种情况，可能多个组件在同一句，或者两个隔开一两句
                    termNameList.add(termName);
                }
            }
        }
        return allInMem;
    }
    // todo 可拓展到概念变量，比如两个普通词项需要配对
    public static String varSub0(List<String> notVarList, CompoundTerm ct,
                                 List<List<Term>> matchList0, Map<Term,Term> subVar) {
        synchronized(ct) {
            Term[] terms = ct.term;
            // 未实例化的变量，只有一个，不用实例化，直接跳过
            // 匹配到多少是多少，匹配到的实例化，看缓存有没有，没有则加入mem
            int done = 0;
            // 还要考虑位置，组件相同，位置不同，无要求则可实例化，有要求则不可。同级
            boolean ctHasCt = false;
            Map<Term, Integer> varTermMap = new HashMap<>();
            List<List<Term>> subDoneList = new ArrayList<>();
            // 以ct为准，遍历各部分，再从list中匹配非变量部分，然后对该部分进行变量匹配
            for (int c = 0; c < terms.length; c++) {
                Term term = terms[c];
                // && !(term instanceof Variable)。单个变量项不用实例化，实例化就是自己
                if (!(term instanceof CompoundTerm)){
                    if (term instanceof Variable){
                        varTermMap.put(term, c);
                    }
                    continue;
                }
                ctHasCt = true;
                CompoundTerm cttt = (CompoundTerm) term;
                String result = "";
                boolean hasCttt = false;
                List<Term> subMatchList = new ArrayList<>();

                // 递归匹配，从最里层开始。子层级及以下匹配
                for (Term cttt0 : cttt.term) {
                    // doneList可能会有部分已匹配过，父级与子级相同。但这里只考虑某一层，不跨级？
                    // 变量项不用实例化，放到上一层匹配。常量项一样。list内都是它自己一个
                    if (cttt0 instanceof CompoundTerm) {
                        hasCttt = true;
                        break;
                    }
                }
                if (hasCttt) {
                    result = varSub0(notVarList, cttt, subDoneList, subVar);
                    if (result.equals("true")) {
                        matchVar(subVar, subDoneList, cttt, subMatchList);
                        matchList0.add(subMatchList);
                        // 复合组件也加入mem
                        for (Term term1 : subMatchList) {
                            // todo 要判断是否现在完成
                            nar.addInputTo(term1.toString() + ". :|:", (Memory) goalNs);
                            nar.addInput(term1.toString() + ". :|:");
                        }
                        done++;
                        // 不是直接返回，而是继续往下匹配，因为这里只是一个复合组件，还没遍历完整个ct
                        continue;
                    }else {
                        // 复合组件不匹配，不用继续往下了，即使其他组件匹配，也不行，因为这里是且关系
                        // todo 有或关系？
                        return "false";
                    }
                }

                // 本层级匹配，也即最里层，无其他复合项，复合项都在上层
                for (int i = 0; i < notVarList.size(); i++) {
                    String notVar = notVarList.get(i);
                    if (cttt.toString().contains(notVar)) {
                        Concept nvc1 = nar.memory.concepts.get(notVar);
                        if (nvc1 == null) {continue;}
                        for(TermLink termLink1 : nvc1.termLinks){
                            CompoundTerm ct1 = (CompoundTerm) termLink1.target;
                            int num1 = 0;
                            Term term1 = null;
                            Term varTerm1 = null;
                            if(cttt.term.length == ct1.term.length) {
                                // 对应位置比较
                                for (int j = 0; j < cttt.term.length; j++) {
                                    if (cttt.term[j].equals(ct1.term[j])) {
                                        num1++;
                                    } else {
                                        term1 = ct1.term[j];// 变量项实值
                                        varTerm1 = cttt.term[j];// 变量项带$
                                    }
                                }
                            }
                            // 目前默认一个变量
                            if (num1 == cttt.term.length - 1 && term1 != null && varTerm1 != null && !ct1.hasVar()) {
                                subVar.put(varTerm1, term1);
                                if (!subMatchList.contains(ct1)) {
                                    subMatchList.add(ct1);
                                }
                                //匹配一条，再匹配另一条，用匹配到的varTerm1实例化另一条变量分句，看是否在另一个非变量组件的termLinks里
//                                getElse(notVarList, ct, terms, term, notVar, varTerm1, term1);
                            }
                        }
                    }
                }
                if (!subMatchList.isEmpty()){
                    matchList0.add(subMatchList);
                    done++;
                }else {
                    // 没有实例值，加入varTermMap，看其他已经实例化的变量能否实例化它
                    varTermMap.put(term, c);
                }
            }
            // 如果有单个变量varTermMap，且subvar有实例化值，则实例化单变量，并按index插入matchList0
            if (!varTermMap.isEmpty() && !subVar.isEmpty() && done >= 1){
                // 直接将匹配到的变量值加入到matchList0里，按index插入
                for (Map.Entry<Term, Integer> entry : varTermMap.entrySet()) {
                    Term varTerm = entry.getKey();
                    Integer index = entry.getValue();
                    if(varTerm instanceof Variable){
                        Term term = subVar.get(varTerm);
                        if (term != null){
                            List<Term> subMatchList = new ArrayList<>();
                            subMatchList.add(term);
                            matchList0.add(index, subMatchList);
                        }
                    }else if(varTerm instanceof CompoundTerm){
                        // 非单个变量，遍历varTerm，将变量项实例化
                        for (Term term: ((CompoundTerm) varTerm).term){
                            if (term instanceof Variable){
                                Term term1 = subVar.get(term);
                                if (term1 != null){
                                    ((CompoundTerm) varTerm).deepReplaceVar(term, term1);
                                }
                            }
                        }
                        List<Term> subMatchList = new ArrayList<>();
                        subMatchList.add(varTerm);
                        matchList0.add(index, subMatchList);
                    }else {
                        // 常量项，不用实例化
                    }
                }
            }
            if (matchList0 != null && matchList0.size() >= terms.length - 1){
                return "true";
            }
            if (!ctHasCt){
                return "noCt";
            }
        }
        return "";
    }

    public static void matchVar(Map<Term, Term> subVar, List<List<Term>> subDoneList,
                                CompoundTerm cttt, List<Term> subMatchList) {
        List<List<Term>> subDLs = new ArrayList<>();
        List<Term> subDL = new ArrayList<>();
        CompoundTerm copyTerm0 = cttt.cloneDeep();
        CompoundTerm copyNoVar;
        // 组件各自完全匹配，组装各种可能语句，用findSubstitute判断组件组合是否合理，组合是合取，比组件自身匹配更严格
        // 按顺序，遍历每个组件的所有可能，也就是list里的可能实例
        getAllDone0(subDoneList, subDL, subDLs, 0);

        for(List<Term> lt: subDLs){
            copyTerm0.deepReplaceTerm(lt.toArray(Term.EmptyTermArray));
            // cttt克隆避免污染，copyTerm0一直在变化，如果不再次克隆，加入subMatchList后，subMatchList也变了
            copyNoVar = copyTerm0.cloneDeep();
            Map<Term,Term> subsconc = new LinkedHashMap<>();
            // 变量和实例值集合
            Map<Term,Term> subsconc0 = new LinkedHashMap<>();
            // 组合是否匹配，如果匹配，则组合成立
            boolean conclusionMatches = Variables.findSubstitute(nar.memory.randomNumber, Symbols.VAR_INDEPENDENT,
                    copyNoVar,
                    cttt,
                    subsconc,
                    subsconc0);
            if (conclusionMatches){
                subMatchList.add(copyNoVar);
                // 加入变量列表，可用于实例化单变量等
                appendToMap(subsconc0, subVar);
            }
        }
    }

    private static void getElse(List<String> notVarList, CompoundTerm ct, Term[] terms, Term term, String notVar, Term varTerm1, Term term1) {
        for (Term tt: terms){
            if(tt.toString().equals(term.toString()) || !(tt instanceof CompoundTerm)){
                continue;
            }
            for(String notVar0: notVarList){
                if(notVar0.equals(notVar)){
                    continue;
                }
                if (tt.toString().contains(notVar0) && tt.toString().contains(varTerm1.toString())) {
                    // 用变量实值替换变量，再去nonVar0的termLinks里找，找到则实例化
                    CompoundTerm cttt1 = (CompoundTerm) tt;
                    // 需要复制一份，否则替换后，cttt0也变了
                    CompoundTerm cttt0 = cttt1.cloneDeep();
                    // 实例化，深度替换，递归找到每一层的变量，替换为实例
                    cttt0.deepReplaceVar(varTerm1, term1);
                    Concept nvc2 = nar.memory.concepts.get(notVar0);
                    for(TermLink termLink2 : nvc2.termLinks){
                        CompoundTerm ct2 = (CompoundTerm) termLink2.target;
                        if (ct2.toString().equals(cttt0.toString())){
//                                                    CompoundTerm ctcd = ct.cloneDeep();
                            String ctcds = ct.toString().replace(varTerm1.toString(), term1.toString());
                            // 整个ct实例化
//                                                    ctcd.deepReplace(varTerm1, term1);
                            nar.addInput(ctcds.toString() + ".");
                            nar.addInputTo(ctcds.toString()+ ".", (Memory) goalNs);
                            break;
                        }
                    }
                    // 未匹配成功，cttt0换回来
                    cttt0.deepReplaceVar(term1, varTerm1);
                }
            }
        }
    }

    private static void getAllDone0(List<List<Term>> subDoneList, List<Term> subDL, List<List<Term>> subDLs, int index) {
        // 递归实现。因为不确定多少层
        if (index >= subDoneList.size()){
            return;
        }
        for (int k = 0; k < subDoneList.get(index).size(); k++) {
            subDL.add(subDoneList.get(index).get(k));
            if (subDoneList.size() >= index + 2){
                getAllDone0(subDoneList, subDL, subDLs, index + 1);
                subDL = new ArrayList<>();
            }else {
                subDLs.add(subDL);
            }
        }
    }

    private static void getAllDone(List<List<Term>> subDoneList, List<Term> subDL, List<List<Term>> subDLs, int index) {
        // 递归实现。因为不确定多少层
        if (index >= subDoneList.size()) {return;}
        for (int k = 0; k < subDoneList.get(index).size(); k++) {
            // 添加当前元素到subDL
            subDL.add(subDoneList.get(index).get(k));
            // 用于递归的临时列表，避免影响当前层的subDL
            List<Term> tempSubDL = new ArrayList<>(subDL);
            if (index + 1 < subDoneList.size()) {
                // 递归调用，传递临时列表以保护当前层的subDL
                getAllDone(subDoneList, tempSubDL, subDLs, index + 1);
//                tempSubDL = new ArrayList<>();
            } else {
                // 到达最后一层，将累积的列表添加到subDLs中
                subDLs.add(new ArrayList<>(subDL));
            }
        }
    }

    private boolean varSub(List<String> termNameList, Term[] terms, CompoundTerm ct) {
        synchronized(termNameList) {
            Concept c1 = nar.memory.concepts.get(termNameList.get(0));
            Concept c2 = nar.memory.concepts.get(termNameList.get(1));
            CompoundTerm ctst0 = (CompoundTerm) terms[0];
            CompoundTerm ctst1 = (CompoundTerm) terms[1];
            if(c1 == null || c2 == null) {
                return true;
            }
            // 案例ct：<(&&,(&&,听到,$命令),<$命令 --> 命令>) ==> <$命令 =/> <SELF --> [happy]>>>.
            // 根据案例，在c1和c2的termLinks集合中，找到包含变量实际值的两条，即$命令的两个不同实例
            // 如其中一条termLink的target是(&&,听到,$命令)形式，另一条是(<$命令 --> 命令>)形式，两条中的$命令要相同
            for(TermLink termLink1 : c1.termLinks){
                for (TermLink termLink2 : c2.termLinks) {
                    if(termLink1.target instanceof CompoundTerm && termLink2.target instanceof CompoundTerm) {
                        CompoundTerm ct1 = (CompoundTerm) termLink1.target;
                        CompoundTerm ct2 = (CompoundTerm) termLink2.target;
                        // c1和c2两个非变量组件默认与ct位置相同，ct1和ct2分别对应ct里的两个复合项，里面词项也对应
                        // 不能简单对应前两个，可能是一句包含多个组件，可能隔开，要精确匹配对应
                        if (ct1.term.length == ctst0.term.length && ct2.term.length == ctst1.term.length){
                            // 如果词项位置对应，非变量词项相同，变量位置对应，且变量词项相同，则实例化
                            // ct1与ct的第一个复合项对应，ct2与ct的第二个复合项对应，不是ct1与ct2对应
                            // 对比标准是，只有一项不同，也就是带有变量的一项，其他都相同
                            boolean isSame0 = true;
                            boolean isSame1 = true;
                            int num1 = 0;
                            int num2 = 0;
                            Term term1 = null;
                            Term term2 = null;
                            Term varTerm1 = null;
                            Term varTerm2 = null;
                            for (int i = 0; i < ctst0.term.length; i++) {
                                for (int j = 0; j < ct1.term.length; j++) {
                                    if (ctst0.term[i].equals(ct1.term[j])) {
                                        num1++;
                                    } else {
                                        term1 = ct1.term[j];// 变量项
                                        varTerm1 = ctst0.term[i];// 变量项
                                    }
                                }
                            }
                            if (num1 != ctst0.term.length - 1) {
                                isSame0 = false;
                            }
                            for (int i = 0; i < ctst1.term.length; i++) {
                                for (int j = 0; j < ct2.term.length; j++) {
                                    if (ctst1.term[i].equals(ct2.term[j])) {
                                        num2++;
                                    } else {
                                        term2 = ct2.term[j];// 变量项
                                        varTerm2 = ctst1.term[i];// 变量项
                                    }
                                }
                            }
                            if (num2 != ctst1.term.length - 1) {
                                isSame1 = false;
                            }
                            // 变量项相同，且非变量项相同，且变量项位置对应，则实例化
                            if (isSame0 && isSame1 && term1.toString().equals(term2.toString())
                                    && varTerm1.toString().equals(varTerm2.toString())){
                                // 实例化，深度替换，递归找到每一层的变量，替换为实例
                                ct.deepReplaceVar(varTerm1, term1);
                                nar.addInput(ct.toString());
                                nar.addInputTo(ct.toString(), (Memory) goalNs);
                            }
                        }
                    }
                }
            }
        }
        return false;
    }

    @Override
    public void setSceneMainNode(Node sink) {
        if (sceneNs.getMainNodeId() != 0){
            boolean isesxit = false;
            for (String sceneId: AgentStarter.scenelist){
                if (sceneId.equals(String.valueOf(sink.getNodeId()))){
                    isesxit = true;
                    break;
                }
            }
            // 如果没有待生成的场景，而还有mainid，则直接覆盖，遗留的无用mainid
            if(sceneNs.getNodes().isEmpty()){
                sceneNs.setMainNodeId(sink.getNodeId());
                AgentStarter.scenelist.clear();
            }else if (!isesxit) {
                AgentStarter.scenelist.add(String.valueOf(sink.getNodeId()));
            }
        }else {
            sceneNs.setMainNodeId(sink.getNodeId());
        }
    }

    // todo 基于意象生成语句可能有用
    @Override
    public void getSceneNode(Node scene, String scenename, boolean isvar) {
        String query;
        // 进入场景buffer默认是语言生成或视听想象，普通场景直接通达
        query = "match (n{name:\'" + scenename + "\'})<-[r]-() return r";
        System.out.println("------- getSceneNode ------ " + query);
        try (Transaction tx1 = graphDb.beginTx()) {
            try (Result result = tx1.execute(query, NeoUtil.parameters)) {
                int num = 0;
                while (result.hasNext()) {
                    num++;
                    getScene(result, isvar);
                }
                // 有语法被激活，且为主场景，则触发语法框架建模任务，尽量只一次
                if (num != 0 ) {
                    if(scene.getNodeId() == sceneNs.getMainNodeId()){
                        // 每个场景一个任务，包括子场景？
                        GrammarTask task = new GrammarTask(yufaNs, sceneNs,1,this);
                        taskSpawner.addTask(task);
                    }
                    for(String sceneId : AgentStarter.scenelist){
                        if(sceneId.equals(String.valueOf(scene.getNodeId()))){
                            // 如果接下来的时序执行也激活了，那同样激活语法任务
                            GrammarTask task = new GrammarTask(yufaNs, sceneNs,1,this);
                            taskSpawner.addTask(task);
                        }
                    }
                }
            }
            tx1.commit();
        }
    }

    public void getScene(Result result, boolean isvar) {
        Link link;
        Map<String, Object> row = result.next();
        Relationship re;
        String retype;
        Node fromNode = null;
        Node toNode = null;
        for ( String key : result.columns() ) {
            re = (Relationship) row.get(key);
            retype = re.getType().toString();
            if (retype.equals("顺承")) continue;
            if (retype.equals("时序")) continue;
            if (retype.equals("时序首")) continue;
            if (retype.equals("参数")) continue;

            link = NeoUtil.CastNeoToLidaLink(re,null);
            toNode = (Node)link.getSink();
            pamNodeStructure.addNode(toNode,"PamNodeImpl");
            if(isvar){
                Map<String,Object> resultmap = getIsaLink(link.getSource(), toNode, link.getCategory(),this);
                if (resultmap.get("done").equals("yes")){
                    link = (Link) resultmap.get("link");
                }
            }
            fromNode = link.getSource();
            putMap(fromNode,fromNode.getTNname());
            putMap(toNode,toNode.getTNname());

            // 如果待生成的是场景，继续纳入场景元素以备生成，
            // 在场景buffer之后，父场景边语法激活之前？
            // 避免子场景先集齐语法框架输出，已在语法任务激活时控制
            if(AgentStarter.scenemap.containsKey(fromNode.getTNname())){
                getSceneNode(fromNode,fromNode.getTNname(),isvar);
            }
            activGrammarLink(link, retype);
        }
    }

    @Override
    public  Map getIsaLink(Node node, Node toNode, LinkCategory category, PAMemory pam) {
        Link link;
        Map<String,Object> thisresult = new HashMap<>();
        // 初始化返回map，如果下面没有被更新，则没有新建边，因为没有nowisa
        thisresult.put("done","no");
        for (Link l :seqNs.getLinksOfSource(node.getNodeId())){
            if(l.getTNname().equals("nowisa")){
                thisresult = getIsaLink((Node) l.getSink(),toNode,category,pam);
                if (thisresult.get("done").equals("yes")){
                    // 如果已经新建边，则直接层层返回；
                    return thisresult;
                }
                // 上面没有直接返回，证明还没新建边，如果是含变量场景，则替换为现值
                link = pam.addDefaultLink((Node)l.getSink(), toNode, category);
                thisresult.put("link",link);
                thisresult.put("done","yes");
            }
        }
        return thisresult;
    }

    @Override
    public void activGrammarLink(Link link, String retype) {
        Linkable linkable;
        // 用于生成的场景，可用来对应语法结构，以场景为准，拼接语法框架=更灵活多变可变
        pamListeners.get(0).receivePercept(link.getSource(), ModuleName.SceneGraph);
        pamListeners.get(0).receivePercept((Node)link.getSink(),ModuleName.SceneGraph);
        pamListeners.get(0).receivePercept(link,ModuleName.SceneGraph);
        // 过阈值才通达？不通达不代表没有，无场景无语法=不可能生成=除非直接回忆现有
        pamListeners.get(0).receivePercept(link.getSource(),ModuleName.CurrentSM);
        pamListeners.get(0).receivePercept((Node)link.getSink(), ModuleName.CurrentSM);
        pamListeners.get(0).receivePercept(link, ModuleName.CurrentSM);
        // 无意识痕迹
        pamListeners.get(0).receivePercept(link.getSource(),ModuleName.NonGraph);
        pamListeners.get(0).receivePercept((Node)link.getSink(), ModuleName.NonGraph);
        pamListeners.get(0).receivePercept(link, ModuleName.NonGraph);

        // 从边类型开始激活
        linkable = getNode(retype);

        if (linkable == null) {
            linkable = pamNodeStructure.getNeoNode(retype);
            if (linkable != null) {
                addDefaultNode((Node) linkable);
            }
        }
        linkable.setActivation(0.8);
        // 直接从场景激活语法
        propagateActivationToParents((Node) linkable,1, "sentence");
    }

    // todo 认知执行语句化，在类似nars时序上执行，尽量不用线程？语句只是小图程，直接替换并改元组即可，大图程还需线程
    // 		图程需要动机管理分配，不能直接根据时序连续执行，集中管理=能派生+能中断+能回溯
    // 动机管理介入时序执行，时刻关注当前上层动机，如果上层改动，则不继续往下执行，上层不变则按时序执行
    // 动机时序与语法整合，语法时序本身内隐，思考语法的外显时序跟内隐的不是同一个，外显时序是认知动机时序，内隐时序是语法时序
    // 以下几行为copilot生成-20230321
    // 形成动机语法，动机语法形成动机树，动机树形成动机图，动机图形成动机场景，动机场景形成动机世界，语法、时序、动机、语句、时刻
    // 时刻是语句的执行时间，语句是动机的执行语句，动机是动机图的执行动机，动机图是动机场景的执行动机图，动机场景是动机世界的执行动机场景，动机世界是动机的执行动机世界
    // 语句是动机的执行语句，动机是动机图的执行动机，动机图是动机场景的执行动机图，动机场景是动机世界的执行动机场景，动机世界是动机的执行动机世界
    @Override
    public void getActRoot(Link link, boolean isVar, boolean isLoop, String actStamp) { // 案例：ft151-心理计划-》ft47，
        Node sink = (Node) link.getSink();
        Node source = link.getSource();
        String sname = sink.getTNname();
//        String fname = source.getTNname();
        putMap(sink,sname);
//        putMap(source,fname);

        if(AgentStarter.formap.containsKey(sname) && !isLoop){
            // 如果是for循环，需要先执行循环体，再执行后续。调用本方法前无putmap，所以这里要putmap
            // 循环体内结构同普通时序，但多了判断和遍历索引
            ForEachTask forEachTask = new ForEachTask(link, this, seqNs, sceneNs, actStamp);
            taskSpawner.addTask(forEachTask);
            return;
        }
        // 从时序首开始执行，递归查找到最上头时序
        String query = "match (m)-[r:时序首]->(i) where id(m) = " + sink.getNodeId() + " return r";
        System.out.println("query = " + query);
        Link link0 = null;
        try (Transaction tx0 = graphDb.beginTx()) {
            try (Result result0 = tx0.execute(query, NeoUtil.parameters)) {
                Map<String, Object> row0;
                while (result0.hasNext()) {
                    row0 = result0.next();
                    Relationship actre;
                    for (String key0 : result0.columns()) {
                        actre = (Relationship) row0.get(key0);

                        link0 = NeoUtil.CastNeoToLidaLink(actre,null);
                        Node toNode = (Node)link0.getSink();
                        // 每个时序分别加入计划，以备执行，头节点已有，不用加入
                        pamListeners.get(0).receivePercept(toNode,ModuleName.SeqGraph);
                        pamListeners.get(0).receivePercept(link0, ModuleName.SeqGraph);

                        toNode.setIncentiveSalience(sink.getIncentiveSalience());

                        System.out.println("时序首---|||-" + link0.toString());

                        // 即使当前层时序已经在这里找到并执行，还需要激活非时序节点，如满足和else？
                        // 只需找到时序就行，时序节点具体是什么类型，再根据类型执行，往下就往下，如满足和else
                        propagateActivation(toNode, (PamLink) link0, 1.0, 1, "varmindplan");
                    }
                }
            }
            tx0.commit();
        }
        // 时序加时间戳，以区分时序，方便回溯时序
        List<Link> mainlist = seqNs.getDoMainPath_map().get(actStamp);
        if (mainlist == null) {
            mainlist = new ArrayList<>();
            seqNs.getDoMainPath_map().put(actStamp, mainlist);
        }

        if(link0 != null) {
            if(!isVar){
                // 如果有可能的后续嵌套时序，则将上位时序存入主路线，以便回溯执行
                // 循环时序+判断时序，都会进入这里。时序执行逻辑一样，判断时序的上位是判断边，A--判断（首）--》B
                // todo 执行层级也是标记一种，应该通达到意识，被认知到，通过查缓存可知时序相关的各标记信息

                mainlist.add(link);
            }
            // 尾递归查找执行全部子时序
            getActRoot(link0,false, false, actStamp);
        }else {
            AgentStarter.isDoVar = true;
            AgentStarter.doStartick = TaskManager.getCurrentTick();

            // 这里是尽头，后面没有时序，从这根据节点类型开始执行具体逻辑
            // 跨过当前节点，可能还有时序，要先执行完当前阶段所有可执行节点，里面可能还要递归
//            if(AgentStarter.varscenemap.containsKey(sname) ){//|| sname.startsWith("nars")
//
//                DoMindActTask doMindActTask = new DoMindActTask(sink,source,this, seqNs, goalNs);
//                taskSpawner.addTask(doMindActTask);
//
//            }else
            if(AgentStarter.ifelsemap.containsKey(sname)){
                DoSelectTreeTask doSelectTreeTask = new DoSelectTreeTask(link, this, goalNs, sceneNs, actStamp);
                taskSpawner.addTask(doSelectTreeTask);
            }else {
                // 除了判断，都统一执行，有操作的再针对处理，顺承、变量、初始化等
                DoMindActTask doMindActTask = new DoMindActTask(sink,source,this, seqNs, goalNs, actStamp);
                taskSpawner.addTask(doMindActTask);

//                DoSimpleSceneTask doSimpleSceneTask = new DoSimpleSceneTask(sink,source,this, goalNs);
//                taskSpawner.addTask(doSimpleSceneTask);
            }

//			doSucc(link, sink, source);
//			DoSuccTask doSuccTask = new DoSuccTask(sink,source,this);
//			taskSpawner.addTask(doSuccTask);
        }
    }
}
