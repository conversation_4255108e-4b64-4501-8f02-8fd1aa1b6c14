package edu.memphis.ccrg.lida.nlanguage;

import org.opennars.entity.BudgetValue;
import edu.memphis.ccrg.linars.Term;

import java.util.Collection;

public class SubGraph extends TreeChart{
    SubGraph(String scene, BudgetValue budget, Term cond, Term[] sceneTerm, Collection<Term> foundList, Collection<Term> findingList) {
//        super(scene, budget, cond, sceneTerm, foundList, findingList);
        // 图比树多了环
    }
}
