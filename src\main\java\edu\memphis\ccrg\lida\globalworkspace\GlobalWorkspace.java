/*******************************************************************************
 * Copyright (c) 2009, 2011 The University of Memphis.  All rights reserved. 
 * This program and the accompanying materials are made available 
 * under the terms of the LIDA Software Framework Non-Commercial License v1.0 
 * which accompanies this distribution, and is available at
 * http://ccrg.cs.memphis.edu/assets/papers/2010/LIDA-framework-non-commercial-v1.0.pdf
 *******************************************************************************/
/**
 * 
 */
package edu.memphis.ccrg.lida.globalworkspace;

import edu.memphis.ccrg.lida.attentioncodelets.AttentionCodelet;
import edu.memphis.ccrg.lida.framework.FrameworkModule;
import edu.memphis.ccrg.lida.framework.shared.RefractoryPeriod;
import edu.memphis.ccrg.lida.framework.strategies.DecayStrategy;
import edu.memphis.ccrg.lida.globalworkspace.triggers.BroadcastTrigger;
import edu.memphis.ccrg.lida.globalworkspace.triggers.TriggerListener;
import edu.memphis.ccrg.lida.globalworkspace.triggers.TriggerTask;

/**
 * Interface for the Global Workspace module which contains Coalition objects
 * and implements a global conscious broadcast. It receives {@link Coalition}
 * objects generated by {@link AttentionCodelet} objects from Workspace.
 * Different {@link BroadcastTrigger} tasks can be registered dynamically. When
 * a {@link TriggerTask} fires, all registered {@link BroadcastListener} modules
 * receive the content of the winning {@link Coalition}. Modules that receive
 * the broadcast must register themselves to this module and implement the
 * {@link BroadcastListener} interface.
 * 
 * <AUTHOR> Snaider
 * <AUTHOR> J. McCall
 * @see TriggerListener
 * @see RefractoryPeriod
 */
public interface GlobalWorkspace extends FrameworkModule, TriggerListener, RefractoryPeriod {
	
	/**
	 * Adds specified {@link Coalition}
	 * 
	 * @param c the {@link Coalition} to be added to the GlobalWorkspace
	 * @return true if coalition was added
	 */
	public boolean addCoalition(Coalition c);

	/**
	 * Adds and registers specified {@link BroadcastTrigger}
	 * 
	 * @param t a {@link BroadcastTrigger} which can initiate a broadcast
	 */
	public void addBroadcastTrigger(BroadcastTrigger t);

	/**
	 * Adds and registers a {@link BroadcastListener}. Each registered
	 * {@link BroadcastListener} receives each conscious broadcast.
	 * 
	 * @param bl the {@link BroadcastListener} that will be registered
	 */
	public void addBroadcastListener(BroadcastListener bl);

	/**
	 * Returns the number of broadcasts sent.
	 * 
	 * @return the current number of broadcasts sent by the module
	 */
	public long getBroadcastSentCount();

	/**
	 * Gets the tick at last broadcast
	 * 
	 * @return the tick when the last broadcast occurred
	 */
	public long getTickAtLastBroadcast();

	/**
	 * Sets the {@link DecayStrategy} used by {@link Coalition} objects in this module
	 * @param ds the {@link DecayStrategy} used to decay coalition objects
	 */
	public void setCoalitionDecayStrategy(DecayStrategy ds);

	/**
	 * Gets the {@link DecayStrategy} used by {@link Coalition} objects in this module
	 * @return {@link DecayStrategy} by which coalitions will be decayed.
	 */
	public DecayStrategy getCoalitionDecayStrategy();

	/**
	 * Sets coalition removal threshold
	 * @param t lower bound of activation for a {@link Coalition} to remain in the module
	 */
	public void setCoalitionRemovalThreshold(double t);

	/**
	 * Gets coalition removal threshold
	 * @return lower bound of activation for a {@link Coalition} to remain in the module
	 */
	public double getCoalitionRemovalThreshold();

}
