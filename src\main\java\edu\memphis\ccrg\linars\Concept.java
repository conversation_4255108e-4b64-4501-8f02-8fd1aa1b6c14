/* 
 * The MIT License
 *
 * Copyright 2018 The OpenNARS authors.
 *
 * Permission is hereby granted, free of charge, to any person obtaining a copy
 * of this software and associated documentation files (the "Software"), to deal
 * in the Software without restriction, including without limitation the rights
 * to use, copy, modify, merge, publish, distribute, sublicense, and/or sell
 * copies of the Software, and to permit persons to whom the Software is
 * furnished to do so, subject to the following conditions:
 *
 * The above copyright notice and this permission notice shall be included in
 * all copies or substantial portions of the Software.
 *
 * THE SOFTWARE IS PROVIDED "AS IS", WITHOUT WARRANTY OF ANY KIND, EXPRESS OR
 * IMPLIED, INCLUDING BUT NOT LIMITED TO THE WARRANTIES OF MERCHANTABILITY,
 * FITNESS FOR A PARTICULAR PURPOSE AND NONINFRINGEMENT. IN NO EVENT SHALL THE
 * AUTHORS OR COPYRIGHT HOLDERS BE LIABLE FOR ANY CLAIM, DAMAGES OR OTHER
 * LIABILITY, WHETHER IN AN ACTION OF CONTRACT, TORT OR OTHERWISE, ARISING FROM,
 * OUT OF OR IN CONNECTION WITH THE SOFTWARE OR THE USE OR OTHER DEALINGS IN
 * THE SOFTWARE.
 */
package edu.memphis.ccrg.linars;

import edu.memphis.ccrg.lida.framework.initialization.AgentStarter;
import org.opennars.control.DerivationContext;
import org.opennars.entity.*;
import org.opennars.inference.BudgetFunctions;
import org.opennars.inference.LocalRules;
import org.opennars.interfaces.Timable;
import org.opennars.io.Symbols.NativeOperator;
import org.opennars.io.events.Events.*;
import org.opennars.main.Shell;
import org.opennars.main.Parameters;
import org.opennars.storage.Bag1;

import java.io.Serializable;
import java.util.*;

import org.opennars.control.concept.ProcessQuestion;

import static edu.memphis.ccrg.lida.framework.initialization.AgentStarter.nar;
import static org.opennars.inference.BudgetFunctions.distributeAmongLinks;
import static org.opennars.inference.BudgetFunctions.rankBelief;
import static org.opennars.inference.UtilityFunctions.or;

/**
 * Concept as defined by the NARS-theory
 *
 * Concepts are used to keep track of interrelated sentences
 *
 * <AUTHOR> Wang
 * <AUTHOR> Hammer
 */
public class Concept extends Item<Term> implements Serializable {
    /**
     * The term is the unique ID of the concept
     */
    public Term term;
    
    // recent events that happened before the operation the concept represents was executed
    // 在执行操作之前发生的最近事件
    public Bag1<Task<Term>, Sentence<Term>> seq_before;

    /**
     * Task links for indirect processing
     */
    public Bag1<TaskLink,Task> taskLinks;
//    public List<TaskLink> taskLinks;

    /**
     * Term links between the term and its components and compounds; beliefs
     * 词项与其组件和化合物之间的术语链接；信念。但不包含变量单词项，复合词项可有变量
     * 冗余设计，缓存换效率，可由整体找到组件，也可组件找到整体。比neo便捷。可做热数据，超额则衰减即可
     */
//    public final Bag1<TermLink,TermLink> termLinks;
    public Set<TermLink> termLinks;

    /**
     * Link templates of TermLink, only in concepts with CompoundTerm Templates
     * are used to improve the efficiency of TermLink building
     */
    public List<TermLink> termLinkTemplates;

    /**
     * Pending Question directly asked about the term
     *
     * Note: since this is iterated frequently, an array should be used. To
     * avoid iterator allocation, use .get(n) in a for-loop
     */
    public List<Task> questions;
    
    /**
     * Pending Quests to be answered by new desire values
     */
    public List<Task> quests;

    /**
     * Judgments directly made about the term Use List because of access and insertion in the middle
     * 断言直接关于术语使用列表，因为访问和插入在中间
     */
    public List<Task> beliefs;
    public List<Task> executable_preconditions;
    public List<Task> general_executable_preconditions;

    /**
     * Desire values on the term, similar to the above one
     * 欲望值在术语上，类似于上面的
     */
    public List<Task> desires;

    /**
     * Reference to the memory to which the Concept belongs
     * Concept所属的内存的引用
     */
    public Memory memory;
    

    // use to create averaging stats of occurring intervals
    // so that revision can decide whether to use the new or old term
    // based on which intervals are closer to the average
    // 用于创建发生间隔的平均统计数据，以便修订可以决定是使用新术语还是旧术语
    public final List<Float> recent_intervals = new ArrayList<>();
    //whether it received a "native" input task
    //是否收到“本地”输入任务
    public boolean observable = false;
    //for operations, becomes false if sufficiently confident, used procedure knowledge  exists.
    //对于操作，如果有足够的信心，使用过程知识存在，则变为false。
    public boolean allowBabbling = true;

    public Concept(){
        super();
    }

    /**
     * Constructor, called in Memory.getConcept only
     *  构造函数，仅在Memory.getConcept中调用
     * @param tm A term corresponding to the concept
     * @param memory A reference to the memory
     */
    public Concept(final BudgetValue b, final Term tm, final Memory memory) {
        super(b);        
        
        this.term = tm;
        this.memory = memory;

        this.questions = new ArrayList<>();
        this.beliefs = new ArrayList<>();
        this.executable_preconditions = new ArrayList<>();
        this.general_executable_preconditions = new ArrayList<>();
        this.quests = new ArrayList<>();
        this.desires = new ArrayList<>();

//        this.taskLinks = new Bag<>(memory.narParameters.TASK_LINK_BAG_LEVELS, memory.narParameters.TASK_LINK_BAG_SIZE, memory.narParameters);
//        this.termLinks = new Bag<>(memory.narParameters.TERM_LINK_BAG_LEVELS, memory.narParameters.TERM_LINK_BAG_SIZE, memory.narParameters);

        this.taskLinks = new Bag1<>(memory.narParameters.TASK_LINK_BAG_SIZE);
//        this.termLinks = new Bag1<>(memory.narParameters.TERM_LINK_BAG_SIZE);

        if (tm instanceof CompoundTerm) {
            this.termLinkTemplates = ((CompoundTerm) tm).prepareComponentLinks();
            // 复制一份为termLinks，防止多线程同时修改。将list转set
            this.termLinks = Collections.synchronizedSet(new HashSet<>(termLinkTemplates));
//            this.termLinks = Collections.synchronizedSet(termLinkTemplates);
//            this.termLinks = Collections.synchronizedList(new ArrayList<>(termLinkTemplates));
//            this.termLinks = Collections.synchronizedList(termLinkTemplates);
//            this.termLinks = termLinkTemplates;
        } else {
            this.termLinkTemplates = null;
            this.termLinks = new HashSet<>();
        }

//        this.taskLinks = new ArrayList<>();
    }

    @Override
    public boolean equals(final Object obj) {
        if (this == obj) return true;
        if (!(obj instanceof Concept)) return false;
        Term name = ((Concept)obj).name();
        if (name == null) name = ((Concept)obj).name();
        Term name2 = name();
        if (name2 == null) name2 = name();
        return name.equals(name2);
    }

    @Override
    public int hashCode() { return name().hashCode();     }

    @Override
    public Term name() {
        return term;
    }

    public void addToTable(final Task task, final boolean rankTruthExpectation, final List<Task> table,
                           final int max, final Class eventAdd, final Class eventRemove, final Object... extraEventArguments) {
        final int preSize = table.size();
        final Task removedT;
        Sentence removed = null;
        removedT = addToTable(task, table, max, rankTruthExpectation);
        if(removedT != null) {
            removed=removedT.sentence;
        }

        if (removed != null) {
            memory.event.emit(eventRemove, this, removed, task, extraEventArguments);
        }
        if ((preSize != table.size()) || (removed != null)) {
            memory.event.emit(eventAdd, this, task, extraEventArguments);
        }
    }
    
    /**
     * Link to a new task from all relevant concepts for continued processing in
     * the near future for unspecified time.
     * The only method that calls the TaskLink constructor.
     * 链接到所有相关概念的新任务，以便在不久的将来继续处理未指定时间。
     * @param task The task to be linked
     * @param content The content of the task
     */
    public TaskLink linkToTask(final Task task, final DerivationContext content) {
        final BudgetValue taskBudget = task.budget;

        TaskLink retLink = new TaskLink(task, null, taskBudget, content.narParameters.TERM_LINK_RECORD_LENGTH);
        insertTaskLink(retLink, content);  // link type: SELF

        if (!(term instanceof CompoundTerm)) {
            return retLink;
        }
        if (termLinkTemplates.isEmpty()) {
            return retLink;
        }
                
        final BudgetValue subBudget = distributeAmongLinks(taskBudget, termLinkTemplates.size(), content.narParameters);
        if (subBudget.aboveThreshold()) {

            for (final TermLink termLink : termLinkTemplates) {
                if (termLink.type == TermLink.TEMPORAL)
                    continue;
                final Term componentTerm = termLink.target;

                final Concept componentConcept = memory.conceptualize(subBudget, componentTerm);

                if (componentConcept != null) {
                    synchronized(componentConcept) {
                        // globalbuffer死锁 - waiting to lock <0x00000005aa808968> (a edu.memphis.ccrg.linars.Concept)
                        componentConcept.insertTaskLink(new TaskLink(task, termLink, subBudget,
                                content.narParameters.TERM_LINK_RECORD_LENGTH), content );
                    }
                }
            }
            buildTermLinks(taskBudget, content.narParameters);  // recursively insert TermLink
        }
        return retLink;
    }

    /**
     * Add a new belief (or goal) into the table Sort the beliefs/desires by
     * rank, and remove redundant or low rank one
     * 加入新的信念（或目标）到表中，按rank排序信念/欲望，并删除冗余或低rank的信念/欲望
     * @param table The table to be revised
     * @param capacity The capacity of the table
     * @return whether table was modified
     */
    public static Task addToTable(final Task newTask, final List<Task> table, final int capacity, final boolean rankTruthExpectation) {
        final Sentence newSentence = newTask.sentence;
        final float rank1 = rankBelief(newSentence, rankTruthExpectation);    // for the new isBelief
        float rank2;        
        int i;
        for (i = 0; i < table.size(); i++) {
            final Sentence judgment2 = table.get(i).sentence;
            rank2 = rankBelief(judgment2, rankTruthExpectation);
            if (rank1 >= rank2) {
                if (newSentence.truth.equals(judgment2.truth) && newSentence.stamp.equals(judgment2.stamp,false,true,true)) {
                    //System.out.println(" ---------- Equivalent Belief: " + newSentence + " == " + judgment2);
                    return null;
                }
                table.add(i, newTask);
                break;
            }            
        }
        
        if (table.size() == capacity) {
            // nothing
        } else if (table.size() > capacity) {
            final Task removed = table.remove(table.size() - 1);
            return removed;
        } else if (i == table.size()) { // branch implies implicit table.size() < capacity
            table.add(newTask);
        }
        return null;
    }

    /**
     * Select a belief value or desire value for a given query
     * 选择给定查询的信念值或欲望值
     * @param query The query to be processed
     * @param list The list of beliefs or desires to be used
     * @return The best candidate selected
     */
    public Task selectCandidate(final Task query, final List<Task> list, final Timable time) {
        //        if (list == null) {
        //            return null;
        //        }
        float currentBest = 0;
        float beliefQuality;
        Task candidate = null;
        final boolean rateByConfidence = true; //table vote, yes/no question / local processing
        synchronized (list) {
            for (final Task judgT : list) {
                final Sentence judg = judgT.sentence;
                beliefQuality = LocalRules.solutionQuality(rateByConfidence, query, judg, memory, time); //makes revision explicitly search for
                if (beliefQuality > currentBest /*&& (!forRevision || judgT.sentence.equalsContent(query)) */ /*&& (!forRevision || !Stamp.baseOverlap(query.stamp.evidentialBase, judg.stamp.evidentialBase)) */) {
                    currentBest = beliefQuality;
                    candidate = judgT;
                }
            }
        }
        return candidate;
    }

    public Task toTask(char punctuation) {
        Sentence s = new Sentence<>(term, punctuation, new TruthValue((float) 1.0, 0.9, AgentStarter.nar.narParameters), new Stamp(nar, nar.memory));
        Task t = new Task(s, new BudgetValue(0.8f, 0.5f, 1, AgentStarter.nar.narParameters), Task.EnumType.INPUT);
        return t;
    }

    public static class AnticipationEntry implements Serializable {
        public float negConfirmationPriority = 0.0f;
        public Task negConfirmation = null;
        public long negConfirm_abort_mintime = 0;
        public long negConfirm_abort_maxtime = 0;
        public AnticipationEntry(float negConfirmationPriority, Task negConfirmation, long negConfirm_abort_mintime, long negConfirm_abort_maxtime) {
            this.negConfirmationPriority = negConfirmationPriority;
            this.negConfirmation = negConfirmation;
            this.negConfirm_abort_mintime = negConfirm_abort_mintime;
            this.negConfirm_abort_maxtime = negConfirm_abort_maxtime;
        }
    }
    public List<AnticipationEntry> anticipations = new ArrayList<>();

    /* ---------- insert Links for indirect processing ---------- */
    /**
     * Insert a TaskLink into the TaskLink bag
     * <p>
     * called only from Memory.continuedProcess
     *
     * @param taskLink The termLink to be inserted
     */
    protected boolean insertTaskLink(final TaskLink taskLink, final DerivationContext nal) {
        int complexity = taskLink.getTarget().sentence.getTerm().getComplexity();
        if (complexity > 10) {
//            System.out.println( complexity + " ---- TaskLink too complex: ---" + taskLink.getTarget().sentence);
            return false;
        }
        final Task target = taskLink.getTarget();
        //what question answering, question side:
        ProcessQuestion.ProcessWhatQuestion(this, target, nal);
        //what question answering, belief side:
        ProcessQuestion.ProcessWhatQuestionAnswer(this, target, nal);
        //HANDLE MAX PER CONTENT
        //if taskLinks already contain a certain amount of tasks with same content then one has to go
        final boolean isEternal = target.sentence.isEternal();
        int nSameContent = 0;
        float lowest_priority = Float.MAX_VALUE;
        TaskLink lowest = null;
        for(final TaskLink tl : taskLinks) {
            final Sentence s = tl.getTarget().sentence;
            if(s.getTerm().equals(taskLink.getTerm()) && s.isEternal() == isEternal) {
                nSameContent++; //same content and occurrence-type, so count +1
                if(tl.getPriority() < lowest_priority) { //the current one has lower priority so save as lowest
                    lowest_priority = tl.getPriority();
                    lowest = tl;
                }
                if(nSameContent > nar.narParameters.TASKLINK_PER_CONTENT) { //ok we reached the maximum so lets delete the lowest
                    taskLinks.pickOut(lowest.toString());
                    memory.emit(TaskLinkRemove.class, lowest, this);
                    break;
                }
            }
        }
        //END HANDLE MAX PER CONTENT
        final TaskLink removed = taskLinks.putIn(taskLink);      
        if (removed!=null) {
            if (removed == taskLink) {
                memory.emit(TaskLinkRemove.class, taskLink, this);
                return false;
            }
            else {
                memory.emit(TaskLinkRemove.class, removed, this);
            }
        }
        memory.emit(TaskLinkAdd.class, taskLink, this);
        return true;
    }


    /**
     * Recursively build TermLinks between a compound and its components
     * called only from Memory.continuedProcess
     * 递归地在复合物和其组件之间建立TermLinks，仅从Memory.continuedProcess调用
     * @param taskBudget The BudgetValue of the task
     */
    public void buildTermLinks(final BudgetValue taskBudget, Parameters narParameters) {
        if (termLinkTemplates == null || termLinkTemplates.size() == 0) {
            return;
        }
        final BudgetValue subBudget = distributeAmongLinks(taskBudget, termLinkTemplates.size(), narParameters);
        if (!subBudget.aboveThreshold()) {
            return;
        }
        for (final TermLink template : termLinkTemplates) {
            if (template.type == TermLink.TRANSFORM) {
                continue;
            }
            final Term target = template.target;
            final Concept concept = memory.conceptualize(taskBudget, target);
            if (concept == null) {
                continue;
            }
            // this termLink to that and vice versa
            insertTermLink(new TermLink(target, template, subBudget));
            concept.insertTermLink(new TermLink(term, template, subBudget));

            if (target instanceof CompoundTerm && template.type != TermLink.TEMPORAL) {
                concept.buildTermLinks(subBudget, narParameters);
            }
        }
    }

    /**
     * Insert a TermLink into the TermLink bag
     * called from buildTermLinks only
     * 插入TermLink到TermLink包中，仅从buildTermLinks调用
     * @param termLink The termLink to be inserted
     */
    public boolean insertTermLink(final TermLink termLink) {
        int complexity = termLink.target.getComplexity();
        if (complexity > 10) {
//            System.out.println( complexity + " ---- TermLink too complex: ---" + termLink.target);
            return false;
        }
        termLinks.add(termLink);
//        final TermLink removed = termLinks.putIn(termLink);
//        if (removed != null) {
//            if (removed == termLink) {
//                memory.emit(TermLinkRemove.class, termLink, this);
//                return false;
//            } else {
//                //emit remove and add for this case
//                memory.emit(TermLinkRemove.class, removed, this);
//            }
//        }
        memory.emit(TermLinkAdd.class, termLink, this);
        return true;        
    }

    /**
     * Return a string representation of the concept, called in ConceptBag only
     * 返回概念的字符串表示，仅在ConceptBag中调用
     * @return The concept name, with taskBudget in the full version
     */
//    @Override
//    public String toString() {  // called from concept bag
//        //return (super.toStringBrief() + " " + key);
//        return super.toStringExternal();
//    }

    /**
     * called from {@link Shell}
     */
    @Override
    public String toStringLong() {
        final String res =
                toStringExternal() + " " + term.name()
                + toStringIfNotNull(termLinks.size(), "termLinks")
                + toStringIfNotNull(taskLinks.nameSize(), "taskLinks")
                + toStringIfNotNull(beliefs.size(), "beliefs")
                + toStringIfNotNull(desires.size(), "desires")
                + toStringIfNotNull(questions.size(), "questions")
                + toStringIfNotNull(quests.size(), "quests");
        
                //+ toStringIfNotNull(null, "questions");
        /*for (Task t : questions) {
            res += t.toString();
        }*/
        // TODO other details?
        return res;
    }

    private String toStringIfNotNull(final Object item, final String title) {
        if (item == null) {
            return "";
        }
        final String itemString = item.toString();
        return new StringBuilder(2 + title.length() + itemString.length() + 1).
                append(" ").append(title).append(':').append(itemString).toString();
    }
    // 含义：获取概念的质量 acquired=获得的
    public float acquiredQuality = 0.0f;
    public void incAcquiredQuality() {
        acquiredQuality += 0.1f;
        if(acquiredQuality > 1.0f) {
            acquiredQuality = 1.0f;
        }
    }

    public float getAveragePriority() {
        float linkPriority = 0;
        for (final TaskLink tl : taskLinks) {
            linkPriority += tl.getPriority();
        }
        return linkPriority;
    }

    private final Object toMutex = new Object();
    /**
     * Recalculate the quality of the concept [to be refined to show
     * extension/intension balance]
     *
     * @return The quality value
     */
    @Override
    public float getQuality() {
        final float linkPriority;
        synchronized (toMutex) {
            linkPriority = getAveragePriority();
        }
        final float termComplexityFactor = 1.0f / (term.getComplexity()*memory.narParameters.COMPLEXITY_UNIT);
        final float result = or(acquiredQuality, linkPriority, termComplexityFactor);
        if (result < 0) {
//            System.out.println("Concept.getQuality < 0:  result = " + result + ", linkPriority = " + linkPriority
//                    + " ,termComplexityFactor = " + termComplexityFactor + ", termLinks.size = " + termLinks.nameSize());
            return 0.01f;
//            throw new IllegalStateException("Concept.getQuality < 0:  result = " + result + ", linkPriority = " + linkPriority
//                    + " ,termComplexityFactor = " + termComplexityFactor + ", termLinks.size = " + termLinks.nameSize());
        }
        return result;
    }

    /**
     * Return the templates for TermLinks, only called in
     * Memory.continuedProcess
     *
     * @return The template get
     */
    public List<TermLink> getTermLinkTemplates() {
        return termLinkTemplates;
    }

    /**
     * Select a isBelief to interact with the given task in inference
     * <p>
     * get the first qualified one
     * <p>
     * only called in RuleTables.reason
     *
     * @param task The selected task
     * @return The selected isBelief
     */
    public Sentence getBelief(final DerivationContext nal, final Task task) {
        final Stamp taskStamp = task.sentence.stamp;
        final long currentTime = nal.time.time();

        for (final Task beliefT : beliefs) {  
            final Sentence belief = beliefT.sentence;
            nal.emit(BeliefSelect.class, belief);
            nal.setTheNewStamp(taskStamp, belief.stamp, currentTime);
            
            final Sentence projectedBelief = belief.projection(taskStamp.getOccurrenceTime(), nal.time.time(), nar.memory);
            /*if (projectedBelief.getOccurenceTime() != belief.getOccurenceTime()) {
               nal.singlePremiseTask(projectedBelief, task.budget);
            }*/
            
            return projectedBelief;     // return the first satisfying belief
        }
        return null;
    }

    /**
     * Get the current overall desire value. TODO to be refined
     */
    public TruthValue getDesire() {
        if (desires.isEmpty()) {
            return null;
        }
        final TruthValue topValue = desires.get(0).sentence.truth;
        return topValue;
    }
    
    /**
     * Replace default to prevent repeated inference, by checking TaskLink
     *
     * @param taskLink The selected TaskLink
     * @param time The current time
     * @return The selected TermLink
     */
    public TermLink selectTermLink(final TaskLink taskLink, final long time, final Parameters narParameters) {
        final int toMatch = narParameters.TERM_LINK_MAX_MATCHED; //Math.min(memory.param.termLinkMaxMatched.get(), termLinks.size());
        for (int i = 0; (i < toMatch) && (termLinks.size() > 0); i++) {
            // 获取第一条记录
//            final TermLink termLink = termLinks.remove(0);
//            final TermLink termLink = termLinks.takeOut();
            final TermLink termLink = getOneTermLink();
            if (termLink == null)
                break;
            if (taskLink.novel(termLink, time, narParameters)) {
                //return, will be re-inserted in caller method when finished processing it
                return termLink;
            }
            //just put back since it isn't novel
            returnTermLink(termLink);
        }
        return null;
    }

    private TermLink getOneTermLink() {
        synchronized (termLinks) {
            if (termLinks.isEmpty()) {
                return null;
            }
            // 要求budget不能为null
            TermLink termLink = null;
            for (int i = 0; i < termLinks.size(); i++) {
                termLink = termLinks.iterator().next();
                if (termLink.budget != null) {
                    termLinks.remove(termLink);
                    break;
                }else {
                    termLink = null;
                }
            }
            return termLink;
        }
    }

    public void returnTermLink(final TermLink termLink) {
        puBack(termLink, memory.cycles(memory.narParameters.TERMLINK_FORGET_DURATIONS), memory);
    }

    private void puBack(TermLink termLink, float cycles, Memory memory) {
//        int complexity = termLink.target.getComplexity();
//        if (complexity > 15) {
//            return;
//        }
        final float relativeThreshold = memory.narParameters.FORGET_QUALITY_RELATIVE;
        BudgetFunctions.applyForgetting(termLink.budget, cycles, relativeThreshold);
        insertTermLink(termLink);
    }

    /**
     * Return the questions, called in ComposionalRules in
     * dedConjunctionByQuestion only
     */
    public List<Task> getQuestions() {
        return Collections.unmodifiableList(questions);
    }
    public List<Task> getQuess() {
        return Collections.unmodifiableList(quests);
    }

    public void discountConfidence(final boolean onBeliefs) {
        if (onBeliefs) {
            for (final Task t : beliefs) {
                t.sentence.discountConfidence(memory.narParameters);
            }
        } else {
            for (final Task t : desires) {
                t.sentence.discountConfidence(memory.narParameters);
            }
        }
    }

    public NativeOperator operator() {
        return term.operator();
    }

    public Term getTerm() {
        return term;
    }

    /** returns unmodifidable collection wrapping beliefs */
    public List<Task> getBeliefs() {
        return Collections.unmodifiableList(beliefs);
    }
    
    /** returns unmodifidable collection wrapping beliefs */
    public List<Task> getDesires() {
        return Collections.unmodifiableList(desires);
    }

    // 克隆概念
    public Concept clone() {
        Concept c = new Concept();
        c.term = new Term(term.name());
        c.beliefs = new ArrayList<>(beliefs);
        c.desires = new ArrayList<>(desires);
        c.questions = new ArrayList<>(questions);
        c.quests = new ArrayList<>(quests);
        c.memory = memory;
        c.termLinks = new HashSet<>(termLinks);
        c.taskLinks = new Bag1<>(memory.narParameters.TASK_LINK_BAG_SIZE);
        c.taskLinks = taskLinks;
        c.executable_preconditions = new ArrayList<>(executable_preconditions);
        c.general_executable_preconditions = new ArrayList<>(general_executable_preconditions);
//        c.termLinkTemplates = new ArrayList<>(termLinkTemplates);
        return c;
    }
}
