package edu.memphis.ccrg.lida.sensorymotormemory;

import edu.memphis.ccrg.lida.actionselection.Action;
import edu.memphis.ccrg.lida.framework.tasks.FrameworkTaskImpl;
import edu.memphis.ccrg.lida.framework.tasks.TaskManager;

import java.util.logging.Level;
import java.util.logging.Logger;

public class ProcessActionTask extends FrameworkTaskImpl {

    private static final Logger logger = Logger
            .getLogger(ProcessActionTask.class.getCanonicalName());

    private final BasicSensoryMotorMemory basicSensoryMotorMemory;
    private Action action;
    public ProcessActionTask(BasicSensoryMotorMemory basicSensoryMotorMemory, Action a) {
        super(basicSensoryMotorMemory.processActionTaskTicks);
        this.basicSensoryMotorMemory = basicSensoryMotorMemory;
        action = a;
        Object alg = basicSensoryMotorMemory.actionAlgorithmMap.get((Number) action.getId());
        if(alg != null){
            this.basicSensoryMotorMemory.sendActuatorCommand(alg);
        }
    }
    @Override
    protected void runThisFrameworkTask() {
//        Object alg = basicSensoryMotorMemory.actionAlgorithmMap.get((Number) action.getId());
        Object alg = action.getName();
        if(alg != null){
            basicSensoryMotorMemory.sendActuatorCommand(alg);
        } else {
            logger.log(Level.WARNING,
                    "The SensoryMotorMemory does not contain an action-algorithm mapping for action {1}",
                    new Object[] { TaskManager.getCurrentTick(), action });
        }
        cancel();
    }
}
