/* 
 * The MIT License
 *
 * Copyright 2018 The OpenNARS authors.
 *
 * Permission is hereby granted, free of charge, to any person obtaining a copy
 * of this software and associated documentation files (the "Software"), to deal
 * in the Software without restriction, including without limitation the rights
 * to use, copy, modify, merge, publish, distribute, sublicense, and/or sell
 * copies of the Software, and to permit persons to whom the Software is
 * furnished to do so, subject to the following conditions:
 *
 * The above copyright notice and this permission notice shall be included in
 * all copies or substantial portions of the Software.
 *
 * THE SOFTWARE IS PROVIDED "AS IS", WITHOUT WARRANTY OF ANY KIND, EXPRESS OR
 * IMPLIED, INCLUDING BUT NOT LIMITED TO THE WARRANTIES OF MERCHANTABILITY,
 * FITNESS FOR A PARTICULAR PURPOSE AND NONINFRINGEMENT. IN NO EVENT SHALL THE
 * AUTHORS OR COPYRIGHT HOLDERS BE LIABLE FOR ANY CLAIM, DAMAGES OR OTHER
 * LIABILITY, WHETHER IN AN ACTION OF CONTRACT, TORT OR OTHERWISE, ARISING FROM,
 * OUT OF OR IN CONNECTION WITH THE SOFTWARE OR THE USE OR OTHER DEALINGS IN
 * THE SOFTWARE.
 */
package org.opennars.control.concept;

import com.google.common.base.Optional;
import edu.memphis.ccrg.lida.framework.initialization.AgentStarter;
import edu.memphis.ccrg.linars.Memory;
import org.jetbrains.annotations.Nullable;
import org.opennars.control.DerivationContext;
import edu.memphis.ccrg.linars.Concept;
import org.opennars.entity.Sentence;
import org.opennars.entity.Stamp;
import org.opennars.entity.Task;
import org.opennars.language.*;
import org.opennars.storage.InternalExperienceBuffer;

import static com.google.common.collect.Iterables.tryFind;
import java.util.LinkedHashSet;
import java.util.List;
import java.util.Map;
import java.util.Set;

import static edu.memphis.ccrg.lida.framework.initialization.AgentStarter.nar;
import static org.opennars.inference.LocalRules.revisible;
import static org.opennars.inference.LocalRules.revision;
import static org.opennars.inference.LocalRules.trySolution;
import static org.opennars.inference.LocalRules.calcTaskAchievement;

import org.opennars.inference.TemporalRules;
import org.opennars.io.events.Events;
import edu.memphis.ccrg.linars.CompoundTerm;
import edu.memphis.ccrg.linars.Term;
import org.opennars.operator.Operation;

public class ProcessJudgment {
    /**
     * To accept a new judgment as belief, and check for revisions and solutions.
     * Revisions will be processed as judgment tasks by themselves.
     * Due to their higher confidence, summarizing more evidence,
     * the will become the top entries in the belief table.
     * Additionally, judgements can themselves be the solution to existing questions
     * and goals, which is also processed here.
     * 
     * @param task The judgment task to be accepted
     * @param concept The concept of the judment task
     * @param nal The derivation context
     */
    public static void processJudgment(final Concept concept, final DerivationContext nal, final Task task) {
        InternalExperienceBuffer.handleOperationFeedback(task, nal);
        final Sentence judg = task.sentence;
        ProcessAnticipation.confirmAnticipation(task, concept, nal);
        final Task oldBeliefT = concept.selectCandidate(task, concept.beliefs, nal.time);   // only revise with the strongest -- how about projection?
        Sentence oldBelief = null;
        if (oldBeliefT != null) {
            oldBelief = oldBeliefT.sentence;
            final Stamp newStamp = judg.stamp;
            final Stamp oldStamp = oldBelief.stamp;       //when table is full, the latter check is especially important too
            if (newStamp.equals(oldStamp,false,false,true)) {
                concept.memory.removeTask(task, "Duplicated");
                return;
            } else if (revisible(judg, oldBelief, nar.narParameters)) {
                nal.setTheNewStamp(newStamp, oldStamp, nal.time.time());
                final Sentence projectedBelief = oldBelief.projection(nal.time.time(), newStamp.getOccurrenceTime(), concept.memory);
                if (projectedBelief != null) {
                    nal.setCurrentBelief(projectedBelief);
                    revision(judg, projectedBelief, concept, false, nal);
                    task.setAchievement(calcTaskAchievement(task.sentence.truth, projectedBelief.truth));
                }
            }
        }
        if (!task.aboveThreshold()) {
            return;
        }
        final int nnq = concept.questions.size();
        for (int i = 0; i < nnq; i++) {
            trySolution(judg, concept.questions.get(i), nal, true);
        }
        final int nng = concept.desires.size();
        for (int i = 0; i < nng; i++) {
            trySolution(judg, concept.desires.get(i), nal, true);
        }
        concept.addToTable(task, false, concept.beliefs,
                nar.narParameters.CONCEPT_BELIEFS_MAX, Events.ConceptBeliefAdd.class, Events.ConceptBeliefRemove.class);
    }
    
    /**
     * Check whether the task is an executable hypothesis of the form
     * <(&/,a,op()) =/> b>.
     * 
     * @param task The judgement task be checked
     * @param nal The derivation context
     * @return Whether task is an executable precondition
     */
    protected static boolean isExecutableHypothesis(Task task, final DerivationContext nal) {
        final Term subj = getTerm(task);
        if (subj == null) return false;

        // also it has to be enactable, meaning the last entry of the sequence before the interval is an operation:
        // 它也必须是可执行的，这意味着间隔之前序列的最后一个条目是一个操作：
        boolean isInExecutableFormat = false;
        if(subj instanceof Conjunction) {
            final Conjunction conj = (Conjunction) subj;
            // 条件很多限制，非空间，但前向，长度大于4，偶数，最后一个是间隔，倒数第二个是操作
            isInExecutableFormat = !conj.isSpatial &&
                    conj.getTemporalOrder() == TemporalRules.ORDER_FORWARD &&
                    conj.term.length >= 4 && conj.term.length%2 == 0 &&
                    conj.term[conj.term.length-1] instanceof Interval &&
                    conj.term[conj.term.length-2] instanceof Operation;
        }else if(!(subj instanceof CompoundTerm)){
//            isInExecutableFormat = subj instanceof Operation;
            isInExecutableFormat = subj.toString().contains("^");
        }

        return isInExecutableFormat;
    }
    public static boolean isBeliefsHypothesis(Task task, DerivationContext nal) {
        final Term subj = getTerm(task);
        if (subj == null) return false;

        // 只要是顺承，不管是可执行还是状态，都加入到目标概念的信念中
        return true;
    }
    @Nullable
    private static Term getTerm(Task task) {
        final Term term = task.getTerm();
        Term subj;
        //|| (!(term instanceof Implication)
        //                && !(term instanceof Similarity) && !(term instanceof Equivalence))

//        if(!task.sentence.isEternal()) {
//            // 不是永久的，不加入到目标概念信念中
//            return null;
//        }
        if(!(term instanceof Statement)) {
            return null;
        }

        if (term instanceof Implication) {
            final Implication imp = (Implication) term;
            if(imp.getTemporalOrder() != TemporalRules.ORDER_FORWARD) {
                return null;
            }
//            subj = imp.getSubject();
//        }else {
//            subj = ((Statement) term).getSubject();
        }

        subj = ((Statement) term).getSubject();

//        if (!(subj instanceof Conjunction)) {
//            return false;
//        }
        return subj;
    }

    /**
     * Add <(&/,a,op()) =/> b> beliefs to preconditions in concept b
     * 
     * @param task The potential implication task
     * param nal The derivation context
     */
    protected static void addToTargetConceptsPreconditions(final Task task, Memory memory) {
        Set<Term> targets = new LinkedHashSet<>();
        Term pred = ((Statement)task.getTerm()).getPredicate();
        Term subj = ((Statement)task.getTerm()).getSubject();
//        if(pred.toString().equals("<SELF --> [happy]>") && subj instanceof CompoundTerm){
//            System.out.println("---命令----addToTargetConceptsPreconditions ------ " + task.toString());
//        }
        // add to all components, unless it doesn't have vars
        // 添加到所有组件，除非它没有变量
        if(!pred.hasVar()) {
            // <(&&,运算,(&&,26,加,8)) <-> 命令>。 <(&&,运算,(&&,$加数1,加,$加数2)) <-> nars_4>。
            // 反推条件，不含变量，是时序路径溯源，结果反推条件。变量语句是部分反推整体
            // todo 可考虑含变量
            targets.add(pred);
        } else {
//            if(pred instanceof CompoundTerm && subj instanceof CompoundTerm){
//                System.out.println("addToTargetConceptsPreconditions CompoundTerm----" + task.toString());
//            }
            // <nars_4 <-> (&&,运算,(&&,$加数1,加,$加数2))>.
            // 非变量组件，关联变量语句，在动机bestReactionForGoal中，根据变量语句的非变量组件，汇总找到可代换的变量语句
            Map<Term, Integer> ret = pred.countTermRecursively(null);
            targets.addAll(ret.keySet());
        }
        // the concept of the implication task
        // 后面的这个task.getTerm()是一个Implication，它的subject是一个Conjunction，它的term是一个Operation
        Concept origin_concept = memory.concept(task.getTerm());
        if(origin_concept == null) {
            return;
        }
        // get the first eternal. the highest confident one (due to the sorted order):
        // 获取第一个永恒的。最有信心的一个（由于排序顺序）：词项相同，但数值不同
        Optional<Task> strongest_target = null;
        synchronized(origin_concept) {
            strongest_target = tryFind(origin_concept.beliefs, iTask -> iTask.sentence.isEternal());
        }
        if (!strongest_target.isPresent()) {
            return;
        }
        Statement iii = (Statement) strongest_target.get().getTerm();
        if(iii.getSubject() instanceof Conjunction) {
            final Term[] prec0 = ((Conjunction)iii.getSubject()).term;
            for (int i = 0; i < prec0.length - 2; i++) {
                if (prec0[i] instanceof Operation) {
                    // don't react to precondition with an operation before the last
                    // for now, these can be decomposed into smaller such statements anyway
                    // 不要对最后一个之前的具有操作的前提做出反应，现在，这些可以被分解成更小的语句
                    return;
                }
            }
        }
        // 这里天然拆分组件并关联到目标（结论），pam对变量实例化，还需拆分关联。
        for(Term t : targets) {
            // the target sub concepts it needs to go to
            // 它需要匹配的目标子概念，也就是非变量组件。包括整体结论本身
            final Concept target_concept = memory.concept(t);
            if(target_concept == null) { //target concept does not exist
                continue;
            }
            // we do not add the target, instead the strongest belief in the target concept
            // 我们不添加目标，用目标概念的最强信念代替。词项相同，但数值不同
            synchronized(target_concept) {
                boolean hasVar = strongest_target.get().sentence.term.hasVar();
                List<Task> table = null;
                if (hasVar) {
                    // 这里就在追踪溯源，含变量语句每个非变量部分，都与变量语句关联，根据部分来查找判断是否可代换
                    table = target_concept.general_executable_preconditions;
                } else {
                    // 不含变量，是时序路径溯源，结果反推条件。变量语句是部分反推整体
                    table = target_concept.executable_preconditions;
                }
                // at first we have to remove the last one with same content from table
                // 首先，我们必须从表中删除具有相同内容的最后一个
                int i_delete = -1;
                for(int i = 0; i < table.size(); i++) {
                    if(CompoundTerm.replaceIntervals(table.get(i).getTerm()).equals(
                            CompoundTerm.replaceIntervals(strongest_target.get().getTerm()))) {
                        // even these with same term but different intervals are removed here
                        // 即使这些术语相同但间隔不同也会在此处删除
                        i_delete = i;
                        break;
                    }
                }
                if(i_delete != -1) {
                    table.remove(i_delete);
                }
                // this way the strongest confident result of this content is put into table but the table ranked according to truth expectation
                // 这样，这个内容的最强有信心的结果就被放入表中，但表按照真实的期望排名
                // todo 这里只增加，顺推激活，可能只占该概念全部组件的一部分，后期需按情况衰减删除，动态调整
                target_concept.addToTable(strongest_target.get(), true, table,
                        nar.narParameters.CONCEPT_BELIEFS_MAX, Events.EnactableExplainationAdd.class,
                        Events.EnactableExplainationRemove.class);
            }
        }
    }


}
