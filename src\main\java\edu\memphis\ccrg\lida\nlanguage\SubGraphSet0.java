package edu.memphis.ccrg.lida.nlanguage;

public class SubGraphSet0 extends ChartTreeSet {
    // 图可包含树，树不能包含图，但图可以做一个树的整体节点，不能展开整合进树，树是特殊图=继承图？
    // 图树比点边集多了更多复杂结构。规则图，语法树，时序树，概念图？场景图，知识图。都是模式，除必要模式外，其他可按需抽取
    // 路径是特殊树=只有光杆=单子叶，一条产生式=原chart=也一种特殊树，只有两层，其他可以保护多层嵌套多分支

    // 子图可以是单点或单边，可用继承于复合term的类别，限制点集内关系。不定关系+后期定义关系=较自由灵活
    // 原始复合term只是term集，可继承自定义内含固定关系，点集关系=如单边类型+集合类型等，普通边关系可自定义，只含两点
    // 点集map，边集map，点边所在子图map，单子图包含点边map，子图树集map=根节点为key，单图树=单模式=额外简单类，不用ns
    // 加属性=是否单图=限制单根，空图或与图有关才加入某点边。no，
    // todo 直接用subgraph类继承ns，做语法buffer，图树在buffer里构建，每个图树都是chart模式
    // 整图衰减=图内点边衰减后=子图也消失？子图不消失=但多了待激活点边，直到全衰减或根节点衰减

    // 虽然可以从散点集和散边集构造出图树，但散点边集能构造的图树不明确，只能预先构建整体=各值统计+框架区分+缓存加速
    // 很多需预先构造的图树，用于预测推理和嵌套构建，自传体图亦是，要素激活率和激活强度，判断推理方向路径
    // 总图和分图，所有点边汇总=总预测+总构建，语法树+语义树+其他=联合预测+联合构建，各图也可独立预测构建
    // 区分散树和子树，散树=两层多叉树，子树=多层多叉树，子树与散树可有同样根节点，但子树后面有完整树结构，嵌套由子树构成，最先由散树构成

    // 对既有数据预测，对缺失数据推理新建。各子图间需要关联整合，之间需多种各类结构关系，三段论+结构变形
    // 推理预测方向=内隐意图，也可外显动机，并非要外显动机处理一切，语言意图可能内隐，顺着预测构建，就理解和表达了
    // 想表达内容=文本框架=内隐意图，外显后成脑内视听想象，agi还有一种文本外显=生成语句文本+框架直接输出

    // 状态、位置、堆栈，构式树+树集，子图+图集

}
