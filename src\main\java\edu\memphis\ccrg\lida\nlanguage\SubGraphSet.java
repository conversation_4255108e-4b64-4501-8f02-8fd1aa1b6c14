package edu.memphis.ccrg.lida.nlanguage;

import edu.memphis.ccrg.linars.Memory;

// todo 先继承mem，不做太大改动，bag或队列，改为树
// 树作用：
// 1、梳理脉络，有迹可循，统筹零散动机；也提供增删改查基础，兼容灵活操作
// 2、用来参考，以供竞争；谁跟谁竞争，不能乱，结构竞争；怎么竞争，其他备选需超越才能替代，数值竞争
// 3、确定执行方案罗列，执行基础；作为中介，不用立即执行，还可复盘，复用；执行只对动机树负责，不会有其他干扰
// 4、整合状态、时序、体感，多种动机内容和表征形式；父子兄弟动机，兼容灵活结构
// todo 可参考soar，增删改查等底层操作本身也可被操作，本质操作本身也是一种对象
// todo 链树图多种结构结合，备选树，动机链，时序图
public class SubGraphSet extends Memory{
    public SubGraphSet() {
        super();
    }
    // 同一个点边集，可能构建出多个普通树结构，特定的树结构就是一个特定的点边子集，NodeStructureImpl是总集
    // 目标树子集需特定构建，确定的点边才加入，竞争性构建，分短中长目标，限量，点边过多，则将暂不执行的放一边
    // 可复用某些结构，如果只是平面平等罗列，则只有一个实例。语句arg结构本身有数量标记，纯构式可能无标记。这里边无标记
    public Tree_nest goalTree = new Tree_nest(new TreeNode("<SELF --> [happy]>"));

    // Tree_nest类是树结构基本方法，这里是特定树结构构建方案，目标线程是操作方案

    // 202405之前，结合nars方案：场景顺推，直到本能，途中路径加入nars概念前提条件，再从本能反推，提升途中路径为目标，进而执行
    // 05之后，不能只顺推或反推，中间路径加入目标buffer，各自交互竞争，结果整合到goaltree中，最终执行
    // todo 本质是一个自底向上构建多叉树，可直接用arg结构算法，只是算的是顺承和相似，不是arg组件
}
