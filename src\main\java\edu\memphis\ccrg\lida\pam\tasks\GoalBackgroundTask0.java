/*******************************************************************************
 * Copyright (c) 2009, 2011 The University of Memphis.  All rights reserved. 
 * This program and the accompanying materials are made available 
 * under the terms of the LIDA Software Framework Non-Commercial License v1.0 
 * which accompanies this distribution, and is available at
 * http://ccrg.cs.memphis.edu/assets/papers/2010/LIDA-framework-non-commercial-v1.0.pdf
 *******************************************************************************/
package edu.memphis.ccrg.lida.pam.tasks;

import edu.memphis.ccrg.lida.framework.FrameworkModule;
import edu.memphis.ccrg.lida.framework.ModuleName;
import edu.memphis.ccrg.lida.framework.initialization.AgentStarter;
import edu.memphis.ccrg.lida.framework.shared.Node;
import edu.memphis.ccrg.lida.framework.shared.NodeStructure;
import edu.memphis.ccrg.lida.framework.tasks.FrameworkTaskImpl;
import edu.memphis.ccrg.lida.framework.tasks.TaskManager;
import edu.memphis.ccrg.lida.nlanguage.SubGraphSet;
import edu.memphis.ccrg.lida.pam.PAMemory;
import edu.memphis.ccrg.lida.workspace.Workspace;
import edu.memphis.ccrg.lida.workspace.WorkspaceImpl;
import edu.memphis.ccrg.lida.workspace.workspacebuffers.WorkspaceBuffer;
import edu.memphis.ccrg.linars.Memory;
import org.opennars.entity.Task;

import java.util.logging.Level;
import java.util.logging.Logger;

import static edu.memphis.ccrg.lida.framework.initialization.AgentStarter.nar;

/**
 */
public class GoalBackgroundTask0 extends FrameworkTaskImpl {
	private static final Logger logger = Logger.getLogger(GoalBackgroundTask0.class.getCanonicalName());
//	private SensoryMemory sm;
	private PAMemory pam;
	private NodeStructure seqNs;
	private NodeStructure goalNs;
	private NodeStructure listenNs;

	public WorkspaceBuffer csm;
	public WorkspaceBuffer seqGraph;
	public WorkspaceBuffer goalGraph;

	public WorkspaceBuffer listenGraph;
	private String query;

	@Override
	public void setAssociatedModule(FrameworkModule m, String moduleUsage) {
		if(m instanceof PAMemory){    // 用factorydata里的初始化，不是alifeagent里的
			pam = (PAMemory) m;
		}else if(m instanceof Workspace){
			csm = (WorkspaceBuffer) m.getSubmodule(ModuleName.CurrentSM);
			seqGraph = (WorkspaceBuffer) m.getSubmodule(ModuleName.SeqGraph);
			goalGraph = (WorkspaceBuffer) m.getSubmodule(ModuleName.GoalGraph);
			listenGraph = (WorkspaceBuffer) m.getSubmodule(ModuleName.ListenGraph);

			seqNs = seqGraph.getBufferContent(null);
			goalNs = goalGraph.getBufferContent(null);
			listenNs = listenGraph.getBufferContent(null);
		}else{
			logger.log(Level.WARNING, "Cannot add module {1}",
					new Object[]{TaskManager.getCurrentTick(),m});
		}
	}

	/**
	 * 动机管理：动机维持，动机执行，动机挂起，动机激活，动机取消，动机中断，动机竞争
	 */
	@Override
	protected void runThisFrameworkTask() {
		Node isasource;
		Node isasink;
//		seqNs = pam.getWorkspaceBuffer("seq").getBufferContent(null);
// 		goalNs = goalGraph.getBufferContent(null);

		WorkspaceBuffer buffer = (WorkspaceBuffer) ((WorkspaceImpl) pam.getListener()).getSubmodule(ModuleName.GoalGraph);
		NodeStructure nsmem = buffer.getBufferContent(null);

//		Memory mem = (Memory) nsmem;

		// 树图结构操作
		SubGraphSet mem = (SubGraphSet) nsmem;

		Long tick = TaskManager.getCurrentTick() - AgentStarter.doStartick;

//		boolean isDovar = true;
//		if(tick > 20){
//			for(FrameworkTask task:pam.getAssistingTaskSpawner().getTasks()){
//			}
//		}

//		if (AgentStarter.isDoVar && tick < 200){
			// 动机维持，之前在时序buffer，放动机管理
//			for(Link seqlink:seqNs.getLinks()){
//				if(seqlink.getTNname().equals("nowisa")){
//					seqlink.setActivation(seqlink.getActivation() + 0.3);
//					isasource = seqlink.getSource();
//					isasink = (Node) seqlink.getSink();
//					isasource.setActivation(isasource.getActivation() + 0.2);
//					isasink.setActivation(isasink.getActivation() + 0.2);
//				}
//			}

//		}else {
//			AgentStarter.isDoVar = false;
//		}

		// 第一方面：判断listenNs是否包含顺承链接，有则激活整个场景，做备用动机
		// 应该是先多模态整合，这里暂限听觉输入，
//		for(Link glink:listenNs.getLinks()){
//			if(glink.getTNname().equals("顺承")){
//				// 涉我判断+真假判断，感知buffer直达，构建场景，todo 模糊匹配=上位对象+上位动作=类似场景
//				// 默认主人+对我说+X，一个音就直接激活动机，一般知道是谁说的时候，已经有几个音
//				// 动机：我+对主人+说，具体内容待定，等有足够激励即可执行，语无伦次是内容不足
//
////				String query = "match (n)<-[r]-(m:场景)-[r0]->(i) where n.name = \'"
////						+ "对主人" + "\'  and i.name = \'" + "说"  + "\' return *";
//
//				// 搜索glink的尾节点场景，将场景所有要素加入goalGraph，充当备用动机挂起
//				Node goal = (Node) glink.getSink();
//				query = "match (m:场景)-[r]->(n) where m.name = \'" + goal.getTNname() + "\' return *";
//				NeoUtil.addNodesAndRelsToBuffer(query, ModuleName.GoalGraph);
//			}
//		}

		// 第二方面：从体感舒适的根本目标反推
		// 需要两边夹击，场景是状态条件，体感是根本目标
		// todo 直接搜条件与体感的最短路，中间是备选动机，最后是根本目标

		// 知识和程序分离，知识=概念和组分，意向=时序和体感，意向和信念分离。
		// 动机子图目标驱动，只处理动机任务，不推理+不管知识任务，知识子图信念驱动，不管动机任务，但推理
		// 这里整体调控，全面竞争，nars目标是单条处理
//		((Memory) nsmem).doGBuffer("goal");

		// 根据mem.globalBuffer的数量，决定单次执行几条任务，区间按万算。作用是按数量调整，减少任务数量，提高效率
		if(mem.globalBuffer.nameSize() > 10){
			for (int i = 0; i < mem.globalBuffer.nameSize() / 1000; i++) {
				taskExecute(mem);
			}
		}else{
			taskExecute(mem);
		}

		// 不取消就是一直执行，与常驻线程有别，常驻是agi启动即有+一直执行+也可取消，这里是调用才有
//		cancel();
	}

	private static void taskExecute(Memory mem) {
		final Task task = mem.globalBuffer.takeOut();// 无key提取
		if (task != null) {
			if(!task.processed) {
				task.processed = true;
//				if(task.sentence.term.toString().contains("$")){
//					System.out.println("----gbt----相似--变量-----：" + task.toString());
//				}
				mem.localInference(task, mem.narParameters, nar, mem);

//				if(task.sentence.term.toString().contains("ft_不执行")){
//					System.out.println("----" + task.sentence.term.toString() + "----");
//				}
				// 统计执行时间
//				long startTime = System.currentTimeMillis();
//				System.out.println("执行任务：" + task.sentence.term.toString());

//				mem.localInference(task, mem.narParameters, time);

//				long endTime = System.currentTimeMillis();
//				System.out.println("执行任务时间：" + (endTime - startTime) + "ms" + " ---- 任务：" + task.toString());
			}

//			mem.globalBuffer.putBack(task, mem.narParameters.GLOBAL_BUFFER_FORGET_DURATIONS, mem);
		}
	}
}
