/*******************************************************************************
 * Copyright (c) 2009, 2011 The University of Memphis.  All rights reserved. 
 * This program and the accompanying materials are made available 
 * under the terms of the LIDA Software Framework Non-Commercial License v1.0 
 * which accompanies this distribution, and is available at
 * http://ccrg.cs.memphis.edu/assets/papers/2010/LIDA-framework-non-commercial-v1.0.pdf
 *******************************************************************************/
package edu.memphis.ccrg.lida.pam.tasks;

import edu.memphis.ccrg.lida.data.NeoUtil;
import edu.memphis.ccrg.lida.framework.ModuleName;
import edu.memphis.ccrg.lida.framework.initialization.AgentStarter;
import edu.memphis.ccrg.lida.framework.shared.Link;
import edu.memphis.ccrg.lida.framework.shared.Node;
import edu.memphis.ccrg.lida.framework.shared.NodeStructure;
import edu.memphis.ccrg.lida.framework.tasks.FrameworkTaskImpl;
import edu.memphis.ccrg.lida.framework.tasks.TaskManager;
import edu.memphis.ccrg.lida.pam.PAMemory;
import edu.memphis.ccrg.linars.Memory;
import org.neo4j.graphdb.Relationship;
import org.neo4j.graphdb.Result;
import org.neo4j.graphdb.Transaction;

import java.util.ArrayList;
import java.util.List;
import java.util.Map;

import static com.warmer.kgmaker.KgmakerApplication.graphDb;
import static edu.memphis.ccrg.lida.framework.initialization.AgentStarter.nar;
import static edu.memphis.ccrg.lida.pam.tasks.DoSelectTreeTask.getResult;

/**
 */
public class DoSuccTask extends FrameworkTaskImpl {
	private  List<Link> cosclist;
	private Node sink;
	private Node source;
	private Link link;
	private PAMemory pam;
	private NodeStructure seqNs;
	private NodeStructure goalNs;
	private String actStamp;

	/**
	 * 执行完当前，则按顺承等链接往下执行，树状执行，后面的时序可以查到并加入buffer，方便查，
	 * 提高性能？继续执行放到独立新任务去，以免时序执行过度重叠，稍微拉长执行路径
	 * 异步执行，任务间可以互相监督，思维可以按任务流程来控制？也要按内容和激活激励
	 *
	 * 认知执行期间的各变量值需要维持，动机目标需要维持，工作记忆维持，注意维持，思维维持
	 * 识别判断流程+内容+激活激励，统筹规划，完全停止+执行中掐断+再唤醒
	 */
	public DoSuccTask(Node sink, Node source, PAMemory pam, int ticksPerRun, String actStamp) {
		super(1, "tact");
		this.sink = sink;
		this.source = source;
//		this.link = link;
		this.pam = pam;
		this.actStamp = actStamp;
	}

	/**
	 */
	@Override
	protected void runThisFrameworkTask() {
		seqNs = pam.getWorkspaceBuffer("seq").getBufferContent(null);
		goalNs = pam.getWorkspaceBuffer("goal").getBufferContent(null);

		String hname = source.getTNname();
		String tname = sink.getTNname();
		String query;
		query = "match (n:场景)-[r:时序]->(m:场景)<-[r0:顺承]-(i:场景) where n.name = \'" + hname + "\'  and i.name = \'" + tname + "\' return r";
		System.out.println("DoSuccTask----query = " + query);
		Link link1 = null;
		try (Transaction tx0 = graphDb.beginTx()) {
			try (Result result0 = tx0.execute(query, NeoUtil.parameters)) {
				Map<String, Object> row0;
				while (result0.hasNext()) {
					row0 = result0.next();
					Relationship actre;
					for (String key0 : result0.columns()) {
						actre = (Relationship) row0.get(key0);

						link1 = NeoUtil.CastNeoToLidaLink(actre,null);
						Node toNode = (Node)link1.getSink();

						pam.getListener().receivePercept(toNode, ModuleName.SeqGraph);
						pam.getListener().receivePercept(link1, ModuleName.SeqGraph);

						toNode.setIncentiveSalience(sink.getIncentiveSalience());

						System.out.println("顺承时序---|||-" + link1.toString());
					}
				}
			}
			// 如果有后续时序，需要再递归遍历到最右子时序
			if (link1 != null) {
				pam.getActRoot(link1,false, false, actStamp);
			}else {
				boolean isloopDone = false;
				// 如果是循环体，需要重复执行。先判断头节点是否有循环标签，然后查循环条件，满足则继续执行，不满足则回溯
				if (AgentStarter.formap.containsKey(hname)) {
					// do while循环，之前已经执行过一次，所以这里先判断条件，再执行一次
					query = "match (n)-[r:循环条件]->(m) where id(m) = " + source.getNodeId() + " return r";
					Link plink0 = seqNs.getDoMainPath_map().get(actStamp).get(1);
					DoSelectTreeTask.Done done = null;
					Link link2 = null;
					try (Transaction tx1 = graphDb.beginTx()) {
						link2 = NeoUtil.getOneLinkTx(query, tx1);
						List<Link> cLinkList = new ArrayList<Link> ();
						int donenum = 0;
						if (link2 != null) {
							cosclist = new ArrayList<Link>();
							// 参数：link无用，第二个是条件框架节点
							done = getResult(link, link2.getSource(), cLinkList, donenum, cosclist);
						}else {
							System.out.println("---------循环条件----空------------");
						}
						tx1.commit();
					}
					if (done != null && done.donenum == 2) {
						// 循环条件满足，继续执行
						// 循环体执行跟普通时序一致。do的部分。目前默认主路径第一条
						pam.getActRoot(plink0, false, false, actStamp);

						StringBuilder sb0 = DoSelectTreeTask.getStringBuilderSafe(cosclist);

						if (sb0.length() > 0) {
							nar.addInputTo("(^say,{SELF},(#," + sb0.toString() + "))! :|:", (Memory) goalNs);
							System.out.println("------succ---判断首和判断---|||---s-a-y----" + sb0.toString());
						}else {
							System.out.println("---------判断首和判断----s-a-y----空--------------" + link2.toString());
						}
						isloopDone = true;
					}
				}
				// 如果不是循环体，或者循环条件不满足，则回溯上位时序，继续执行
				if (!isloopDone){
					doLoop();
				}
			}
			tx0.commit();
		}

		AgentStarter.isDoVar = true;
		AgentStarter.doStartick = TaskManager.getCurrentTick();

		cancel();
	}

	private void doLoop() {
		List<Link> seqMainPath = new ArrayList<>();
		boolean isonlyif = false;
		// todo 放在动机系统，判断执行情况，需要反馈，有动机注意
		// 判断时序执行完和判断遍历完，只会出现一个，时序执行完后，直接回溯上位时序，而非上位判断边
		seqMainPath = seqNs.getDoMainPath_map().get(actStamp);
		if(seqMainPath != null && !seqMainPath.isEmpty()){
			// 倒序遍历回溯到上位时序，继续执行
			Link plink1 = seqMainPath.get(seqMainPath.size() - 1);
			if (plink1 != null ) {
				if (plink1.getCategory().equals("判断") || plink1.getCategory().equals("判断首")) {
					if (seqMainPath.size() > 1) {
						plink1 = seqMainPath.get(seqMainPath.size() - 2);
						seqMainPath.remove(seqMainPath.size() - 1);
					} else {
						isonlyif = true;
					}
				}
				if (!isonlyif) {
					// 回溯后按常规往下执行
					DoSuccTask doSuccTask = new DoSuccTask((Node) plink1.getSink(), plink1.getSource(), pam, 30, actStamp);
					pam.getAssistingTaskSpawner().addTask(doSuccTask);
				}
				// 执行完，回溯后删除当前时序，以便下次回溯
				// todo 报错----》Index 24784 out of bounds for length 24784
				seqMainPath.remove(seqNs.getDoMainPath_map().get(actStamp).size() - 1);
			}
		}
	}
}
