package edu.memphis.ccrg.lida.search;

/**
 * 搜索参数类，用于配置搜索行为。
 * 提供了细粒度的搜索控制，包括深度、宽度、时间限制等。
 */
public class SearchParameters {
    // 搜索深度控制
    private int maxDepth = 5;

    // 搜索宽度控制
    private int maxBranchingFactor = 10;

    // 时间限制（毫秒）
    private long timeLimit = 500;

    // 结果数量限制
    private int resultLimit = 20;

    // 激活阈值
    private double activationThreshold = 0.3;

    // 相似度阈值
    private double similarityThreshold = 0.6;

    // 是否使用缓存
    private boolean useCache = true;

    // 是否并行执行
    private boolean parallel = false;

    // Getter和Setter方法
    public int getMaxDepth() {
        return maxDepth;
    }

    public void setMaxDepth(int maxDepth) {
        this.maxDepth = maxDepth;
    }

    public int getMaxBranchingFactor() {
        return maxBranchingFactor;
    }

    public void setMaxBranchingFactor(int maxBranchingFactor) {
        this.maxBranchingFactor = maxBranchingFactor;
    }

    public long getTimeLimit() {
        return timeLimit;
    }

    public void setTimeLimit(long timeLimit) {
        this.timeLimit = timeLimit;
    }

    public int getResultLimit() {
        return resultLimit;
    }

    public void setResultLimit(int resultLimit) {
        this.resultLimit = resultLimit;
    }

    public double getActivationThreshold() {
        return activationThreshold;
    }

    public void setActivationThreshold(double activationThreshold) {
        this.activationThreshold = activationThreshold;
    }

    public double getSimilarityThreshold() {
        return similarityThreshold;
    }

    public void setSimilarityThreshold(double similarityThreshold) {
        this.similarityThreshold = similarityThreshold;
    }

    public boolean isUseCache() {
        return useCache;
    }

    public void setUseCache(boolean useCache) {
        this.useCache = useCache;
    }

    public boolean isParallel() {
        return parallel;
    }

    public void setParallel(boolean parallel) {
        this.parallel = parallel;
    }

    /**
     * 构建器模式实现，用于流式创建参数对象
     */
    public static class Builder {
        private final SearchParameters params = new SearchParameters();

        public Builder withMaxDepth(int maxDepth) {
            params.maxDepth = maxDepth;
            return this;
        }

        public Builder withMaxBranchingFactor(int maxBranchingFactor) {
            params.maxBranchingFactor = maxBranchingFactor;
            return this;
        }

        public Builder withTimeLimit(long timeLimit) {
            params.timeLimit = timeLimit;
            return this;
        }

        public Builder withResultLimit(int resultLimit) {
            params.resultLimit = resultLimit;
            return this;
        }

        public Builder withActivationThreshold(double threshold) {
            params.activationThreshold = threshold;
            return this;
        }

        public Builder withSimilarityThreshold(double threshold) {
            params.similarityThreshold = threshold;
            return this;
        }

        public Builder useCache(boolean useCache) {
            params.useCache = useCache;
            return this;
        }

        public Builder parallel(boolean parallel) {
            params.parallel = parallel;
            return this;
        }

        public SearchParameters build() {
            return params;
        }
    }
}
