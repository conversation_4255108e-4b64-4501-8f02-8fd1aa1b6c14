/*******************************************************************************
 * Copyright (c) 2009, 2011 The University of Memphis.  All rights reserved. 
 * This program and the accompanying materials are made available 
 * under the terms of the LIDA Software Framework Non-Commercial License v1.0 
 * which accompanies this distribution, and is available at
 * http://ccrg.cs.memphis.edu/assets/papers/2010/LIDA-framework-non-commercial-v1.0.pdf
 *******************************************************************************/
package edu.memphis.ccrg.lida.workspace;

import edu.memphis.ccrg.lida.episodicmemory.CueListener;
import edu.memphis.ccrg.lida.episodicmemory.LocalAssociationListener;
import edu.memphis.ccrg.lida.framework.FrameworkModuleImpl;
import edu.memphis.ccrg.lida.framework.ModuleListener;
import edu.memphis.ccrg.lida.framework.ModuleName;
import edu.memphis.ccrg.lida.framework.initialization.AgentStarter;
import edu.memphis.ccrg.lida.framework.shared.Link;
import edu.memphis.ccrg.lida.framework.shared.LinkImpl;
import edu.memphis.ccrg.lida.framework.shared.Node;
import edu.memphis.ccrg.lida.framework.shared.NodeStructure;
import edu.memphis.ccrg.lida.framework.tasks.TaskManager;
import edu.memphis.ccrg.lida.globalworkspace.BroadcastListener;
import edu.memphis.ccrg.lida.globalworkspace.Coalition;
import edu.memphis.ccrg.lida.pam.PamListener;
import edu.memphis.ccrg.lida.workspace.workspacebuffers.WorkspaceBuffer;
import edu.memphis.ccrg.linars.CompoundTerm;
import edu.memphis.ccrg.linars.Memory;
import edu.memphis.ccrg.linars.Term;
import org.jetbrains.annotations.Nullable;
import org.opennars.entity.*;
import org.opennars.inference.TemporalRules;
import org.opennars.io.Parser;
import org.opennars.language.*;
import org.opennars.main.Nar;

import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;
import java.util.logging.Level;
import java.util.logging.Logger;

import static edu.memphis.ccrg.lida.framework.initialization.AgentStarter.nar;
import static edu.memphis.ccrg.lida.framework.initialization.AgentStarter.narsese;

/**
 * 
 * The Workspace contains the Perceptual and 
 * Episodic Buffers as well as the Broadcast Queue and Current Situational Model. 
 * This class implements the Facade pattern.  Any outside module that wishes to access and/or 
 * modify these Workspace components must do so through this class. 
 * Thus this class defines the methods to access the data of these submodules.
 *
 * 工作区包含感知缓冲区和*情景缓冲区以及广播队列和当前情况模型。
 * *此类实现Facade模式。希望访问和/或修改这些工作区组件的任何外部模块都必须通过此类进行。
 * *因此，此类定义了访问这些子模块的数据的方法
 *
 * <AUTHOR> Snaider
 * <AUTHOR> J. McCall
 *
 */
public class WorkspaceImpl extends FrameworkModuleImpl implements Workspace,
		PamListener, LocalAssociationListener, BroadcastListener{
	private static final Logger logger = Logger.getLogger(WorkspaceImpl.class.getCanonicalName());
	private List<CueListener> cueListeners = new ArrayList<CueListener>();
	private List<WorkspaceListener> workspaceListeners = new ArrayList<WorkspaceListener>();
	
	/*** Default constructor*/
	public WorkspaceImpl(){}
	
	@Override
	public void addListener(ModuleListener listener) {
		if (listener instanceof WorkspaceListener){
			addWorkspaceListener((WorkspaceListener)listener);
		}else if (listener instanceof CueListener){
			addCueListener((CueListener)listener);
		}else{
			logger.log(Level.WARNING, "Listener {1} was not added, wrong type.", 
					new Object[]{TaskManager.getCurrentTick(),listener});
		}
	}

	@Override
	public void addCueListener(CueListener l){
		cueListeners.add(l);
	}

	@Override
	public void addWorkspaceListener(WorkspaceListener listener){
		workspaceListeners.add(listener);
	}
	
	@Override
	public void cueEpisodicMemories(NodeStructure content){
		for(CueListener c: cueListeners){
			c.receiveCue(content);
		}
		logger.log(Level.FINER, "Cue performed.", TaskManager.getCurrentTick());
	}
	
	/*
	 * Received broadcasts are sent to the BroadcastQueue
	 * 收到的广播将发送到BroadcastQueue
	 */
	@Override
	public void receiveBroadcast(Coalition c) {
		if(containsSubmodule(ModuleName.BroadcastQueue)){
			BroadcastListener listener = (BroadcastListener) getSubmodule(ModuleName.BroadcastQueue);
			try {
				listener.receiveBroadcast(c);
			} catch (Exception e) {
				e.printStackTrace();
			}
		}else{
			logger.log(Level.WARNING, "Received broadcast content but Workspace does not have a broadcast queue.", TaskManager.getCurrentTick());
		}
	}
	
	/*
	 * Received local associations are merged into the episodic buffer.
	 * Then they are sent to PAM.
	 * 接收到的本地关联将合并到情节缓冲区中。 *然后将它们发送到PAM
	 */
	@Override
	public void receiveLocalAssociation(NodeStructure association) {
		if(containsSubmodule(ModuleName.EpisodicBuffer)){
			WorkspaceBuffer buffer = (WorkspaceBuffer) getSubmodule(ModuleName.EpisodicBuffer);
			buffer.addBufferContent((WorkspaceContent) association);
			for (WorkspaceListener listener : workspaceListeners) {
				listener.receiveWorkspaceContent(ModuleName.EpisodicBuffer,
						buffer.getBufferContent(null));
			}
		}else{
			logger.log(Level.WARNING, "Received a Local assocation but Workspace does not have an episodic buffer.", TaskManager.getCurrentTick());
		}
	}
	
	/*
	 * Implementation of the PamListener interface.  Adds newPercept to the
	 * the perceptualBuffer.
	 * 收到知觉 PamListener接口的实现。向* perceptualBuffer添加newPercept
	 */
	@Override
	public void receivePercept(NodeStructure newPercept) {
		if(containsSubmodule(ModuleName.PerceptualBuffer)){
			WorkspaceBuffer buffer = (WorkspaceBuffer) getSubmodule(ModuleName.PerceptualBuffer);
			buffer.addBufferContent((WorkspaceContent) newPercept);
		}else{
			logger.log(Level.WARNING, "Received a percept but Workspace does not have a perceptual buffer.", TaskManager.getCurrentTick());
		}
	}

	@Override
	public void receivePercept(Node n) {
		receivePercept(n,ModuleName.PerceptualBuffer);
	}

	@Override
	public void receivePercept(Node n,ModuleName name) {
		if(containsSubmodule(name)){
			WorkspaceBuffer buffer = (WorkspaceBuffer) getSubmodule(name);
			NodeStructure ns = buffer.getBufferContent(null);

			ns.setSceneTime(String.valueOf(TaskManager.getCurrentTick()));
			ns.setSceneSite(n.getLocation());
			// 复制数值，不是整个连地址拿过来
			ns.addDefaultNode(n);
			// todo buffer 图cast ，加入新数据，通知下游线程？避免轮询，但其实时刻有新数据，不通知也罢
		}else{
			logger.log(Level.WARNING, "Received a percept but Workspace does not have a perceptual buffer.", TaskManager.getCurrentTick());
		}
	}
	
	Nar nar = AgentStarter.nar;
	
	@Override
	public void receivePercept(Link l) {
		receivePercept(l,ModuleName.PerceptualBuffer);
	}

	@Override
	public void receivePercept(Link l, ModuleName name) {
		if(containsSubmodule(name)){
			// 原lida部分
			WorkspaceBuffer buffer = (WorkspaceBuffer) getSubmodule(name);
			NodeStructure ns = buffer.getBufferContent(null);
			ns.addDefaultLink(l);

			// 内含直接推理，无需再额外新建任务
			maketask((LinkImpl) l, name, ns);

//			if (punctuation == '!') {
//				nar.cycles(1);
//			}
//			NarCycleTask narCycleTask = new NarCycleTask();
//			taskSpawner.addTask(narCycleTask);
//			nar.addInput("");

		}else{
			logger.log(Level.WARNING, "Received a percept but Workspace does not have a perceptual buffer.", TaskManager.getCurrentTick());
		}
	}

	private void maketask(LinkImpl l, ModuleName name, NodeStructure ns) {
//		Memory memory = (Memory)ns;
//		memory = nar.memory; // 总无意识集合

		Memory memory = null;
		String target = "both";
		char punctuation = '.';
		switch (name.name){
			case "GoalGraph" :
//					if(!l.getCategory().getName().equals("动作")){
//						punctuation = '!';
//					}
				// ！！动机类型只在体感、nars派生、目标处理中添加
				memory = (Memory)ns;
				target = "goal";
				break;
			default:
//				target = "belief";
				memory = nar.memory;
				break;
		}

		Term term = getLinkTerm(l);

		if (term != null) {
			try {
				// todo 不能直接字符串转化，细分部分如pam点边需要保留原属性
				term = narsese.parseTerm(term.toString());
			} catch (Parser.InvalidInputException e) {
				throw new RuntimeException(e);
			}

			doMakeTask(punctuation, memory, term, target);
			// 再次激活？
	//		addCPTo(l, headcpstr, memory, tailcpstr);
		}
	}

	@Nullable
	public static Term getLinkTerm(LinkImpl l) {
		Term term = null;
		List<String> exclude = Arrays.asList(new String[]{"属性", "心理计划","具身计划",
				"返回赋值", "整体赋值", "子类", "媒介", "赋值", "情绪","时间","顺发","关键帧",
				"具象","抽象","时态","名字","对象","实例","手段","表示","break","拆分"});
		List<String> exclude3 = Arrays.asList(new String[]{"isa","顺承","蕴含"});

		// 假如link的首尾节点的“cpstr”属性存在且不为空，则新建一个任务，任务内容为“cpstr”属性的值
//		String headcpstr = (String) l.getSource().getProperties().get("cpstr");
//		String tailcpstr = (String) l.getSink().getProperties().get("cpstr");

		String pcate = l.getCategory().getName();

		// 单三元组都是小继承,需要细分
		switch (pcate) {
			case "isa": //202311，51条
				if (l.term[0].toString().equals("{SELF}")){
					// <{SELF} --> [goodHealth]>! :|: <{SELF} --> [satisfied]>! :|:
					// 体感不纳入继承范畴
					break;
				}
				// <ss4 --> ss1>.
				term = Inheritance.make(l.term[0], l.term[2]);
				break;
			case "顺承": //202311，53条
				if (l.term[0].TNname.equals("(*,(*,听到1,$问题),<$问题 --> 问题>)"))
					System.out.println(l.term[0].TNname);

				// 允许单词项，不必复合词，如<ss11 =/> ss22>，<(*,$1,#2) =/> (*,$1,sH16)>.<(*,food,ss2) =/> (*,food,ss1)>
				term = Implication.make(l.term[0], l.term[2], TemporalRules.ORDER_FORWARD);
				break;
			case "蕴含": //202311，35条
				// 语句对等，如<ss11 <=> ss22>
				term = Equivalence.make(l.term[0], l.term[2], TemporalRules.ORDER_NONE);
				break;
			case "对等":
				// <ss1 <=> ss2>.
				term = Equivalence.make(l.term[0], l.term[2], TemporalRules.ORDER_NONE);
				break;
			case "相似":
				// <ss1 <-> ss2>.
				term = Similarity.make(l.term[0], l.term[2]);
//				if(term.toString().contains("$")){
//					System.out.println("--wki----相似--变量-----：" + term.toString());
//				}
				break;
			default:
				// 如果pcate不在列表里则跳过。欲求、计划等转顺承时序=关联奖惩
				if(exclude.contains(pcate)) {
					// 形式：<(*,ss1,ss2) --> 时序>,product=集合=可以多个元素
//					term = Inheritance.make(new Product(l.term[0], l.term[2]),l.term[1]);

//				}else if(tailcpstr != null && (tailcpstr.contains("-->") || tailcpstr.contains("<=>") || tailcpstr.contains("<->")
//						|| tailcpstr.contains("=/>") || tailcpstr.contains("=\\>") || tailcpstr.contains("=|>"))) {
//					nar.addInputTo(tailcpstr + ".", memory);
//					addCPTo(l, headcpstr, memory, tailcpstr);
//					return;
				}
				break;
		}
		return term;
	}

	private void addCPTo(LinkImpl l, String headcpstr, Memory memory, String tailcpstr) {
		String text = "";
		if(headcpstr != null && !headcpstr.isEmpty()) {
			text = "<" + headcpstr + " <-> " + l.getSource().getTNname() + ">.";
			nar.addInputTo(text, memory);
		}
		if(tailcpstr != null && !tailcpstr.isEmpty()) {
			text = "<" + tailcpstr + " <-> " + l.getSink().getTNname() + ">.";
			nar.addInputTo(text, memory);
		}
	}

	private void doMakeTask(char punctuation, Memory memory, Term term, String target) {
		if (term instanceof CompoundTerm) {
			int num = 0;
			Term[] terms = ((CompoundTerm)term).term;
			for (Term term1 : terms) {
				if(term1 instanceof CompoundTerm){
					num++;
				}
			}
			// 都是单项，且有变量单项，没意义？实例化太多，至少两个前提，才能推理，特别是动机？如：$命令--》好状态。
			// 这个可以，至少有一个限定：（回应，$问题）--》好状态。命令本身也是限定？
			if (num == 0 && term.hasVar()) return;

			// 内部时间
			final Stamp stamp = new Stamp(nar.time() , null, memory.newStampSerial(),
					nar.narParameters.DURATION);
			// 转nars的task，进入数值计算和推理
			final Sentence sentence = new Sentence(term, punctuation,
					new TruthValue((float) 1.0, 0.9, nar.narParameters), stamp);
			// todo 任务预算，要根据动机激活激励等实时算，在bag里排序
			final BudgetValue budget = new BudgetValue(0.8f, 0.5f, 1, nar.narParameters);
			Task task = new Task(sentence, budget, Task.EnumType.INPUT);

			// 三种新增任务方案
			// 1、外来语句输入，符号语句没有各种图结构属性
//			nar.addInput("<苹果 --> [红色]>. :|:");
//			nar.cycles(1);
			// 2、通过addnewtask方法，本身属于cycles一部分
			memory.addNewTask(task, "lida");
			if (target.equals("goal")) {
                // 处理派生的目标，如变量替换后加入的，和nars原派生方法
				memory.doGBuffer("goal");
                // 20250508，直接处理，缩短执行路径，免得扩散激活等待的线程太多，或新建插入线程（组）
                // 与上面不同，这是扩散，上面是派生
                memory.localInference(task, memory.narParameters, nar, memory);
			}else {
				for (int i = 0; i < 3; i++) {
//				memory.doAll(nar);
					memory.doGBuffer("belief");
					memory.doReason(nar);
				}
			}

			// 3、直接推理，没有缓冲等部分
//			nar.memory.localInference(task,AgentStarter.AgentStarter.nar.narParameters,AgentStarter.nar);
		}
	}

	@Override
	public Object getModuleContent(Object... params) {
		return null;
	}

	@Override
	public void learn(Coalition coalition) {
		// Not applicable
	}

	/** 
	 * Should do nothing, submodules' decayModule method will be called 
     * in FrameworkModuleImpl#taskManagerDecayModule.
	 * @see edu.memphis.ccrg.lida.framework.FrameworkModule#decayModule(long)
	 */
	@Override
	public void decayModule(long ticks) {
	}
	
}
